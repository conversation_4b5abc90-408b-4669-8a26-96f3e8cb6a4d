package com.linkcircle.boss.module.billing.web.data.service;

import com.linkcircle.boss.module.billing.web.data.model.vo.BillingCycleResultVO;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;

/**
 * <AUTHOR>
 * @date 2025-07-08 16:00
 * @description 客户账户 出账周期计算服务接口
 */
public interface BillingCycleCalculateService {

    /**
     * 计算账户出账周期信息
     * 根据账户的出账周期配置和当前时间，计算是否允许出账以及出账时间范围
     *
     * @param accountInfo 账户信息
     * @return 出账周期计算结果，包含是否允许出账、出账时间范围等信息
     */
    BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo);

    /**
     * 计算账户出账周期信息（支持计算上一周期）
     * 根据账户的出账周期配置和当前时间，计算是否允许出账以及出账时间范围
     *
     * @param accountInfo            账户信息
     * @param calculatePreviousCycle true-计算上一周期，false-计算当前周期
     * @return 出账周期计算结果，包含是否允许出账、出账时间范围等信息
     * @deprecated 使用 {@link #calculateBillingCycle(CustomerAccountVO, int)} 替代
     */
    BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo, boolean calculatePreviousCycle);

    /**
     * 计算账户出账周期信息（支持多周期回溯）
     * 根据账户的出账周期配置和当前时间，计算是否允许出账以及出账时间范围
     *
     * @param accountInfo        账户信息
     * @param previousCycleCount 回溯周期数量，0-当前周期，1-上一周期，2-上两个周期，以此类推
     * @return 出账周期计算结果，包含是否允许出账、出账时间范围等信息
     */
    BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo, int previousCycleCount);

}
