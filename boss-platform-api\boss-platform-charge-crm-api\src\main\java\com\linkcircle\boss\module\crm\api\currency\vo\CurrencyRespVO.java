package com.linkcircle.boss.module.crm.api.currency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zyuan
 * @data : 2025-06-25
 */
@Schema(description = "管理后台 - 货币下拉列表 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CurrencyRespVO {

    /**
     * 三字母货币代码（ISO 4217标准，主键）
     */
    @Schema(description = "货币代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "AED")
    private String currencyCode;

    /**
     * 货币全称（英文）
     */
    @Schema(description = "货币全称（英文）", requiredMode = Schema.RequiredMode.REQUIRED, example = "阿联酋迪拉姆")
    private String currencyName;

    /**
     * 货币符号（如¥/$/€）
     */
    @Schema(description = "货币符号（如¥/$/€）", requiredMode = Schema.RequiredMode.REQUIRED, example = "¥")
    private String symbol;
}
