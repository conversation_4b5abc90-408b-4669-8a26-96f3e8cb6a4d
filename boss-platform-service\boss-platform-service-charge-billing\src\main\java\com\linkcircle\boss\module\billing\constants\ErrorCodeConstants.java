package com.linkcircle.boss.module.billing.constants;

import com.linkcircle.boss.framework.common.exception.ErrorCode;

/**
 * <AUTHOR>
 * @date 2025-06-05 9:54
 * @description 错误码 <p>
 * charge 系统，使用 1-003-000-000 段
 * 1-003_业务模块_序号
 */
public interface ErrorCodeConstants {

    ErrorCode REQUEST_FAIL = new ErrorCode(1_003_000_001, "请求失败");

    // ========== 鉴权相关错误码 1-003-001-xxx ==========
    ErrorCode AUTH_FAILED = new ErrorCode(1_003_001_000, "鉴权失败");
    ErrorCode AUTH_PARAM_MISSING = new ErrorCode(1_003_001_001, "参数缺失: {}");
    ErrorCode AUTH_SIGN_INVALID = new ErrorCode(1_003_001_002, "签名验证失败");
    ErrorCode AUTH_TIMESTAMP_EXPIRED = new ErrorCode(1_003_001_003, "请求时间戳过期");
    ErrorCode AUTH_APPID_MISSING = new ErrorCode(1_003_001_004, "X-AppId header缺失");
    ErrorCode AUTH_APPID_INVALID = new ErrorCode(1_003_001_005, "AppId无效或已禁用");
    ErrorCode AUTH_SIGN_EMPTY = new ErrorCode(1_003_001_006, "签名不能为空");
    ErrorCode AUTH_TIMESTAMP_EMPTY = new ErrorCode(1_003_001_007, "请求时间戳不能为空");
    ErrorCode AUTH_TIMESTAMP_FORMAT_ERROR = new ErrorCode(1_003_001_008, "请求时间戳格式错误");

    // ========== 账单明细相关错误码 1-003-002-xxx ==========
    ErrorCode BILL_DETAIL_EXISTS = new ErrorCode(1_003_002_001, "账单明细已存在");
    ErrorCode BILL_DETAIL_CREATE_FAILED = new ErrorCode(1_003_002_002, "账单明细创建失败");
    ErrorCode BILL_DETAIL_SYSTEM_ERROR = new ErrorCode(1_003_002_003, "系统异常，请稍后重试");
    ErrorCode REQUEST_ID_DUPLICATE = new ErrorCode(1_003_002_004, "请求ID重复: {}");
    ErrorCode BUSINESS_ID_DUPLICATE = new ErrorCode(1_003_002_005, "业务ID重复: {}");
    ErrorCode ACCOUNT_SUBSCRIPTION_NOT_FOUND = new ErrorCode(1_003_002_006, "账户订阅信息不存在: {}");
    ErrorCode MQ_SEND_FAILED = new ErrorCode(1_003_002_007, "消息队列发送失败");

    // ========== 计费处理相关错误码 1-003-003-xxx ==========
    ErrorCode SUBSCRIPTION_LIST_EMPTY = new ErrorCode(1_003_003_001, "账户订阅列表为空");
    ErrorCode SERVICE_SUBSCRIPTION_NOT_FOUND = new ErrorCode(1_003_003_002, "未找到匹配的服务订阅: {}");
    ErrorCode SERVICE_CONFIG_NOT_FOUND = new ErrorCode(1_003_003_003, "未找到服务配置: {}");
    ErrorCode BILLING_CALCULATION_FAILED = new ErrorCode(1_003_003_004, "计费计算失败: {}");
    ErrorCode BILL_SAVE_FAILED = new ErrorCode(1_003_003_005, "账单保存失败");
    ErrorCode DELAY_MQ_SEND_FAILED = new ErrorCode(1_003_003_006, "延迟重试MQ发送失败");
    ErrorCode WALLET_MQ_SEND_FAILED = new ErrorCode(1_003_003_007, "钱包扣款MQ发送失败");
    ErrorCode LOCAL_FILE_WRITE_FAILED = new ErrorCode(1_003_003_008, "本地文件写入失败");
    ErrorCode PREPAID_BILL_PROCESS_FAILED = new ErrorCode(1_003_003_009, "预付费账单处理失败: {}");
    ErrorCode POSTPAID_SERVICE_BILL_PROCESS_FAILED = new ErrorCode(1_003_003_010, "后付费服务账单处理失败: {}");
    ErrorCode POSTPAID_PRODUCT_BILL_PROCESS_FAILED = new ErrorCode(1_003_003_011, "后付费产品账单处理失败: {}");
    ErrorCode COST_BILL_PROCESS_FAILED = new ErrorCode(1_003_003_012, "成本账单处理失败: {}");

    // ========== 费率配置相关错误码 1-003-004-xxx ==========
    ErrorCode RATE_CONFIG_NOT_FOUND = new ErrorCode(1_003_004_001, "费率配置不存在");
    ErrorCode RATE_CONFIG_CURRENCY_NOT_FOUND = new ErrorCode(1_003_004_002, "未找到匹配币种 {} 的费率配置");

    // ========== 出账定时任务相关错误码 1-003-005-xxx ==========
    ErrorCode INCOME_BILL_CALCULATION_FAILED = new ErrorCode(1_003_005_001, "收入出账计算原价失败: {}");
    ErrorCode COST_BILL_CALCULATION_FAILED = new ErrorCode(1_003_005_002, "成本出账计算原价失败: {}");

    // ========== 费率配置查询失败相关错误码 1-003-006-xxx ==========
    ErrorCode NOT_FOUND_SUBSCRIPTION = new ErrorCode(1_003_006_001, "未找到订阅信息: {}");
    ErrorCode NOT_FOUND_MATCHING_SERVICE_SUBSCRIPTION = new ErrorCode(1_003_006_002, "未找到匹配的服务订阅信息: {}");
    ErrorCode NOT_FOUND_CUSTOMER_ACCOUNT = new ErrorCode(1_003_006_003, "未找到客户账户信息: {}");

    ErrorCode NOT_FOUND_PURCHASE = new ErrorCode(1_003_006_004, "未找到资源采购信息: {}");
    ErrorCode NOT_FOUND_MATCHING_SERVICE_PURCHASE = new ErrorCode(1_003_006_005, "未找到匹配的资源采购信息: {}");
    ErrorCode NOT_FOUND_SUPPLIER_ACCOUNT = new ErrorCode(1_003_006_006, "未找到供应商账户信息: {}");
    ErrorCode NOT_BIND_SCALE_TABLE = new ErrorCode(1_003_006_007, "{}, 订阅: {}, 服务: {}, 未绑定量表");

}
