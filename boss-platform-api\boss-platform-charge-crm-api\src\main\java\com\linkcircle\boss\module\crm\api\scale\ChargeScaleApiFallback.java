package com.linkcircle.boss.module.crm.api.scale;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.scale.vo.ChargeScaleColumnInfoRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:57
 */
@Slf4j
@Component
public class ChargeScaleApiFallback implements FallbackFactory<ChargeScaleApi> {
    @Override
    public ChargeScaleApi create(Throwable cause) {
        return new ChargeScaleApi() {
            @Override
            public CommonResult<List<ChargeScaleColumnInfoRespVO>> getScaleBusinessColumnInfoList(Long tableId) {
                log.error("调用服务-量表API失败，tableId: {} 异常信息: ", tableId, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
