package com.linkcircle.boss.module.billing.web.data.service.impl;

import com.linkcircle.boss.module.billing.web.data.model.vo.BillingCycleResultVO;
import com.linkcircle.boss.module.billing.web.data.service.BillingCycleCalculateService;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.enums.BillingCycleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.github.cloud.framework.common.constant.TimeConstants;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025-07-29 17:00
 * @description 出账周期计算服务上一周期功能测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class BillingCycleCalculateServicePreviousCycleTest {

    @Resource
    private BillingCycleCalculateService billingCycleCalculateService;

    /**
     * 测试月结出账上一周期功能
     */
    @Test
    void testCalculateMonthlyBillingPreviousCycle() {
        System.out.println("\n=== 测试月结出账上一周期功能 ===");
        
        // 构造测试账户信息
        CustomerAccountVO accountInfo = createTestAccount(BillingCycleTypeEnum.MONTHLY.getType(), 1);
        
        // 测试当前周期
        BillingCycleResultVO currentResult = billingCycleCalculateService.calculateBillingCycle(accountInfo, false);
        
        // 测试上一周期
        BillingCycleResultVO previousResult = billingCycleCalculateService.calculateBillingCycle(accountInfo, true);
        
        System.out.println("当前周期结果:");
        printBillingCycleResult(currentResult);
        
        System.out.println("上一周期结果:");
        printBillingCycleResult(previousResult);
        
        // 验证结果
        assertNotNull(currentResult, "当前周期结果不应为空");
        assertNotNull(previousResult, "上一周期结果不应为空");
        
        // 如果当前周期允许出账，上一周期也应该有结果
        if (currentResult.getAllowBilling()) {
            assertTrue(previousResult.getAllowBilling(), "上一周期应该允许出账");
            assertNotNull(previousResult.getBillingStartTime(), "上一周期开始时间不应为空");
            assertNotNull(previousResult.getBillingEndTime(), "上一周期结束时间不应为空");
            
            // 验证上一周期的时间应该早于当前周期
            assertTrue(previousResult.getBillingStartTime() < currentResult.getBillingStartTime(),
                    "上一周期开始时间应该早于当前周期");
            assertTrue(previousResult.getBillingEndTime() < currentResult.getBillingEndTime(),
                    "上一周期结束时间应该早于当前周期");
        }
    }

    /**
     * 测试周结出账上一周期功能
     */
    @Test
    void testCalculateWeeklyBillingPreviousCycle() {
        System.out.println("\n=== 测试周结出账上一周期功能 ===");
        
        // 构造测试账户信息（周一出账）
        CustomerAccountVO accountInfo = createTestAccount(BillingCycleTypeEnum.WEEKLY.getType(), 1);
        
        // 测试当前周期
        BillingCycleResultVO currentResult = billingCycleCalculateService.calculateBillingCycle(accountInfo, false);
        
        // 测试上一周期
        BillingCycleResultVO previousResult = billingCycleCalculateService.calculateBillingCycle(accountInfo, true);
        
        System.out.println("当前周期结果:");
        printBillingCycleResult(currentResult);
        
        System.out.println("上一周期结果:");
        printBillingCycleResult(previousResult);
        
        // 验证结果
        assertNotNull(currentResult, "当前周期结果不应为空");
        assertNotNull(previousResult, "上一周期结果不应为空");
    }

    /**
     * 测试向后兼容性：现有方法应该返回当前周期
     */
    @Test
    void testBackwardCompatibility() {
        System.out.println("\n=== 测试向后兼容性 ===");
        
        CustomerAccountVO accountInfo = createTestAccount(BillingCycleTypeEnum.MONTHLY.getType(), 1);
        
        // 使用原有方法（不带calculatePreviousCycle参数）
        BillingCycleResultVO oldMethodResult = billingCycleCalculateService.calculateBillingCycle(accountInfo);
        
        // 使用新方法，calculatePreviousCycle=false
        BillingCycleResultVO newMethodResult = billingCycleCalculateService.calculateBillingCycle(accountInfo, false);
        
        System.out.println("原有方法结果:");
        printBillingCycleResult(oldMethodResult);
        
        System.out.println("新方法(calculatePreviousCycle=false)结果:");
        printBillingCycleResult(newMethodResult);
        
        // 验证两个方法返回相同的结果
        assertEquals(oldMethodResult.getAllowBilling(), newMethodResult.getAllowBilling(), 
                "原有方法和新方法应该返回相同的允许出账状态");
        
        if (oldMethodResult.getAllowBilling() && newMethodResult.getAllowBilling()) {
            assertEquals(oldMethodResult.getBillingStartTime(), newMethodResult.getBillingStartTime(), 
                    "原有方法和新方法应该返回相同的出账开始时间");
            assertEquals(oldMethodResult.getBillingEndTime(), newMethodResult.getBillingEndTime(), 
                    "原有方法和新方法应该返回相同的出账结束时间");
            assertEquals(oldMethodResult.getBillingCycle(), newMethodResult.getBillingCycle(), 
                    "原有方法和新方法应该返回相同的出账周期标识");
        }
    }

    /**
     * 测试不同周期类型的上一周期功能
     */
    @Test
    void testAllCycleTypesPreviousCycle() {
        System.out.println("\n=== 测试所有周期类型的上一周期功能 ===");
        
        // 测试月结
        testCycleType(BillingCycleTypeEnum.MONTHLY.getType(), 1, "月结");
        
        // 测试周结
        testCycleType(BillingCycleTypeEnum.WEEKLY.getType(), 1, "周结");
        
        // 测试季结
        testCycleType(BillingCycleTypeEnum.QUARTERLY.getType(), 1, "季结");
        
        // 测试年结
        testCycleType(BillingCycleTypeEnum.YEARLY.getType(), 1, "年结");
    }

    /**
     * 测试特定周期类型
     */
    private void testCycleType(Integer cycleType, Integer billingDay, String typeName) {
        System.out.println("\n--- 测试" + typeName + "出账上一周期 ---");
        
        CustomerAccountVO accountInfo = createTestAccount(cycleType, billingDay);
        
        BillingCycleResultVO previousResult = billingCycleCalculateService.calculateBillingCycle(accountInfo, true);
        
        System.out.println(typeName + "上一周期结果:");
        printBillingCycleResult(previousResult);
        
        assertNotNull(previousResult, typeName + "上一周期结果不应为空");
        assertEquals(accountInfo.getAccountId(), previousResult.getAccountId(), "账户ID应该匹配");
        assertEquals(cycleType, previousResult.getBillingCycleType(), "出账周期类型应该匹配");
    }

    /**
     * 创建测试账户信息
     */
    private CustomerAccountVO createTestAccount(Integer billingCycleType, Integer billingDay) {
        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(12345L);
        accountInfo.setBillingCycle(billingCycleType);
        accountInfo.setBillingDay(billingDay);
        accountInfo.setTimezone(TimeConstants.TIMEZONE_SHANGHAI);
        accountInfo.setCurrency("CNY");
        return accountInfo;
    }

    /**
     * 打印出账周期计算结果
     */
    private void printBillingCycleResult(BillingCycleResultVO result) {
        if (result == null) {
            System.out.println("结果为空");
            return;
        }
        
        System.out.println("账户ID: " + result.getAccountId());
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("出账周期类型: " + result.getBillingCycleType());
        System.out.println("出账日: " + result.getBillingDay());
        
        if (result.getAllowBilling()) {
            System.out.println("出账开始时间: " + result.getBillingStartTime());
            System.out.println("出账结束时间: " + result.getBillingEndTime());
            System.out.println("出账周期: " + result.getBillingCycle());
            System.out.println("时区: " + result.getTimezone());
            System.out.println("币种: " + result.getCurrency());
            
            if (result.getDescription() != null) {
                System.out.println("描述: " + result.getDescription());
            }
        } else {
            if (result.getDescription() != null) {
                System.out.println("不允许出账原因: " + result.getDescription());
            }
        }
        System.out.println("---");
    }
}
