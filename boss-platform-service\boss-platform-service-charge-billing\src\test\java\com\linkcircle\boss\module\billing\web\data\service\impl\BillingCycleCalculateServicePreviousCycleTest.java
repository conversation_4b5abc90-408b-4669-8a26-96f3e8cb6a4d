package com.linkcircle.boss.module.billing.web.data.service.impl;

import com.linkcircle.boss.module.billing.web.data.model.vo.BillingCycleResultVO;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.enums.BillingCycleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.github.cloud.framework.common.constant.TimeConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.ZoneId;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;

/**
 * <AUTHOR>
 * @date 2025-07-29 17:00
 * @description 出账周期计算服务多周期回溯功能测试（使用Mockito）
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class BillingCycleCalculateServicePreviousCycleTest {

    @InjectMocks
    private BillingCycleCalculateServiceImpl billingCycleCalculateService;

    private CustomerAccountVO testAccount;

    @BeforeEach
    void setUp() {
        testAccount = createTestAccount(BillingCycleTypeEnum.MONTHLY.getType(), 30);
    }

    /**
     * 测试月结出账多周期回溯功能
     * 模拟当前日期为2025年7月30日（出账日），测试回溯不同数量的周期
     */
    @Test
    void testCalculateMonthlyBillingMultipleCycles() {
        System.out.println("\n=== 测试月结出账多周期回溯功能 ===");

        // Mock当前日期为2025年7月30日
        LocalDate mockCurrentDate = LocalDate.of(2025, 7, 30);

        try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
            mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(mockCurrentDate);

            // 测试当前周期 (previousCycleCount = 0)
            BillingCycleResultVO currentResult = billingCycleCalculateService.calculateBillingCycle(testAccount, 0);
            System.out.println("当前周期结果:");
            printBillingCycleResult(currentResult);

            // 测试上一周期 (previousCycleCount = 1)
            BillingCycleResultVO previousResult = billingCycleCalculateService.calculateBillingCycle(testAccount, 1);
            System.out.println("上一周期结果:");
            printBillingCycleResult(previousResult);

            // 测试上两个周期 (previousCycleCount = 2)
            BillingCycleResultVO twoMonthsAgoResult = billingCycleCalculateService.calculateBillingCycle(testAccount, 2);
            System.out.println("上两个周期结果:");
            printBillingCycleResult(twoMonthsAgoResult);

            // 验证结果
            assertNotNull(currentResult, "当前周期结果不应为空");
            assertNotNull(previousResult, "上一周期结果不应为空");
            assertNotNull(twoMonthsAgoResult, "上两个周期结果不应为空");

            // 验证时间顺序：上两个周期 < 上一周期 < 当前周期
            if (currentResult.getAllowBilling() && previousResult.getAllowBilling() && twoMonthsAgoResult.getAllowBilling()) {
                assertTrue(twoMonthsAgoResult.getBillingStartTime() < previousResult.getBillingStartTime(),
                        "上两个周期开始时间应该早于上一周期");
                assertTrue(previousResult.getBillingStartTime() < currentResult.getBillingStartTime(),
                        "上一周期开始时间应该早于当前周期");

                // 验证出账周期标识格式
                assertNotNull(currentResult.getBillingCycle(), "当前周期标识不应为空");
                assertNotNull(previousResult.getBillingCycle(), "上一周期标识不应为空");
                assertNotNull(twoMonthsAgoResult.getBillingCycle(), "上两个周期标识不应为空");
            }
        }
    }

    /**
     * 测试月末日期智能处理功能
     * 测试配置31号出账在7月份的处理情况
     */
    @Test
    void testMonthEndDateHandling() {
        System.out.println("\n=== 测试月末日期智能处理功能 ===");

        // 创建配置31号出账的账户
        CustomerAccountVO monthEndAccount = createTestAccount(BillingCycleTypeEnum.MONTHLY.getType(), 31);

        // Mock当前日期为2025年7月31日（7月有31天）
        LocalDate mockCurrentDate = LocalDate.of(2025, 7, 31);

        try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
            mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(mockCurrentDate);

            // 测试当前周期
            BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(monthEndAccount, 0);
            System.out.println("7月31日配置31号出账结果:");
            printBillingCycleResult(result);

            // 验证应该允许出账（因为31日是7月的最后一天）
            assertTrue(result.getAllowBilling(), "7月31日应该允许31号配置的出账");
            assertNotNull(result.getBillingStartTime(), "出账开始时间不应为空");
            assertNotNull(result.getBillingEndTime(), "出账结束时间不应为空");
        }

        // Mock当前日期为2025年7月30日
        LocalDate mockCurrentDate30 = LocalDate.of(2025, 7, 30);

        try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
            mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(mockCurrentDate30);

            // 测试当前周期
            BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(monthEndAccount, 0);
            System.out.println("7月30日配置31号出账结果:");
            printBillingCycleResult(result);

            // 验证不应该允许出账（因为30日不是7月的最后一天）
            assertFalse(result.getAllowBilling(), "7月30日不应该允许31号配置的出账");
        }
    }

    /**
     * 测试向后兼容性：现有方法应该返回当前周期
     */
    @Test
    void testBackwardCompatibility() {
        System.out.println("\n=== 测试向后兼容性 ===");

        // Mock当前日期为2025年7月30日
        LocalDate mockCurrentDate = LocalDate.of(2025, 7, 30);

        try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
            mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(mockCurrentDate);

            // 使用原有方法（不带参数）
            BillingCycleResultVO oldMethodResult = billingCycleCalculateService.calculateBillingCycle(testAccount);

            // 使用新方法，previousCycleCount=0
            BillingCycleResultVO newMethodResult = billingCycleCalculateService.calculateBillingCycle(testAccount, 0);

            // 使用已废弃的方法，calculatePreviousCycle=false
            BillingCycleResultVO deprecatedMethodResult = billingCycleCalculateService.calculateBillingCycle(testAccount, false);

            System.out.println("原有方法结果:");
            printBillingCycleResult(oldMethodResult);

            System.out.println("新方法(previousCycleCount=0)结果:");
            printBillingCycleResult(newMethodResult);

            System.out.println("已废弃方法(calculatePreviousCycle=false)结果:");
            printBillingCycleResult(deprecatedMethodResult);

            // 验证三个方法返回相同的结果
            assertEquals(oldMethodResult.getAllowBilling(), newMethodResult.getAllowBilling(),
                    "原有方法和新方法应该返回相同的允许出账状态");
            assertEquals(oldMethodResult.getAllowBilling(), deprecatedMethodResult.getAllowBilling(),
                    "原有方法和已废弃方法应该返回相同的允许出账状态");

            if (oldMethodResult.getAllowBilling() && newMethodResult.getAllowBilling()) {
                assertEquals(oldMethodResult.getBillingStartTime(), newMethodResult.getBillingStartTime(),
                        "原有方法和新方法应该返回相同的出账开始时间");
                assertEquals(oldMethodResult.getBillingEndTime(), newMethodResult.getBillingEndTime(),
                        "原有方法和新方法应该返回相同的出账结束时间");
                assertEquals(oldMethodResult.getBillingCycle(), newMethodResult.getBillingCycle(),
                        "原有方法和新方法应该返回相同的出账周期标识");
            }
        }
    }

    /**
     * 测试参数验证功能
     */
    @Test
    void testParameterValidation() {
        System.out.println("\n=== 测试参数验证功能 ===");

        // 测试空账户信息
        BillingCycleResultVO nullAccountResult = billingCycleCalculateService.calculateBillingCycle(null, 0);
        assertFalse(nullAccountResult.getAllowBilling(), "空账户信息应该不允许出账");
        assertNotNull(nullAccountResult.getDescription(), "错误信息不应为空");

        // 测试负数回溯周期
        BillingCycleResultVO negativeCountResult = billingCycleCalculateService.calculateBillingCycle(testAccount, -1);
        assertFalse(negativeCountResult.getAllowBilling(), "负数回溯周期应该不允许出账");
        assertNotNull(negativeCountResult.getDescription(), "错误信息不应为空");

        // 测试无效的出账日配置
        CustomerAccountVO invalidDayAccount = createTestAccount(BillingCycleTypeEnum.MONTHLY.getType(), 32);
        BillingCycleResultVO invalidDayResult = billingCycleCalculateService.calculateBillingCycle(invalidDayAccount, 0);
        assertFalse(invalidDayResult.getAllowBilling(), "无效出账日应该不允许出账");
        assertNotNull(invalidDayResult.getDescription(), "错误信息不应为空");

        System.out.println("参数验证测试完成");
    }

    /**
     * 测试所有周期类型的多周期回溯功能
     */
    @Test
    void testAllCycleTypesMultipleCycles() {
        System.out.println("\n=== 测试所有周期类型的多周期回溯功能 ===");

        // Mock当前日期为合适的出账日
        LocalDate mockCurrentDate = LocalDate.of(2025, 7, 30);

        try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
            mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(mockCurrentDate);

            // 测试月结
            testCycleTypeMultiple(BillingCycleTypeEnum.MONTHLY.getType(), 30, "月结");

            // 测试周结（周三，2025年7月30日是周三）
            testCycleTypeMultiple(BillingCycleTypeEnum.WEEKLY.getType(), 3, "周结");

            // 测试季结（2025年7月30日是第三季度第30天）
            testCycleTypeMultiple(BillingCycleTypeEnum.QUARTERLY.getType(), 30, "季结");

            // 测试年结（2025年7月30日是年第211天）
            testCycleTypeMultiple(BillingCycleTypeEnum.YEARLY.getType(), 211, "年结");
        }
    }

    /**
     * 测试特定周期类型的多周期回溯
     */
    private void testCycleTypeMultiple(Integer cycleType, Integer billingDay, String typeName) {
        System.out.println("\n--- 测试" + typeName + "出账多周期回溯 ---");

        CustomerAccountVO accountInfo = createTestAccount(cycleType, billingDay);

        // 测试当前周期、上一周期、上两个周期
        for (int i = 0; i <= 2; i++) {
            BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo, i);

            System.out.println(typeName + "回溯" + i + "个周期结果:");
            printBillingCycleResult(result);

            assertNotNull(result, typeName + "回溯" + i + "个周期结果不应为空");
            assertEquals(accountInfo.getAccountId(), result.getAccountId(), "账户ID应该匹配");
            assertEquals(cycleType, result.getBillingCycleType(), "出账周期类型应该匹配");
        }
    }

    /**
     * 创建测试账户信息
     */
    private CustomerAccountVO createTestAccount(Integer billingCycleType, Integer billingDay) {
        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(12345L);
        accountInfo.setBillingCycle(billingCycleType);
        accountInfo.setBillingDay(billingDay);
        accountInfo.setTimezone(TimeConstants.TIMEZONE_SHANGHAI);
        accountInfo.setCurrency("CNY");
        return accountInfo;
    }

    /**
     * 打印出账周期计算结果
     */
    private void printBillingCycleResult(BillingCycleResultVO result) {
        if (result == null) {
            System.out.println("结果为空");
            return;
        }

        System.out.println("账户ID: " + result.getAccountId());
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("出账周期类型: " + result.getBillingCycleType());
        System.out.println("出账日: " + result.getBillingDay());

        if (result.getAllowBilling()) {
            System.out.println("出账开始时间: " + result.getBillingStartTime());
            System.out.println("出账结束时间: " + result.getBillingEndTime());
            System.out.println("出账周期: " + result.getBillingCycle());
            System.out.println("时区: " + result.getTimezone());
            System.out.println("币种: " + result.getCurrency());

            if (result.getDescription() != null) {
                System.out.println("描述: " + result.getDescription());
            }
        } else {
            if (result.getDescription() != null) {
                System.out.println("不允许出账原因: " + result.getDescription());
            }
        }
        System.out.println("---");
    }
}
