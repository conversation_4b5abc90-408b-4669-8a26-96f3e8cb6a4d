package com.linkcircle.boss.module.charge.crm.web.currency.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.web.context.LoginUser;
import com.linkcircle.boss.framework.web.core.util.WebFrameworkUtils;
import com.linkcircle.boss.module.charge.crm.web.currency.mapper.ChargeCurrencyMapper;
import com.linkcircle.boss.module.charge.crm.web.currency.model.dto.ChargeCurrencyPageQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.currency.model.dto.CurrencySaveReqDTO;
import com.linkcircle.boss.module.charge.crm.web.currency.model.entity.ChargeCurrencyDO;
import com.linkcircle.boss.module.charge.crm.web.currency.model.vo.ChargeCurrencyRespVO;
import com.linkcircle.boss.module.charge.crm.web.currency.model.vo.CurrencySelectRespVO;
import com.linkcircle.boss.module.charge.crm.web.currency.service.ChargeCurrencyService;
import com.linkcircle.boss.module.crm.api.currency.vo.CurrencyRespVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zyuan
 * @data : 2025-06-25
 */
@Service
@Slf4j
public class ChargeCurrencyServiceImpl implements ChargeCurrencyService {

    @Resource
    private ChargeCurrencyMapper currencyMapper;

    @Override
    public PageResult<ChargeCurrencyRespVO> getChargeCurrencyPage(ChargeCurrencyPageQueryDTO queryDTO) {
        Page<?> page = MyBatisUtils.buildPage(queryDTO);
        List<ChargeCurrencyRespVO> list = currencyMapper.queryByPage(page, queryDTO);
        return MyBatisUtils.convert2PageResult(page, list);
    }

    @Override
    public CommonResult<?> create(CurrencySaveReqDTO queryDTO) {
        ChargeCurrencyDO currencyDO = currencyMapper.selectOne(new LambdaQueryWrapper<ChargeCurrencyDO>()
                .eq(ChargeCurrencyDO::getCurrencyCode, queryDTO.getCurrencyCode())
                .eq(ChargeCurrencyDO::getDeleted, 0));
        if (currencyDO != null) {
            return CommonResult.error(500, "货币代码已经存在");
        }
        ChargeCurrencyDO entity = queryDTO.toEntity(null);
        LoginUser user = WebFrameworkUtils.getLoginUser();
        if (user != null) {
            entity.setCreator(user.getUsername());
        }
        entity.setCreateTime(System.currentTimeMillis());
        try {
            currencyMapper.insert(entity);
        } catch (DuplicateKeyException e) {

            // 货币代码存在 但是已经被逻辑删除
            log.info("货币代码存在但是已经被逻辑删除: {}", entity.getCurrencyCode());
            entity.setDeleted(false);
            currencyMapper.update(entity, new LambdaQueryWrapper<ChargeCurrencyDO>()
                    .eq(ChargeCurrencyDO::getCurrencyCode, entity.getCurrencyCode())
                    .eq(ChargeCurrencyDO::getDeleted, 1));
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> deleteById(String currencyCode) {
        ChargeCurrencyDO currencyDO = currencyMapper.selectOne(new LambdaQueryWrapper<ChargeCurrencyDO>()
                .eq(ChargeCurrencyDO::getCurrencyCode, currencyCode)
                .eq(ChargeCurrencyDO::getDeleted, 0));
        if (currencyDO == null) {
            return CommonResult.success();
        }
        currencyDO.setDeleted(true);
        LoginUser user = WebFrameworkUtils.getLoginUser();
        if (user != null) {
            currencyDO.setUpdater(user.getUsername());
        }
        currencyDO.setUpdateTime(System.currentTimeMillis());
        currencyMapper.updateById(currencyDO);
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> edit(CurrencySaveReqDTO queryDTO) {
        ChargeCurrencyDO currencyDO = currencyMapper.selectOne(new LambdaQueryWrapper<ChargeCurrencyDO>()
                .eq(ChargeCurrencyDO::getCurrencyCode, queryDTO.getCurrencyCode())
                .eq(ChargeCurrencyDO::getDeleted, 0));
        if (currencyDO == null) {
            return CommonResult.error(500, "货币代码不经存在");
        }
        ChargeCurrencyDO entity = queryDTO.toEntity(currencyDO);
        LoginUser user = WebFrameworkUtils.getLoginUser();
        if (user != null) {
            entity.setUpdater(user.getUsername());
        }
        entity.setUpdateTime(System.currentTimeMillis());
        currencyMapper.updateById(entity);
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<CurrencySelectRespVO>> currencySelect() {
        List<CurrencySelectRespVO> list = new ArrayList<>();
        List<ChargeCurrencyDO> dos = currencyMapper.selectList(new LambdaQueryWrapper<ChargeCurrencyDO>()
                .eq(ChargeCurrencyDO::getDeleted, 0));
        for (ChargeCurrencyDO aDo : dos) {
            CurrencySelectRespVO vo = new CurrencySelectRespVO();
            vo.setCurrencyCode(aDo.getCurrencyCode());
            vo.setCurrencyName(aDo.getCurrencyName());
            vo.setSymbol(aDo.getSymbol());
            list.add(vo);
        }
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<List<CurrencyRespVO>> listCurrency() {
        List<CurrencyRespVO> list = new ArrayList<>();
        List<ChargeCurrencyDO> dos = currencyMapper.selectList(new LambdaQueryWrapper<ChargeCurrencyDO>()
                .select(ChargeCurrencyDO::getCurrencyCode, ChargeCurrencyDO::getCurrencyName, ChargeCurrencyDO::getSymbol)
                .eq(ChargeCurrencyDO::getDeleted, 0)
        );
        for (ChargeCurrencyDO aDo : dos) {
            CurrencyRespVO vo = new CurrencyRespVO();
            vo.setCurrencyCode(aDo.getCurrencyCode());
            vo.setCurrencyName(aDo.getCurrencyName());
            vo.setSymbol(aDo.getSymbol());
            list.add(vo);
        }
        return CommonResult.success(list);
    }
}
