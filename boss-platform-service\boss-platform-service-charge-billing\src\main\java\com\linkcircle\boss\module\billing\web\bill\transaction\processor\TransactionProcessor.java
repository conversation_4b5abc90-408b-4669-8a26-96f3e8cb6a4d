package com.linkcircle.boss.module.billing.web.bill.transaction.processor;

import com.linkcircle.boss.module.billing.web.bill.transaction.enums.TransactionMessageTypeEnum;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionState;
import org.springframework.messaging.Message;

/**
 * <AUTHOR>
 * @date 2025-07-29 10:50
 * @description 事务处理器接口
 */
public interface TransactionProcessor {

    /**
     * 获取支持的事务消息类型
     *
     * @return 事务消息类型
     */
    TransactionMessageTypeEnum getSupportedType();

    /**
     * 执行本地事务
     *
     * @param msg 消息
     * @param arg 参数
     * @return 事务状态
     */
    RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg);

    /**
     * 检查本地事务状态（用于消息回查）
     *
     * @param msg 消息
     * @return 事务状态
     */
    RocketMQLocalTransactionState checkLocalTransaction(Message msg);
}
