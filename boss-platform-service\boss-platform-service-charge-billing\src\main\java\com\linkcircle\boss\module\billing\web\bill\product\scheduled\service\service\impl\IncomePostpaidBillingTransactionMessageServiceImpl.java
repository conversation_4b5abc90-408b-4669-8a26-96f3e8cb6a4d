package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.impl;

import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.BillCallbackDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.IncomePostpaidBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidBillingTransactionMessageService;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.TransactionSendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-07-07 18:30
 * @description RocketMQ事务消息实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomePostpaidBillingTransactionMessageServiceImpl implements IncomePostpaidBillingTransactionMessageService {

    private final RocketMQTemplate rocketMQTemplate;

    /**
     * 发送事务消息
     */
    @Override
    public boolean sendTransactionMessage(PostpaidProductServiceIncomeBillDO serviceIncomeBillDO,
                                          AccountSubscriptionsVO subscription,
                                          AccountSubscriptionsVO.Detail detail,
                                          AccountSubscriptionsVO.Product product,
                                          AccountSubscriptionsVO.Service service,
                                          CyclePeriodResultVO cyclePeriodResultVO) {
        // 构建消息
        IncomePostpaidBillingMessageDTO message = new IncomePostpaidBillingMessageDTO();
        message.setServiceIncomeBillDO(serviceIncomeBillDO);
        message.setSubscription(subscription);
        message.setDetail(detail);
        message.setProduct(product);
        message.setService(service);
        message.setCyclePeriodResultVO(cyclePeriodResultVO);

        BillCallbackDTO callbackDTO = BillCallbackDTO.builder()
                .billId(serviceIncomeBillDO.getBillId())
                .startTime(cyclePeriodResultVO.getCycleStartTime())
                .build();
        // 构建消息，添加消息头用于回查
        Message<IncomePostpaidBillingMessageDTO> msg = MessageBuilder
                .withPayload(message)
                .setHeader("extra", JsonUtils.toJsonString(callbackDTO))
                .build();

        String incomeServiceBillingTransactionMessageTopic = ChargeTopicUtils.getIncomeServiceBillingTransactionMessageTopic();
        TransactionSendResult sendResult = rocketMQTemplate.sendMessageInTransaction(incomeServiceBillingTransactionMessageTopic, msg, message);
        log.info("事务消息发送成功, billId: {}, sendResult: {}", serviceIncomeBillDO.getProductServiceBillId(), sendResult);
        return true;
    }

}
