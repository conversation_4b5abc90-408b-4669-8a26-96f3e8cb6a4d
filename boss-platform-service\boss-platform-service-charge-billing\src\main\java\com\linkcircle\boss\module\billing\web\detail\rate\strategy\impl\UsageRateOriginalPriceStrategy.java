package com.linkcircle.boss.module.billing.web.detail.rate.strategy.impl;

import cn.hutool.core.lang.Pair;
import com.linkcircle.boss.module.billing.api.rate.model.dto.FixedRateConfigDTO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.TierRateConfigDTO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-06-24 13:43
 * @description 按量费率原价计算策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = OriginalPriceRateTypeEnum.class, strategyType = RateTypeConstant.USAGE)
public class UsageRateOriginalPriceStrategy extends AbstractOriginalPriceStrategy implements IStrategy<OriginalPriceCalculateRequest, OriginalPriceCalculateResponse> {

    @Override
    public OriginalPriceCalculateResponse execute(OriginalPriceCalculateRequest request) {
        UsageBasedRateConfigDTO rateConfig = (UsageBasedRateConfigDTO) request.getRateConfig();

        convertUsageUnit(request, rateConfig.getMeasureUnit());
        OriginalPriceCalculateResponse response = calculateOriginalPrice(request, rateConfig);
        response.setChargeUsageCount(response.getMeasure().multiply(response.getChargeUnitCount()));

        inTrial(request, response);

        calculateTax(request, response);
        response.setRateConfig(rateConfig);
        response.setCouponList(request.getCouponList());

        response.setDiscountAmount(response.getOriginalPrice().subtract(response.getDiscountedPrice()));
        return response;
    }

    /**
     * 计算目录价和优惠价（按量计费，根据支付方式选择现金或积分，支持单位转换、向上取整、全额支付状态管理）
     *
     * @param request    原价计算请求
     * @param rateConfig 按量计费配置
     * @return 计算响应结果
     */
    private OriginalPriceCalculateResponse calculateOriginalPrice(OriginalPriceCalculateRequest request,
                                                                  UsageBasedRateConfigDTO rateConfig) {
        BigDecimal totalUsageWithCurrent = request.getTotalUsageWithCurrent();
        BigDecimal previousUsage = request.getPreviousUsage();
        BigDecimal currentUsage = request.getCurrentUsage();
        String currentUsageUnit = request.getCurrentUsageUnit();
        String measureUnit = rateConfig.getMeasureUnit();
        log.info("开始计算按量费率 - 之前累计用量: {} {}, 本次用量: {} {}, 总累计用量: {} {}",
                previousUsage, measureUnit, currentUsage, currentUsageUnit, totalUsageWithCurrent, measureUnit);

        // 单位转换：将本次用量转换为计量单位
        BigDecimal actualCurrentUsage = convertUnit(currentUsage, currentUsageUnit, measureUnit);
        log.info("单位转换后本次用量: {} {}", actualCurrentUsage, measureUnit);

        OriginalPriceCalculateResponse response = OriginalPriceCalculateResponse.success();
        response.setUsage(actualCurrentUsage);
        response.setUsageUnit(measureUnit);
        response.setMeasure(rateConfig.getMeasure());
        response.setMeasureUnit(rateConfig.getMeasureUnit());
        response.setMeasureCeil(rateConfig.getMeasureCeil());

        String priceModel = rateConfig.getPriceModel();

        if ("fixed".equals(priceModel)) {
            // 固定价格模式：按量计费的固定费率不需要累计用量，每次独立计算
            calculateFixedPrice(request, response, actualCurrentUsage, rateConfig);
            return response;
        }
        if ("level".equals(priceModel)) {
            // 阶梯价格模式：需要考虑累计用量和全额支付状态
            calculateLevelPrice(request, response, actualCurrentUsage, rateConfig);
            return response;
        }
        log.warn("未知的价格模型: {}", priceModel);
        response.setOriginalPrice(BigDecimal.ZERO);
        response.setDiscountedPrice(BigDecimal.ZERO);
        return response;
    }

    /**
     * 计算固定价格（按量计费，支持单位转换和向上取整）
     *
     * @param request      原价计算请求
     * @param currentUsage 本次用量（已转换为计量单位）
     * @param rateConfig   按量计费配置
     * @param response     响应对象
     */
    private void calculateFixedPrice(OriginalPriceCalculateRequest request,
                                     OriginalPriceCalculateResponse response,
                                     BigDecimal currentUsage,
                                     UsageBasedRateConfigDTO rateConfig) {
        String currentUsageUnit = request.getCurrentUsageUnit();
        String currency = request.getCurrency();
        Integer paymentOptions = request.getPaymentOptions();
        List<Coupon> couponList = request.getCouponList();
        List<FixedRateConfigDTO> fixRatePrices = rateConfig.getFixRatePrices();
        if (fixRatePrices == null || fixRatePrices.isEmpty()) {
            response.setOriginalPrice(BigDecimal.ZERO);
            response.setDiscountedPrice(BigDecimal.ZERO);
            return;
        }

        // 根据币种选择配置
        Optional<FixedRateConfigDTO> configOpt = selectFixedRateConfigByCurrency(fixRatePrices, currency);
        if (configOpt.isEmpty()) {
            log.warn("未找到匹配币种 {} 的固定费率配置", currency);
            response.setOriginalPrice(BigDecimal.ZERO);
            response.setDiscountedPrice(BigDecimal.ZERO);
            return;
        }
        FixedRateConfigDTO config = configOpt.get();

        // 计算计量单位数（支持向上取整）
        BigDecimal measure = rateConfig.getMeasure();
        Integer measureCeil = rateConfig.getMeasureCeil();
        BigDecimal units = roundUnit(currentUsage, measure, measureCeil);
        response.setChargeUnitCount(units);

        log.info("固定价格计算 - 本次用量: {}, 计量单位: {}, 向上取整: {}, 计量单位数: {}",
                currentUsage, measure, measureCeil, units);
        config.setChargeUnitCount(units);
        config.setIsHit(true);

        // 目录价计算
        BigDecimal originalUnitPrice = getUnitPrice(paymentOptions, config.getFixCharge(), config.getIntegralCharge());
        BigDecimal originalPrice = getTotalPrice(paymentOptions, units, originalUnitPrice);
        config.setOriginalUnitPrice(originalUnitPrice);
        config.setOriginalPrice(originalPrice);

        // 优惠价计算
        BigDecimal discountUnitPrice = calculateDiscountPrice(originalUnitPrice, couponList);
        BigDecimal discountPrice = getTotalPrice(paymentOptions, units, discountUnitPrice);
        config.setDiscountedUnitPrice(discountUnitPrice);
        config.setDiscountedPrice(discountPrice);

        log.info("固定价格计算结果 - 计量单位数: {}, 目录价: {}, 优惠价: {}", units, originalPrice, discountPrice);
        config.setUsage(currentUsage);
        config.setUsageUnit(currentUsageUnit);

        response.setOriginalPrice(originalPrice);
        response.setDiscountedPrice(discountPrice);
    }

    /**
     * 计算阶梯价格（根据支付方式选择现金或积分）
     * 参考套餐计费的套餐外阶梯逻辑，支持累计用量和全额支付状态管理
     *
     * @param request      原价计算请求
     * @param currentUsage 本次用量
     * @param rateConfig   费率配置列表
     * @param response     响应对象
     */
    private void calculateLevelPrice(OriginalPriceCalculateRequest request,
                                     OriginalPriceCalculateResponse response,
                                     BigDecimal currentUsage,
                                     UsageBasedRateConfigDTO rateConfig) {
        BigDecimal totalUsageWithCurrent = request.getTotalUsageWithCurrent();
        BigDecimal previousUsage = request.getPreviousUsage();
        String currency = request.getCurrency();
        List<UsageBasedRateConfigDTO.StairRateConfigDTO> levelConfigs = rateConfig.getStairRatePrices();
        if (levelConfigs == null || levelConfigs.isEmpty()) {
            response.setOriginalPrice(BigDecimal.ZERO);
            response.setDiscountedPrice(BigDecimal.ZERO);
            return;
        }

        // 根据币种选择配置
        Optional<UsageBasedRateConfigDTO.StairRateConfigDTO> configOpt = selectLevelRateConfigByCurrency(levelConfigs, currency);
        if (configOpt.isEmpty()) {
            log.warn("未找到匹配币种 {} 的阶梯费率配置", currency);
            response.setOriginalPrice(BigDecimal.ZERO);
            response.setDiscountedPrice(BigDecimal.ZERO);
            return;
        }
        UsageBasedRateConfigDTO.StairRateConfigDTO config = configOpt.get();

        List<TierRateConfigDTO.TierPriceDTO> tierPrices = config.getTierPrices();

        if (tierPrices == null || tierPrices.isEmpty()) {
            response.setOriginalPrice(BigDecimal.ZERO);
            response.setDiscountedPrice(BigDecimal.ZERO);
            return;
        }

        String measureUnit = rateConfig.getMeasureUnit();
        BigDecimal measure = rateConfig.getMeasure();
        Integer measureCeil = rateConfig.getMeasureCeil();

        log.info("开始计算按量计费阶梯费用 - 之前累计用量: {} {}, 本次用量: {} {}, 总累计用量: {} {}",
                previousUsage, measureUnit, currentUsage, measureUnit, totalUsageWithCurrent, measureUnit);

        BigDecimal originalPriceTotal = BigDecimal.ZERO;
        BigDecimal discountPriceTotal = BigDecimal.ZERO;
        BigDecimal processedUsage = previousUsage;
        BigDecimal remainingUsage = currentUsage;
        BigDecimal chargeUnitCount = BigDecimal.ZERO;
        // 按阶梯计算价格
        for (TierRateConfigDTO.TierPriceDTO tierPrice : tierPrices) {
            if (remainingUsage.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal tierMin = tierPrice.getMin();
            BigDecimal tierMax = tierPrice.getMax();

            // 处理无限大的情况（max为-1）
            if (tierMax.compareTo(BigDecimal.valueOf(-1)) == 0) {
                tierMax = BigDecimal.valueOf(Long.MAX_VALUE);
            }

            log.info("处理按量计费阶梯 [{} - {} {}], 已处理用量: {} {}, 剩余用量: {} {}",
                    tierMin, tierMax, measureUnit, processedUsage, measureUnit, remainingUsage, measureUnit);

            // 判断当前阶梯是否与本次用量有交集
            if (processedUsage.compareTo(tierMax) >= 0) {
                // 已处理用量已超过当前阶梯上限，跳过
                continue;
            }

            // 计算本次用量在当前阶梯中的部分（左开右闭区间）
            BigDecimal tierUsageStart = processedUsage.max(tierMin);
            BigDecimal tierUsageEnd = totalUsageWithCurrent.min(tierMax);
            BigDecimal usageInThisTier = tierUsageEnd.subtract(tierUsageStart).max(BigDecimal.ZERO);

            if (usageInThisTier.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            log.info("本次用量在阶梯 [{} - {} {}] 中消费: {} {}", tierMin, tierMax, measureUnit, usageInThisTier, measureUnit);

            // 计算计量单位数（支持向上取整）
            BigDecimal units = roundUnit(usageInThisTier, measure, measureCeil);
            chargeUnitCount = chargeUnitCount.add(units);

            // 根据是否全额支付和支付方式计算价格
            Pair<BigDecimal, BigDecimal> tierAmountPair = calculateTierAmount(request, rateConfig, tierPrice, units, usageInThisTier, previousUsage, tierMin);
            tierPrice.setUsage(usageInThisTier);
            tierPrice.setUsageUnit(measureUnit);
            tierPrice.setOriginalPrice(tierAmountPair.getKey());
            tierPrice.setDiscountedPrice(tierAmountPair.getValue());
            tierPrice.setChargeUnitCount(units);
            tierPrice.setIsHit(true);
            log.info("按量计费阶梯 [{} - {}] 计费: 用量={}, 计量单位={}, 向上取整={}, 计量单位数={}, 目录价={}, 优惠价={}",
                    tierMin, tierMax, usageInThisTier, measure, measureCeil, units, tierAmountPair.getKey(), tierAmountPair.getValue());

            originalPriceTotal = originalPriceTotal.add(tierAmountPair.getKey());
            discountPriceTotal = discountPriceTotal.add(tierAmountPair.getValue());
            processedUsage = tierUsageEnd;
            remainingUsage = remainingUsage.subtract(usageInThisTier);
        }

        log.info("按量计费阶梯费用计算完成 - 本次用量: {}, 目录价: {}, 优惠价: {}", currentUsage, originalPriceTotal, discountPriceTotal);

        response.setOriginalPrice(originalPriceTotal);
        response.setDiscountedPrice(discountPriceTotal);
        response.setChargeUnitCount(chargeUnitCount);
    }

    /**
     * 计算单个阶梯的费用（支持单位转换和向上取整）
     */
    private Pair<BigDecimal, BigDecimal> calculateTierAmount(OriginalPriceCalculateRequest request,
                                                             UsageBasedRateConfigDTO rateConfig,
                                                             TierRateConfigDTO.TierPriceDTO tierPrice,
                                                             BigDecimal units,
                                                             BigDecimal usageInThisTier,
                                                             BigDecimal previousUsage,
                                                             BigDecimal tierMin) {
        Integer paymentOptions = request.getPaymentOptions();
        List<Coupon> couponList = request.getCouponList();
        // 如果有配置全额支付
        if (tierPrice.getIsAllPay() != null && tierPrice.getIsAllPay() == 1) {
            return calculateAllPayTierAmount(request, rateConfig, tierPrice, usageInThisTier, previousUsage, tierMin);
        }

        // 按用量计算：支付类型的单价 * 计量单位数
        // 目录价计算
        BigDecimal originalUnitPrice = getUnitPrice(paymentOptions, tierPrice.getFixCharge(), tierPrice.getIntegralCharge());
        BigDecimal originalPrice = getTotalPrice(paymentOptions, units, originalUnitPrice.divide(BigDecimal.valueOf(tierPrice.getPayUnit()), 6, RoundingMode.HALF_UP));
        tierPrice.setOriginalUnitPrice(originalUnitPrice);

        // 优惠价计算
        BigDecimal discountUnitPrice = calculateDiscountPrice(originalUnitPrice, couponList);
        BigDecimal discountPrice = getTotalPrice(paymentOptions, units, discountUnitPrice.divide(BigDecimal.valueOf(tierPrice.getPayUnit()), 6, RoundingMode.HALF_UP));
        tierPrice.setDiscountedUnitPrice(discountUnitPrice);

        return Pair.of(originalPrice, discountPrice);
    }

    /**
     * 计算全额支付阶梯的费用（参考套餐计费逻辑，支持单位转换和向上取整）
     *
     * @param request         原价计算请求
     * @param rateConfig      按量计费配置
     * @param tierPrice       阶梯费率配置
     * @param usageInThisTier 在当前阶梯的用量
     * @param previousUsage   之前累计用量
     * @param tierMin         当前阶梯下限
     * @return 该阶梯的目录价和优惠价
     */
    private Pair<BigDecimal, BigDecimal> calculateAllPayTierAmount(OriginalPriceCalculateRequest request,
                                                                   UsageBasedRateConfigDTO rateConfig,
                                                                   TierRateConfigDTO.TierPriceDTO tierPrice,
                                                                   BigDecimal usageInThisTier,
                                                                   BigDecimal previousUsage,
                                                                   BigDecimal tierMin) {
        Integer paymentOptions = request.getPaymentOptions();
        List<Coupon> couponList = request.getCouponList();

        // 判断之前的累计用量是否已经进入过该阶梯
        boolean hasEnteredThisTierBefore = previousUsage.compareTo(tierMin) > 0;

        if (hasEnteredThisTierBefore) {
            // 之前已经进入过该阶梯，说明已经付过全额费用，本次免费
            log.info("按量计费阶梯{}之前已付费，本次使用免费", tierPrice.getTierLevel());
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 第一次进入该阶梯，需要按全额支付计算
        // 计算阶梯的范围大小（左开右闭区间：max - min）
        String measureUnit = rateConfig.getMeasureUnit();
        BigDecimal tierRangeSize;
        if (tierPrice.getMax().compareTo(BigDecimal.valueOf(-1)) == 0) {
            // 处理无限大(值为-1)的情况，如果是无限大阶梯，则按实际用量计算
            tierRangeSize = usageInThisTier;
        } else {
            // 阶梯范围大小 = max - min（左开右闭区间）
            tierRangeSize = tierPrice.getMax().subtract(tierPrice.getMin());
        }

        // 计算阶梯范围大小对应的计量单位数（支持向上取整）
        BigDecimal measure = rateConfig.getMeasure();
        Integer measureCeil = rateConfig.getMeasureCeil();
        BigDecimal units = roundUnit(tierRangeSize, measure, measureCeil);
        // 计算价格单位数
        BigDecimal perPayUnit = units.divide(BigDecimal.valueOf(tierPrice.getPayUnit()), 2, RoundingMode.UP);
        log.info("按量计费全额支付 - 是否向上取整: {}, 计量单位数: {}, 每单位: {}, 价格单位: {}", measureCeil, units, tierPrice.getPayUnit(), perPayUnit);

        // 根据支付方式计算目录价
        BigDecimal originalUnitPrice = getUnitPrice(paymentOptions, tierPrice.getFixCharge(), tierPrice.getIntegralCharge());
        BigDecimal originalPrice = getTotalPrice(paymentOptions, perPayUnit, originalUnitPrice);
        tierPrice.setOriginalUnitPrice(originalUnitPrice);

        // 优惠价计算
        BigDecimal discountUnitPrice = calculateDiscountPrice(originalUnitPrice, couponList);
        BigDecimal discountPrice = getTotalPrice(paymentOptions, perPayUnit, discountUnitPrice);
        tierPrice.setDiscountedUnitPrice(discountUnitPrice);

        log.info("按量计费阶梯 {} 首次进入，阶梯范围[{}-{} {}]，范围大小: {} {}，单价: {}, 目录价: {}, 优惠价: {}",
                tierPrice.getTierLevel(), tierPrice.getMin(), tierPrice.getMax(), measureUnit, tierRangeSize, measureUnit, originalUnitPrice, originalPrice, discountPrice);
        return Pair.of(originalPrice, discountPrice);
    }

    /**
     * 根据币种选择固定费率配置
     */
    private Optional<FixedRateConfigDTO> selectFixedRateConfigByCurrency(List<FixedRateConfigDTO> configs, String currency) {
        if (configs == null || configs.isEmpty()) {
            return Optional.empty();
        }

        // 优先匹配指定币种
        for (FixedRateConfigDTO config : configs) {
            if (currency.equals(config.getCurrency())) {
                log.info("找到匹配币种的固定费率配置: {}", currency);
                return Optional.of(config);
            }
        }

        // 如果没有匹配的币种
        log.warn("未找到匹配币种 {} 的固定费率配置", currency);
        return Optional.empty();
    }

    /**
     * 根据币种选择阶梯费率配置
     */
    private Optional<UsageBasedRateConfigDTO.StairRateConfigDTO> selectLevelRateConfigByCurrency(List<UsageBasedRateConfigDTO.StairRateConfigDTO> configs, String currency) {
        if (configs == null || configs.isEmpty()) {
            return Optional.empty();
        }

        // 优先匹配指定币种
        for (UsageBasedRateConfigDTO.StairRateConfigDTO config : configs) {
            if (currency.equals(config.getCurrency())) {
                log.info("找到匹配币种的阶梯费率配置: {}", currency);
                return Optional.of(config);
            }
        }

        // 如果没有匹配的币种
        log.warn("未找到匹配币种 {} 的阶梯费率配置", currency);
        return Optional.empty();
    }
}
