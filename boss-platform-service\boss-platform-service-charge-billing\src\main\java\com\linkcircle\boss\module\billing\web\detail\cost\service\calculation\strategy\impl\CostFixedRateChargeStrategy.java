package com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.impl;

import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.billing.api.rate.model.dto.FixedRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.CostRateTypeEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.exception.RepeatedBillingException;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.data.service.RateCacheDataService;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.ResourceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.AbstractCostRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeRequest;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeResponse;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.service.CostRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.supplier.account.vo.SupplierAccountVO;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025-06-24 16:10
 * @description 成本固定费率计费策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = CostRateTypeEnum.class, strategyType = RateTypeConstant.FIXED)
public class CostFixedRateChargeStrategy extends AbstractCostRateChargeStrategy implements IStrategy<CostRateChargeRequest, CostRateChargeResponse> {

    private final RateCacheDataService rateCacheDataService;
    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final CostRateUsageManageService costRateUsageManageService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return cyclePeriodCalculateService;
    }

    @Override
    public CostRateChargeResponse execute(CostRateChargeRequest request) {
        log.info("开始执行成本固定费率计费策略");

        // 1. 获取基础数据
        SupplierAccountVO supplierAccountVO = request.getSupplierAccountVO();
        ResourceSubscriptionInfoDTO resourceInfo = request.getResourceSubscriptionInfoDTO();
        ResourcePurchaseVO.Detail detail = resourceInfo.getDetail();
        BigDecimal usage = request.getUsage();
        String accountCurrency = supplierAccountVO.getCurrency();
        // 2. 解析固定费率配置
        FixedRateConfigDTO rateConfig = getFixedRateConfigDTO(detail.getCurrencyPriceJson(), accountCurrency);

        // 3. 计算周期
        Long businessTime = request.getBusinessTime();
        Long purchaseId = resourceInfo.getPurchaseVO().getId();
        Long resourceServiceId = detail.getResourceServiceId();
        CyclePeriodResultVO cyclePeriodResultVO = getBillingCycle(resourceInfo, supplierAccountVO, businessTime);
        Duration billingLockTime = getBillingLockTime(cyclePeriodResultVO);
        boolean lock = rateCacheDataService.setFixedRateBillingLock(BillTypeEnum.COST, purchaseId, resourceServiceId,
                cyclePeriodResultVO.getBillingCycle(), billingLockTime);
        if (!lock) {
            log.error("固定费率计费锁定失败，可能已计费");
            throw new RepeatedBillingException(ErrorCodeConstants.BILLING_CALCULATION_FAILED, "已计费");
        }

        // 4. 计算原价
        Integer paymentOptions = detail.getPaymentOptions();
        OriginalPriceCalculateResponse calculateResponse = calculateOriginalPriceByStrategy(rateConfig, paymentOptions);

        // 5. db更新费率用量
        costRateUsageManageService.updateRateUsage(cyclePeriodResultVO, usage, request.getUsageUnit());

        // 6. 构建响应结果
        CostRateChargeResponse response = CostRateChargeResponse.success();
        response.setCurrency(rateConfig.getCurrency());
        response.setUsage(usage);
        response.setUsageUnit(request.getUsageUnit());
        response.setCyclePeriodResultVO(cyclePeriodResultVO);
        response.setRateConfig(rateConfig);
        convertChargeResponse(response, calculateResponse);

        return response;
    }

    /**
     * 通过策略计算目录价
     */
    private OriginalPriceCalculateResponse calculateOriginalPriceByStrategy(FixedRateConfigDTO rateConfig, Integer paymentOptions) {
        // 构建原价计算请求
        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setRateConfig(rateConfig);
        request.setPaymentOptions(paymentOptions);
        request.setCurrentUsage(BigDecimal.ONE);
        request.setCalculateTaxEnabled(true);

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                OriginalPriceRateTypeEnum.FIXED.name(), request);

        if (response.getSuccess()) {
            return response;
        }
        log.error("原价计算失败: {}", response.getErrorMessage());
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILLING_CALCULATION_FAILED, response.getErrorMessage());
    }
}
