package com.linkcircle.boss.module.charge.crm.web.purchase.model.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 供应商资源采购信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@TableName("charge_purchase")
@Schema(description = "供应商资源采购新增信息")
public class ChargePurchaseAddDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "供应商id")
    @NotNull(message = "供应商id不允许为null")
    private Long suppliersId;

    @Schema(description = "账户ID")
    @NotNull(message = "账户ID不允许为null")
    private Long accountId;

    @Schema(description = "货币代码（ISO 4217 标准，如 CNY 表示人民币）")
    @NotBlank(message = "货币代码不允许为null")
    private String currencyCode;

    @Schema(description = "支付方式，0:现金，1：积分")
    @NotNull(message = "支付方式不允许为空")
    private Integer paymentOptions;

    @Schema(description = "主体ID")
    @NotNull(message = "主体ID不允许为null")
    private Long entityId;

    @Schema(description = "支付类型，0-预付费，1-后付费")
    @NotNull(message = "支付类型不允许为null")
    private Integer paymentType;

    @Schema(description = "采购开始时间")
    @NotNull(message = "采购开始时间不允许为null")
    private Long startTime;

    @Schema(description = "采购结束时间")
    @NotNull(message = "采购结束时间不允许为null")
    private Long endTime;

    @Schema(description = "免费试用天数")
    @NotNull(message = "免费试用天数不允许为null")
    private Integer freeTrialDays;


    @Schema(description = "是否按比例计算，0：否，1：是")
    @NotNull(message = "是否按比例计算不允许为null")
    private Integer byProportion;

    @Schema(description = "主体ID（开单主体）")
    @NotNull(message = "主体ID（开单主体）不允许为null")
    private Long billingEntityId;

    @Schema(description = "是否含税，0：不含税，1：含税")
    @NotNull(message = "是否含税不允许为null")
    private Integer isTaxInclusive;

    @Schema(description = "税率百分比（示例：9.00）")
    @NotNull(message = "税率百分比不允许为null")
    private Integer rate;

    @Schema(description = "出账周期类型（数据字典）")
    @NotNull(message = "出账周期类型不允许为null")
    private Integer cycleType;

    @Schema(description = "天数")
    @NotNull(message = "天数不允许为null")
    private Integer days;

    @Schema(description = "资源服务dto")
    @NotEmpty(message = "资源服务dto不允许为null")
    private List<ServiceVersionDTO> serviceVersionDTOList;


}
