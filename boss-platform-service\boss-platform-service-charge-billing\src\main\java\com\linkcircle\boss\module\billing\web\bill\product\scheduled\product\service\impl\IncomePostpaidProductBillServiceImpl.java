package com.linkcircle.boss.module.billing.web.bill.product.scheduled.product.service.impl;

import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.web.bill.product.mapper.PostpaidProductIncomeBillMapper;
import com.linkcircle.boss.module.billing.web.bill.product.mapper.PostpaidProductServiceIncomeBillMapper;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.product.service.IncomePostpaidProductBillService;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-08 11:45
 * @description 后付费产品账单数据服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomePostpaidProductBillServiceImpl implements IncomePostpaidProductBillService {

    private final PostpaidProductIncomeBillMapper productBillMapper;
    private final PostpaidProductServiceIncomeBillMapper serviceBillMapper;
    private final RedissonUtil redissonUtil;

    @Override
    public boolean saveProductBill(PostpaidProductIncomeBillDO productBill) {
        int insertCount = productBillMapper.insert(productBill);
        boolean success = insertCount > 0;
        log.info("保存产品账单, billId: {}, 结果: {}", productBill.getProductBillId(), success);
        return success;
    }

    @Override
    public boolean alreadyProductBill(Long productId, Long subscriptionId, Long startTime, Long endTime) {
        String cacheKey = ChargeCacheUtils.getProductBillCacheKey(productId, subscriptionId, startTime, endTime);
        boolean exists = redissonUtil.exists(cacheKey);
        if (exists) {
            log.info("Redis缓存中产品账单标记已存在, productId: {}, subscriptionId: {}, startTime: {}, endTime: {}",
                    productId, subscriptionId, startTime, endTime);
            return true;
        }
        return false;
    }

    @Override
    public boolean saveProductBillFlag(Long productId, Long subscriptionId, Long startTime, Long endTime) {
        String cacheKey = ChargeCacheUtils.getProductBillCacheKey(productId, subscriptionId, startTime, endTime);
        Duration duration = Duration.ofMillis(endTime - startTime);
        return redissonUtil.setNx(cacheKey, System.currentTimeMillis(), duration);
    }

    @Override
    public boolean existsByProductAndPeriod(Long productId, Long subscriptionId, Long startTime, Long endTime) {
        if (alreadyProductBill(productId, subscriptionId, startTime, endTime)) {
            return true;
        }
        return Objects.nonNull(productBillMapper.existsByProductAndPeriod(productId, subscriptionId, startTime, endTime));
    }

    @Override
    public List<PostpaidProductServiceIncomeBillDO> getServiceBillsByIds(List<Long> serviceIds,
                                                                         Long subscriptionId,
                                                                         Long productId,
                                                                         Long startTime,
                                                                         Long endTime) {
        return serviceBillMapper.selectByServiceIds(serviceIds, subscriptionId, productId, startTime, endTime);
    }

    @Override
    public boolean updateServiceBillProductId(List<PostpaidProductServiceIncomeBillDO> serviceBills, Long productBillId) {
        serviceBills.forEach(serviceBill -> {
            PostpaidProductServiceIncomeBillDO updateDO = new PostpaidProductServiceIncomeBillDO();
            updateDO.setBillId(productBillId);
            updateDO.setProductServiceBillId(serviceBill.getProductServiceBillId());
            serviceBillMapper.updateById(updateDO);
        });
        log.info("批量更新服务账单产品ID, productBillId: {}, count: {}", productBillId, serviceBills.size());
        return true;
    }
}