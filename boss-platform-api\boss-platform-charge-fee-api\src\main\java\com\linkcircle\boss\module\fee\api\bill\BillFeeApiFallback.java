package com.linkcircle.boss.module.fee.api.bill;

import com.linkcircle.boss.framework.common.model.CommonResult;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 * @date 2025/7/30 15:03
 */
@org.springframework.stereotype.Component
@lombok.extern.slf4j.Slf4j
public class BillFeeApiFallback  implements FallbackFactory<BillFeeApi> {
    @Override
    public BillFeeApi create(Throwable cause) {
        return new BillFeeApi() {
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<java.math.BigDecimal> computeConstructFee(Long constructId,Long startTime,Long endTime){
                log.error("调用账单费用中心API失败，constructId: {},startTime: {},endTime: {} 异常信息: ", constructId,startTime,endTime, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<java.lang.Long> computeCustomerUnPaidBillNum(Long customerId){
                log.error("调用账单费用中心API失败，customerId: {} 异常信息: ", customerId, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
