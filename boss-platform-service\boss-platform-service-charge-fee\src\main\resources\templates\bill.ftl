<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${title!''}</title>
    <#--    <link rel="stylesheet" href="./bill.css">-->
</head>
<body>
<div class="invoice-container">
    <div class="invoice-header">
        <img src="${(entity.entityInfo.brandLogoUrl)!''}" class="main-logo" alt="公司Logo">
        <div class="invoice-meta">
            <span>${(entity.entityInfo.entityName)!''} (${entity.entityInfo.taxNumber})</span><br>
            <span>${(entity.entityInfo.address)!''}, ${(entity.entityInfo.city)!''}</span><br>
            <span>${(entity.entityInfo.postalCode)!''}, ${(entity.entityInfo.email)!''}</span>
        </div>
    </div>

    <div class="invoice-body">
        <div class="parties">
            <div class="seller">
                <span>To Customer</span><br>
                <span>${entity.customerInfo.customerName!''}</span><br>
                <span>ADDRESS</span><br>
                <span>${entity.customerInfo.address!''}</span>
            </div>
            <div class="seller textAlginRight">
                <span>Invoice Date: ${entity.date!''} </span><br>
                <span>Invoice No: ${entity.invoiceConfig.startSequence!''} </span>
            </div>
        </div>

        <table class="invoice-items">
            <thead>
            <tr class="tableTr">
                <th class="width25">Description</th>
                <th class="width15">Unit Price<p class="padding0">(SGD)</p></th>
                <th class="width15">Qty<p class="padding0">(Numbers)</p></th>
                <th class="width15">Price<p class="padding0">(SGD)</p></th>
                <th class="width15">Taxes</th>
                <th class="width15">Amount<p class="padding0">(SGD)</p></th>
            </tr>
            </thead>
            <tbody>
            <#if ( entity?? && entity.items?? && entity.items?size > 0)>
                <#list entity.items as item>
                    <tr>
                        <td class="width25">${item_index}</td>
                        <td class="width15">${item.serviceName!''}</td>
                        <td class="width15">${item.number!''}</td>
                        <td class="width15">${item.price!''}</td>
                        <td class="width15">${item.amount!''}</td>
                        <td class="width15">${item.amount!''}</td>
                    </tr>
                </#list>
            </#if>
            </tbody>
        </table>
        <div class="total">
            <div class="total-row">
                <span>
                    <span class="total-item">Subtotal (Excluding GST):</span>
                    <span class="total-value">$100.00</span>
                </span>
            </div>
            <div class="total-row">
                <span>
                    <span class="total-item">Discount</span>
                    <span class="total-value">$10.00</span>
                </span>
            </div>
            <div class="total-row">
                <span>
                    <span class="total-item">Subtotal After Discount:</span>
                    <span class="total-value">$110.00</span>
                </span>
            </div>
            <div class="total-row">
                <span>
                    <span class="total-item">GST</span>
                    <span class="total-value">$110.00</span>
                </span>
            </div>
            <div class="total-row">
                <span>
                    <span class="total-item">Total Amount Payable:</span>
                    <span class="total-value">$110.00</span>
                </span>
            </div>
        </div>
        <div class="law">
            <span class="title">法律信息</span><br>
            <span>${entity.invoiceConfig.legalInformation!''}</span>
        </div>
        <div class="law">
            <span class="title">附加信息</span><br>
            <span>${entity.invoiceConfig.additionalInformation!''}</span>
        </div>
        <div class="bank">
            <div class="bankItem width200">
                <span>Bank Name:</span>
                <span>Bank Account:</span>
                <span>Number:</span>
                <span>Account Name:</span>
                <span>Bank Code:</span>
                <span>Branch Code:</span>
                <span>Swift Code:</span>
            </div>
            <div class="bankItem">
                <span>Bank of China Singapore Branch</span>
                <span>***************</span>
                <span>LINKCIRCLE SINGAPORE PTE. LTD.</span>
                <span>Account Name:</span>
                <span>011</span>
                <span>BKCHSGSG</span>
                <span>-</span>
            </div>
        </div>

    </div>
</div>
<div class="bg" style="background: ${entity.invoiceConfig.color!''}"></div>
<#--<div class="bg" style='"background:" + ${entity.invoiceConfig.legalInformation!""'}></div>-->
</body>
</html>
