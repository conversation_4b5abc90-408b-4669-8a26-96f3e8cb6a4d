package com.linkcircle.boss.module.charge.fee.web.invoice.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linkcircle.boss.framework.tanant.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/30 10:02
 * @description 发票信息表实体类  字段不完整 需要进一步补充
 */
@TableName(value = "charge_invoice_record", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargeInvoiceDO extends TenantBaseDO {

    @Schema(description = "发票主键id")
    @TableId(value = "id", type = com.baomidou.mybatisplus.annotation.IdType.ASSIGN_ID)
    private Long id;

    /**
     * 发票编号(如CN-1, LA20240530-0001)
     */
    @Schema(description = "发票业务id")
    @TableField(value = "invoice_id")
    private String invoiceId;


    /**
     * 发票编号(如CN-1, LA20240530-0001)
     */
    @Schema(description = "类型 0-个人 1-企业")
    @TableField(value = "type")
    private Integer type;

    /**
     * 发票类型(信用票据、国外发票、增值税专票、红冲发票等)
     */
    @Schema(description = "票据类型 0-信用票据 1-普票 2-国外发票 3-增值税专票 4-红冲发票")
    @TableField(value = "invoice_type")
    private Integer invoiceType;

    /**
     * 主体id
     */
    @Schema(description = "发票关联的主体id")
    @TableField(value = "entity_id")
    private Long entityId;

    @Schema(description = "发票关联的客户id")
    @TableField(value = "customer_id")
    private Long customerId;


    @Schema(description = "发票关联的账户ID")
    @TableField(value = "account_id")
    private Long accountId;

    @Schema(description = "开票金额")
    @TableField(value = "invoice_amount")
    private BigDecimal invoiceAmount;

    @Schema(description = "开票账单ID")
    @TableField(value = "invoice_billing_id")
    private String invoiceBillingId;


    @Schema(description = "开票账单号码")
    @TableField(value = "invoice_billing_no")
    private String invoiceBillingNo;

    @Schema(description = "信用票据发票关联id")
    @TableField(value = "credit_invoice_id")
    private Long creditInvoiceId;

    /**
     * 多租户编号
     */
    @Schema(description = "账单的服务编码")
    @TableField(value = "bill_service_code")
    private String billServiceCode;

    /**
     * 账单类型
     */
    @Schema(description = "账单类型 1-预付费账单，2-后付费账单,3-手工账单")
    @TableField(value = "bill_type")
    private Integer billType;

    @Schema(description = "账单的出账时间")
    @TableField(value = "bill_time")
    private Long billTime;

    @Schema(description = "发票状态 0-待审核 1-审核不通过 2-已开票 3-已作废")
    @TableField(value = "status")
    private Integer status;


    @Schema(description = "发票资源地址")
    @TableField(value = "invoice_resource_url")
    private String invoice_resource_url;

    @Schema(description = "币种符号：$")
    @TableField(value = "currency_symbol")
    private String currencySymbol;

    @Schema(description = "主体信息JSON字符串")
    @TableField(value = "entity_json_str")
    private String entityJsonStr;

    @Schema(description = "客户信息JSON字符串")
    @TableField(value = "customer_json_str")
    private String customerJsonStr;
}
