package com.linkcircle.boss.module.billing.web.data.service.impl;

import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.DateTime;
import lombok.extern.slf4j.Slf4j;
import org.github.cloud.framework.common.constant.TimeConstants;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.TimeZone;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025-07-29 16:00
 * @description 周期计算服务上一周期功能测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class CyclePeriodCalculateServicePreviousCycleTest {

    @Resource
    private CyclePeriodCalculateService cyclePeriodCalculateService;

    private static final String DEFAULT_TIMEZONE = TimeConstants.TIMEZONE_SHANGHAI;
    
    // 测试时间：2025年7月16日 10:30:00
    private static final long BUSINESS_TIME = 1721097000000L;
    
    // 开始时间：2025年6月1日 00:00:00
    private static final long START_TIME = 1717171200000L;

    /**
     * 测试月周期上一周期功能
     */
    @Test
    void testCalculateMonthCyclePeriodPrevious() {
        System.out.println("\n=== 测试月周期上一周期功能 ===");
        
        // 测试当前周期
        CyclePeriodResultVO currentResult = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1, false);
        
        // 测试上一周期
        CyclePeriodResultVO previousResult = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1, true);
        
        System.out.println("当前周期结果:");
        printCyclePeriodResult(currentResult);
        
        System.out.println("上一周期结果:");
        printCyclePeriodResult(previousResult);
        
        // 验证结果
        assertTrue(currentResult.isSuccess(), "当前周期计算应该成功");
        assertTrue(previousResult.isSuccess(), "上一周期计算应该成功");
        
        // 验证上一周期的索引比当前周期小1
        assertEquals(currentResult.getCycleIndex() - 1, previousResult.getCycleIndex(), 
                "上一周期索引应该比当前周期小1");
        
        // 验证上一周期的结束时间应该等于当前周期的开始时间-1毫秒
        assertEquals(currentResult.getCycleStartTime() - 1, previousResult.getCycleEndTime(),
                "上一周期的结束时间应该等于当前周期的开始时间-1毫秒");
    }

    /**
     * 测试边界情况：第一个周期的上一周期
     */
    @Test
    void testFirstCyclePrevious() {
        System.out.println("\n=== 测试边界情况：第一个周期的上一周期 ===");
        
        // 使用业务时间等于开始时间，应该是第一个周期
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, START_TIME, START_TIME, 1, true);
        
        System.out.println("第一个周期的上一周期结果:");
        printCyclePeriodResult(result);
        
        // 验证应该返回失败，因为不存在上一周期
        assertFalse(result.isSuccess(), "第一个周期的上一周期应该返回失败");
        assertTrue(result.getErrorMessage().contains("不存在上一周期"), 
                "错误信息应该包含'不存在上一周期'");
    }

    /**
     * 测试边界情况：业务时间未到开始时间
     */
    @Test
    void testBusinessTimeBeforeStartTime() {
        System.out.println("\n=== 测试边界情况：业务时间未到开始时间 ===");
        
        // 业务时间早于开始时间
        long earlyBusinessTime = START_TIME - TimeConstants.MILLIS_PER_DAY; // 开始时间前一天
        
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, earlyBusinessTime, START_TIME, 1, true);
        
        System.out.println("业务时间未到开始时间的上一周期结果:");
        printCyclePeriodResult(result);
        
        // 验证应该返回失败
        assertFalse(result.isSuccess(), "业务时间未到开始时间的上一周期应该返回失败");
        assertTrue(result.getErrorMessage().contains("业务时间未到达开始时间"), 
                "错误信息应该包含'业务时间未到达开始时间'");
    }

    /**
     * 测试向后兼容性：现有方法应该返回当前周期
     */
    @Test
    void testBackwardCompatibility() {
        System.out.println("\n=== 测试向后兼容性 ===");
        
        // 使用原有方法（不带getPreviousCycle参数）
        CyclePeriodResultVO oldMethodResult = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
        
        // 使用新方法，getPreviousCycle=false
        CyclePeriodResultVO newMethodResult = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1, false);
        
        System.out.println("原有方法结果:");
        printCyclePeriodResult(oldMethodResult);
        
        System.out.println("新方法(getPreviousCycle=false)结果:");
        printCyclePeriodResult(newMethodResult);
        
        // 验证两个方法返回相同的结果
        assertEquals(oldMethodResult.getCycleIndex(), newMethodResult.getCycleIndex(), 
                "原有方法和新方法应该返回相同的周期索引");
        assertEquals(oldMethodResult.getCycleStartTime(), newMethodResult.getCycleStartTime(), 
                "原有方法和新方法应该返回相同的周期开始时间");
        assertEquals(oldMethodResult.getCycleEndTime(), newMethodResult.getCycleEndTime(), 
                "原有方法和新方法应该返回相同的周期结束时间");
    }

    /**
     * 测试时间常量的正确性
     */
    @Test
    void testTimeConstants() {
        System.out.println("\n=== 测试时间常量的正确性 ===");
        
        // 验证时间常量计算正确
        assertEquals(1000L, TimeConstants.MILLIS_PER_SECOND);
        assertEquals(60 * 1000L, TimeConstants.MILLIS_PER_MINUTE);
        assertEquals(60 * 60 * 1000L, TimeConstants.MILLIS_PER_HOUR);
        assertEquals(24 * 60 * 60 * 1000L, TimeConstants.MILLIS_PER_DAY);
        assertEquals(7 * 24 * 60 * 60 * 1000L, TimeConstants.MILLIS_PER_WEEK);
        
        System.out.println("时间常量验证通过");
    }

    /**
     * 打印周期计算结果
     */
    private void printCyclePeriodResult(CyclePeriodResultVO result) {
        if (result == null) {
            System.out.println("结果为空");
            return;
        }
        
        System.out.println("成功: " + result.isSuccess());
        if (result.isSuccess()) {
            System.out.println("周期索引: " + result.getCycleIndex());
            System.out.println("周期编号: " + result.getCycleNumber());
            
            if (result.getCycleStartTime() != null) {
                DateTime startTime = new DateTime(result.getCycleStartTime());
                startTime.setTimeZone(TimeZone.getTimeZone(DEFAULT_TIMEZONE));
                System.out.println("周期开始时间: " + DateUtil.formatDateTime(startTime));
            }
            
            if (result.getCycleEndTime() != null) {
                DateTime endTime = new DateTime(result.getCycleEndTime());
                endTime.setTimeZone(TimeZone.getTimeZone(DEFAULT_TIMEZONE));
                System.out.println("周期结束时间: " + DateUtil.formatDateTime(endTime));
            }
            
            if (result.getBillingCycle() != null) {
                System.out.println("出账周期: " + result.getBillingCycle());
            }
        } else {
            System.out.println("错误信息: " + result.getErrorMessage());
        }
        System.out.println("---");
    }
}
