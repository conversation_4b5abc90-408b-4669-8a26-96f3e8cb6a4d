package com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.impl;

import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.module.billing.api.bill.resource.model.entity.PostpaidResourceServiceCostBillDO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.BillCallbackDTO;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.model.dto.CostPostpaidResourceBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.CostPostpaidResourceBillingTransactionMessageService;
import com.linkcircle.boss.module.billing.web.bill.transaction.enums.TransactionMessageTypeEnum;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-07-17
 * @description 后付费资源服务出账事务消息服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostPostpaidResourceBillingTransactionMessageServiceImpl implements CostPostpaidResourceBillingTransactionMessageService {

    private final RocketMQTemplate rocketMQTemplate;

    @Override
    public boolean sendTransactionMessage(PostpaidResourceServiceCostBillDO resourceServiceCostBillDO,
                                          ResourcePurchaseVO purchase,
                                          ResourcePurchaseVO.Detail detail,
                                          CyclePeriodResultVO cyclePeriodResultVO) {
        try {
            // 构建消息体，直接将方法参数放入messageDTO
            CostPostpaidResourceBillingMessageDTO messageDTO = new CostPostpaidResourceBillingMessageDTO();
            messageDTO.setResourceServiceCostBillDO(resourceServiceCostBillDO);
            messageDTO.setPurchase(purchase);
            messageDTO.setDetail(detail);
            messageDTO.setCyclePeriodResultVO(cyclePeriodResultVO);

            // 构建回调DTO用于消息头
            BillCallbackDTO callbackDTO = BillCallbackDTO.builder()
                    .billId(resourceServiceCostBillDO.getResourceServiceBillId())
                    .startTime(cyclePeriodResultVO.getCycleStartTime())
                    .build();

            // 构建消息，添加消息头用于回查和事务类型标识
            Message<CostPostpaidResourceBillingMessageDTO> msg = MessageBuilder
                    .withPayload(messageDTO)
                    .setHeader("extra", JsonUtils.toJsonString(callbackDTO))
                    .setHeader("transactionType", TransactionMessageTypeEnum.COST_RESOURCE_BILLING.getCode())
                    .build();

            String destination = ChargeTopicUtils.getCostServiceBillingTransactionMessageTopic();

            log.info("发送资源服务出账事务消息, destination: {}, billId: {}", destination, resourceServiceCostBillDO.getResourceServiceBillId());

            rocketMQTemplate.sendMessageInTransaction(destination, msg, messageDTO);

            log.info("资源服务出账事务消息发送成功, billId: {}", resourceServiceCostBillDO.getResourceServiceBillId());
            return true;
        } catch (Exception e) {
            log.error("发送资源服务出账事务消息失败, billId: {}", resourceServiceCostBillDO.getResourceServiceBillId(), e);
            return false;
        }
    }
}