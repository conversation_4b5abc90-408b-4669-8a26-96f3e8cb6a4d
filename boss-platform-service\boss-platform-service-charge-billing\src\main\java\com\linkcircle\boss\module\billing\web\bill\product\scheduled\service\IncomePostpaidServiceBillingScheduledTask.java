package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidBillingService;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025-07-07 16:12
 * @description 后付费产品服务出账定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IncomePostpaidServiceBillingScheduledTask {

    private final IncomePostpaidBillingService postpaidBillingService;
    private final SubscriptionDataService subscriptionDataService;
    private final RedissonUtil redissonUtil;

    /**
     * 分布式锁key前缀
     */
    private static final String LOCK_KEY_PREFIX = "billing:income:postpaid:service";

    /**
     * 锁超时时间 - 30分钟
     */
    private static final Duration LOCK_TIMEOUT = Duration.ofMinutes(30);

    /**
     * 收入 后付费后付费订阅出账定时任务
     * 每小时执行一次，检查后付费订阅是否需要出账
     */
    @XxlJob("incomePostpaidServiceBillingHandler")
    @XxlJobRegister(
            cron = "0 30 * * * ?",
            jobDesc = "收入-后付费-服务出账定时任务",
            author = "admin",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.LEAST_FREQUENTLY_USED,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void incomePostpaidServiceBillingHandler() {
        executeBillingTask("服务-收入-后付费订阅", PaymentTypeEnum.POSTPAID);
    }

    /**
     * 执行出账任务的通用方法
     *
     * @param billingType 出账类型（用于日志）
     * @param paymentType 支付类型 0-预付费, 1-后付费
     */
    @SuppressWarnings("all")
    private void executeBillingTask(String billingType, PaymentTypeEnum paymentType) {
        String traceId = IdUtil.fastSimpleUUID();
        TraceIdUtil.buildAndSetTraceId(" ", billingType, traceId);

        try {
            EnableLoginContext.setContext(false);
            log.info("开始执行{}出账定时任务, paymentType: {}", billingType, paymentType);

            // 分布式锁key（不包含出账周期，因为每个账户的出账周期可能不同）
            String lockKey = ChargeCacheUtils.getProcessLockKey(LOCK_KEY_PREFIX, String.valueOf(paymentType));
            RLock lock = redissonUtil.getLock(lockKey);
            boolean isLocked = false;

            try {
                // 尝试获取分布式锁
                isLocked = lock.tryLock(30, LOCK_TIMEOUT.toSeconds(), TimeUnit.SECONDS);
                if (!isLocked) {
                    log.info("{}出账任务正在执行中，跳过本次执行, lockKey: {}", billingType, lockKey);
                    return;
                }

                log.info("获取分布式锁成功，开始执行{}出账任务, lockKey: {}", billingType, lockKey);

                // 执行出账逻辑
                executePostpaidBilling(paymentType);

                log.info("{}出账定时任务执行完成", billingType);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("获取分布式锁被中断, lockKey: {}", lockKey, e);
            } catch (Exception e) {
                log.error("执行{}出账定时任务异常, paymentType: {}", billingType, paymentType, e);
            } finally {
                if (isLocked && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    log.info("释放分布式锁成功, lockKey: {}", lockKey);
                }
            }
        } finally {
            EnableLoginContext.clearContext();
            TraceIdUtil.remove();
        }
    }

    /**
     * 执行后付费出账逻辑
     *
     * @param paymentType 支付类型
     */
    private void executePostpaidBilling(PaymentTypeEnum paymentType) {
        try {
            // 1. 查询全部订阅的账户ID列表
            List<Long> accountIds = subscriptionDataService.getAllSubscriptionAccountIds(paymentType.getMethod());
            if (CollUtil.isEmpty(accountIds)) {
                log.info("未找到需要出账的账户，paymentType: {}", paymentType);
                return;
            }

            log.info("找到需要出账的账户数量: {}, paymentType: {}", accountIds.size(), paymentType);

            // 2. 逐个处理账户（不再批量处理）
            int processedCount = 0;
            int successCount = 0;
            int failureCount = 0;

            for (Long accountId : accountIds) {
                processedCount++;

                try {
                    log.info("开始处理账户出账, accountId: {}, 进度: {}/{}", accountId, processedCount, accountIds.size());

                    // 逐个处理账户（账户内部会查询自己的出账周期配置）
                    postpaidBillingService.processSingleAccount(accountId, paymentType);

                    successCount++;
                    log.info("账户出账处理成功, accountId: {}, 进度: {}/{}", accountId, processedCount, accountIds.size());

                } catch (Exception e) {
                    failureCount++;
                    log.error("账户出账处理失败, accountId: {}, 进度: {}/{}", accountId, processedCount, accountIds.size(), e);
                }

                // 每处理100个账户输出一次进度日志
                if (processedCount % 10 == 0) {
                    log.info("出账进度统计, 已处理: {}/{}, 成功: {}, 失败: {}",
                            processedCount, accountIds.size(), successCount, failureCount);
                }
            }

            log.info("所有账户出账处理完成, 总数: {}, 成功: {}, 失败: {}", processedCount, successCount, failureCount);
        } catch (Exception e) {
            log.error("执行后付费出账逻辑异常, paymentType: {}", paymentType, e);
            throw e;
        }
    }
}
