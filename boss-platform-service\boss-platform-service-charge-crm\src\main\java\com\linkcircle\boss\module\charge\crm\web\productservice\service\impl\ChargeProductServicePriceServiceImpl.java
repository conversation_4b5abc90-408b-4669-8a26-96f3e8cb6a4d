package com.linkcircle.boss.module.charge.crm.web.productservice.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.excel.core.util.ExcelUtils;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.module.charge.crm.web.base.scale.service.ChargeScaleInfoService;
import com.linkcircle.boss.module.charge.crm.web.productservice.mapper.ChargeProductServicePriceMapper;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.dto.*;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.entity.ChargeProductServicePriceDO;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.entity.ChargeServiceResourcesDO;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.vo.ChargeProductServicePriceGroupNameVo;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.vo.ChargeProductServicePricePageVo;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.vo.ChargeProductServicePriceVo;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.vo.ChargeProductServicePriceXlsVo;
import com.linkcircle.boss.module.charge.crm.web.productservice.service.IChargeProductServicePriceService;
import com.linkcircle.boss.module.charge.crm.web.productservice.service.IChargeServiceResourcesService;
import com.linkcircle.boss.module.charge.crm.web.resource.service.IChargeResourceService;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ProductServiceVO;
import com.linkcircle.boss.module.crm.enums.ScaleTypeEnum;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 服务信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@Slf4j
public class ChargeProductServicePriceServiceImpl extends ServiceImpl<ChargeProductServicePriceMapper, ChargeProductServicePriceDO> implements IChargeProductServicePriceService {

    @Autowired
    private ChargeProductServicePriceMapper chargeProductServicePriceMapper;
    @Autowired
    private IChargeServiceResourcesService chargeServiceResourcesService;
    @Autowired
    private IChargeResourceService chargeResourceService;
    @Autowired
    private ChargeScaleInfoService chargeScaleInfoService;

    private static final String VERSION_PATTERN = "^v\\d+(\\.\\d+)+$"; // 匹配 v1.0, v2.3.1 等格式
    private static final Pattern pattern = Pattern.compile(VERSION_PATTERN);


    @Override
    public PageResult<ChargeProductServicePricePageVo> getLatestServicePage(ChargeProductServicePricePageQueryDTO param) {
        // 构造 MyBatis-Plus 分页对象
        Page<ChargeProductServicePricePageVo> page = new Page<>(param.getPageNo(), param.getPageSize());
        Page<ChargeProductServicePricePageVo> servicePricePageVoPage = chargeProductServicePriceMapper.queryPage(page, param);
//        return new PageResult<>(servicePricePageVoPage.getRecords(), servicePricePageVoPage.getTotal());
        return MyBatisUtils.convert2PageResult(page, servicePricePageVoPage.getRecords());
    }

    @Override
    public CommonResult<ChargeProductServicePriceVo> getServiceById(Long serviceId) {
        ChargeProductServicePriceDO productServicePriceDO = this.getById(serviceId);
        ChargeProductServicePriceVo chargeProductServicePriceVo = new ChargeProductServicePriceVo();
        BeanUtils.copyProperties(productServicePriceDO, chargeProductServicePriceVo);

        /*
         * 只返回资源模块 前端用ID调用资源模块接口获取资源信息
         */
        LambdaQueryWrapper<ChargeServiceResourcesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeServiceResourcesDO::getServiceId, serviceId);
        List<ChargeServiceResourcesDO> resourceList = chargeServiceResourcesService.getBaseMapper().selectList(queryWrapper);
        chargeProductServicePriceVo.setRelResources(resourceList);

        /*List<ChargeResource> resourceList = chargeServiceResourcesService.getResourceListByServiceId(serviceId);

        if (CollectionUtils.isNotEmpty(resourceList)) {
            chargeProductServicePriceVo.setRelResources(resourceList);
        }*/

        return CommonResult.success(chargeProductServicePriceVo);
    }

    @Override
    public CommonResult<List<ChargeProductServicePriceDO>> queryServiceList(ChargeProductServicePriceQueryDTO param) {
        LambdaQueryWrapper<ChargeProductServicePriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(param.getServiceName()), ChargeProductServicePriceDO::getServiceName, param.getServiceName());
        queryWrapper.eq(ObjUtil.isNotNull(param.getId()), ChargeProductServicePriceDO::getId, param.getId());
        queryWrapper.eq(ObjUtil.isNotNull(param.getStatus()), ChargeProductServicePriceDO::getStatus, param.getStatus());
        queryWrapper.eq(ObjUtil.isNotNull(param.getConfigBusProto()), ChargeProductServicePriceDO::getConfigBusProto, param.getConfigBusProto());
        queryWrapper.eq(ObjUtil.isNotNull(param.getConfigResource()), ChargeProductServicePriceDO::getConfigResource, param.getConfigResource());

        List<ChargeProductServicePriceDO> list = this.list(queryWrapper);

        return CommonResult.success(list);
    }

    @Override
    @Transactional
    public CommonResult<Long> saveFixRateChargeProductServicePrice(ChargeProductServicePriceAddFixOrStairDTO param) throws JsonProcessingException {
        if (param.getChargeType() == 2 || param.getChargeType() == 3) {
            return CommonResult.error(400, "不支持的计费类型");
        }
        if (param.getChargeType() == 1 && (ObjUtil.isNull(param.getStairRatePrices()) || param.getStairRatePrices().isEmpty())) {
            return CommonResult.error(400, "阶梯计费类型缺少阶梯计费参数");
        }
        if (param.getChargeType() == 0 && (ObjUtil.isNull(param.getFixRatePrices()) || param.getFixRatePrices().isEmpty())) {
            return CommonResult.error(400, "固定计费类型缺少固定计费参数");
        }
        /*boolean validVersionFormat = this.isValidVersionFormat(param.getVersionName());
        if (!validVersionFormat) {
            return CommonResult.error(400, "版本格式错误");
        }*/
        ChargeProductServicePriceDO chargeProductServicePriceDO = new ChargeProductServicePriceDO();
        BeanUtils.copyProperties(param, chargeProductServicePriceDO);
        CommonResult<Long> checkVerResult = this.checkServiceChargeTypeAndVersion(param.getServiceName(), param.getChargeType(), "", chargeProductServicePriceDO);
        if (checkVerResult.getCode() != 0) {
            return checkVerResult;
        }
        // 固定和阶梯创建后自动激活, 资源可后配置
        chargeProductServicePriceDO.setStatus(1);
        // 生成serviceCode 修改服务编码
//        String serviceCode = convertToCode(param.getServiceName());
//        chargeProductServicePriceDO.setServiceCode(serviceCode);

        ObjectMapper objectMapper = new ObjectMapper();
        switch (param.getChargeType()) {
            case 0: // 固定费率
                List<ChargeFixRatePriceDTO> fixRatePrices = param.getFixRatePrices();
                chargeProductServicePriceDO.setCurrencyPriceJson(objectMapper.writeValueAsString(fixRatePrices));
                // 记录币种集合
                List<String> currencyFixList = fixRatePrices.stream().map(ChargeFixRatePriceDTO::getCurrency).toList();
                chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", currencyFixList));

                break;
            case 1: // 阶梯费率
                List<ChargeStairRatePriceDTO> stairRatePrices = param.getStairRatePrices();
                chargeProductServicePriceDO.setCurrencyPriceJson(objectMapper.writeValueAsString(stairRatePrices));
                // 记录币种集合
                List<String> currencyStairList = stairRatePrices.stream().map(ChargeStairRatePriceDTO::getCurrency).toList();
                chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", currencyStairList));

                break;
        }

        boolean save = this.save(chargeProductServicePriceDO);
        if (save) {
            return CommonResult.success(chargeProductServicePriceDO.getId());
        } else {
            return CommonResult.error(500, "保存失败");
        }
    }

    /**
     * 检查使用 按量计费费率参数
     *
     * @param paymentOptions 支付方式 现金|积分
     * @param usageRatePrice 多币种计费参数
     * @param priceModel     固定|阶梯
     * @return
     */
    private CommonResult<Long> checkUsagePriceRule(Integer paymentOptions, ChargeUsageRatePriceDTO usageRatePrice, String priceModel) {
        switch (priceModel) {
            case "fixed":
                if (usageRatePrice.getFixRatePrices() == null || usageRatePrice.getFixRatePrices().isEmpty()) {
                    return CommonResult.error(400, "固定计费类型缺少固定计费参数");
                }
                List<ChargeUsageFixRatePriceDTO> fixRatePrices = usageRatePrice.getFixRatePrices();
                boolean fixValid = fixRatePrices.stream().allMatch(fixRatePrice -> {
                    if (0 == paymentOptions) {
                        return fixRatePrice.getFixCharge() != null && !fixRatePrice.getFixCharge().isEmpty();
                    }
                    if (1 == paymentOptions) {
                        return fixRatePrice.getIntegralCharge() != null && !fixRatePrice.getIntegralCharge().isEmpty();
                    }
                    if (2 == paymentOptions) {
                        return fixRatePrice.getFixCharge() != null && !fixRatePrice.getFixCharge().isEmpty() &&
                                fixRatePrice.getIntegralCharge() != null && !fixRatePrice.getIntegralCharge().isEmpty();
                    }
                    return false; // 默认不匹配
                });

                if (!fixValid) {
                    return CommonResult.error(400, "金额或者积分数据缺失");
                }
                break;
            case "level":
                if (usageRatePrice.getStairRatePrices() == null || usageRatePrice.getStairRatePrices().isEmpty()) {
                    return CommonResult.error(400, "阶梯计费类型缺少阶梯计费参数");
                }
                List<ChargeUsageStairRatePriceDTO> stairRatePrices = usageRatePrice.getStairRatePrices();
                boolean stairValid = stairRatePrices.stream().allMatch(stairRatePrice -> {
                    return stairRatePrice.getTierPrices().stream().allMatch(tierPrice -> {
                        if (0 == paymentOptions) {
                            return tierPrice.getFixCharge() != null && !tierPrice.getFixCharge().isEmpty();
                        }
                        if (1 == paymentOptions) {
                            return tierPrice.getIntegralCharge() != null && !tierPrice.getIntegralCharge().isEmpty();
                        }
                        if (2 == paymentOptions) {
                            return tierPrice.getFixCharge() != null && !tierPrice.getFixCharge().isEmpty() &&
                                    tierPrice.getIntegralCharge() != null && !tierPrice.getIntegralCharge().isEmpty();
                        }
                        // 最后校验min、max不能为空
                        return false; // 默认不匹配
                    });
                });
                if (!stairValid) {
                    return CommonResult.error(400, "金额或者积分数据缺失");
                }
                break;
            default:
                return CommonResult.error(400, "不支持的计费类型");
        }
        return CommonResult.success();
    }

    @Override
    @Transactional
    public CommonResult<Long> saveUsageRateChargeProductServicePrice(ChargeProductServicePriceAddUsageRateDTO param) throws JsonProcessingException {
        if (param.getChargeType() != 3) {
            return CommonResult.error(400, "不支持的计费类型");
        }
        /*boolean validVersionFormat = this.isValidVersionFormat(param.getVersionName());
        if (!validVersionFormat) {
            return CommonResult.error(400, "版本格式错误");
        }*/
        CommonResult<Long> usageRuleRs = this.checkUsagePriceRule(param.getPaymentOptions(), param.getUsageRatePrice(), param.getUsageRatePrice().getPriceModel());
        if (usageRuleRs.getCode() != 0) {
            return usageRuleRs;
        }
        ChargeProductServicePriceDO chargeProductServicePriceDO = new ChargeProductServicePriceDO();
        BeanUtils.copyProperties(param, chargeProductServicePriceDO);
        CommonResult<Long> checkVerResult = this.checkServiceChargeTypeAndVersion(param.getServiceName(), param.getChargeType(), "", chargeProductServicePriceDO);
        if (checkVerResult.getCode() != 0) {
            return checkVerResult;
        }

        // 按量类型计费默认是未配置量表 数据库默认状态未激活
        chargeProductServicePriceDO.setConfigBusProto(1);
        // 生成serviceCode
//        String serviceCode = convertToCode(param.getServiceName());
//        chargeProductServicePriceDO.setServiceCode(serviceCode);

        ObjectMapper objectMapper = new ObjectMapper();
        ChargeUsageRatePriceDTO usageRatePrice = param.getUsageRatePrice();
        chargeProductServicePriceDO.setCurrencyPriceJson(objectMapper.writeValueAsString(usageRatePrice));

        if ("fixed".equals(usageRatePrice.getPriceModel())) {
            List<ChargeUsageFixRatePriceDTO> fixRatePrices = usageRatePrice.getFixRatePrices();
            List<String> currencyList = fixRatePrices.stream().map(ChargeUsageFixRatePriceDTO::getCurrency).toList();
            chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", currencyList));
        }
        if ("level".equals(usageRatePrice.getPriceModel())) {
            List<ChargeUsageStairRatePriceDTO> stairRatePrices = usageRatePrice.getStairRatePrices();
            List<String> currencyList = stairRatePrices.stream().map(ChargeUsageStairRatePriceDTO::getCurrency).toList();
            chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", currencyList));
        }

        boolean save = this.save(chargeProductServicePriceDO);
        if (save) {
            return CommonResult.success(chargeProductServicePriceDO.getId());
        } else {
            return CommonResult.error(500, "保存失败");
        }
    }

    /**
     * 检查使用 套餐的计费规则
     *
     * @param paymentOptions    支付方式现金|积分
     * @param packageRatePrices 多币种计费、 套餐外固定还是阶梯
     * @param isInPackage       是否有套餐外 0-无
     * @return
     */
    private CommonResult<Long> checkPackagePriceRule(Integer paymentOptions, List<ChargePackageRatePriceDTO> packageRatePrices, Integer isInPackage) {
        if (0 == isInPackage) {
            boolean hasOutPackage = packageRatePrices.stream().allMatch(packageRatePrice -> {
                List<ChargePackageRatePriceDetailDTO> packages = packageRatePrice.getPackages();
                return packages.stream().allMatch(packageRatePriceDetail -> {
                    return packageRatePriceDetail.getOutPackage() == null;
                });
            });
            if (!hasOutPackage) {
                return CommonResult.error(400, "类型是不带套餐外参数");
            }
        }

        if (1 == isInPackage) {
            boolean checkPackageType = packageRatePrices.stream().allMatch(packageRatePrice -> {
                List<ChargePackageRatePriceDetailDTO> packages = packageRatePrice.getPackages();
                return packages.stream().allMatch(packageRatePriceDetail -> {
                    ChargeOutPackageDTO outPackage = packageRatePriceDetail.getOutPackage();
                    if (null == outPackage) {
                        return false;
                    }
                    String outPackagePriceModel = outPackage.getOutPackagePriceModel();
                    if ("fixed".equals(outPackagePriceModel)) {
                        return outPackage.getFixRatePricesOutPackage() != null;
                    }
                    if ("level".equals(outPackagePriceModel)) {
                        return outPackage.getStairRatePricesOutPackage() != null && !outPackage.getStairRatePricesOutPackage().isEmpty();
                    }
                    return false;
                });
            });

            if (!checkPackageType) {
                return CommonResult.error(400, "套餐外计费数据缺失");
            }
        }


        boolean packageValid = packageRatePrices.stream().allMatch(packageRatePrice -> {
            List<ChargePackageRatePriceDetailDTO> packages = packageRatePrice.getPackages();
            return packages.stream().allMatch(packageRatePriceDetail -> {
                ChargeInPackageDTO inPackage = packageRatePriceDetail.getInPackage();
                boolean inPackageValid = false;
                if (0 == paymentOptions) {
                    inPackageValid = inPackage.getFixCharge() != null && !inPackage.getFixCharge().isEmpty();
                }
                if (1 == paymentOptions) {
                    inPackageValid = inPackage.getIntegralCharge() != null && !inPackage.getIntegralCharge().isEmpty();
                }
                if (2 == paymentOptions) {
                    inPackageValid = inPackage.getFixCharge() != null && !inPackage.getFixCharge().isEmpty() &&
                            inPackage.getIntegralCharge() != null && !inPackage.getIntegralCharge().isEmpty();
                }

                if (0 == isInPackage) {
                    // 没有套餐外， 不做后续判断
                    return inPackageValid;
                }

                ChargeOutPackageDTO outPackage = packageRatePriceDetail.getOutPackage();
                String outPackagePriceModel = outPackage.getOutPackagePriceModel();
                boolean outPackageValid = false;
                if ("fixed".equals(outPackagePriceModel) && outPackage.getFixRatePricesOutPackage() != null) {
                    ChargeOutPackageFixRatePriceDTO fixRatePrice = outPackage.getFixRatePricesOutPackage();
                    if (0 == paymentOptions) {
                        outPackageValid = fixRatePrice.getFixCharge() != null && !fixRatePrice.getFixCharge().isEmpty();
                    }
                    if (1 == paymentOptions) {
                        outPackageValid = fixRatePrice.getIntegralCharge() != null && !fixRatePrice.getIntegralCharge().isEmpty();
                    }
                    if (2 == paymentOptions) {
                        outPackageValid = fixRatePrice.getFixCharge() != null && !fixRatePrice.getFixCharge().isEmpty() &&
                                fixRatePrice.getIntegralCharge() != null && !fixRatePrice.getIntegralCharge().isEmpty();
                    }
                } else if ("level".equals(outPackagePriceModel) && outPackage.getStairRatePricesOutPackage() != null
                        && !outPackage.getStairRatePricesOutPackage().isEmpty()) {
                    outPackageValid = outPackage.getStairRatePricesOutPackage().stream().allMatch(stairRateTierPrice -> {
                        if (0 == paymentOptions) {
                            return stairRateTierPrice.getFixCharge() != null && !stairRateTierPrice.getFixCharge().isEmpty();
                        }
                        if (1 == paymentOptions) {
                            return stairRateTierPrice.getIntegralCharge() != null && !stairRateTierPrice.getIntegralCharge().isEmpty();
                        }
                        if (2 == paymentOptions) {
                            return stairRateTierPrice.getFixCharge() != null && !stairRateTierPrice.getFixCharge().isEmpty() &&
                                    stairRateTierPrice.getIntegralCharge() != null && !stairRateTierPrice.getIntegralCharge().isEmpty();
                        }
                        return false; // 默认不匹配
                    });
                } else {
                    // 没有套餐外
                    outPackageValid = true;
                }

                return inPackageValid && outPackageValid;
            });
        });

        if (!packageValid) {
            return CommonResult.error(400, "金额或者积分数据缺失");
        }
        return CommonResult.success();
    }

    @Override
    @Transactional
    public CommonResult<Long> savePackageRateChargeProductServicePrice(ChargeProductServicePriceAddPackageDTO param) throws JsonProcessingException {
        if (param.getChargeType() != 2) {
            return CommonResult.error(400, "不支持的计费类型");
        }
        /*boolean validVersionFormat = this.isValidVersionFormat(param.getVersionName());
        if (!validVersionFormat) {
            return CommonResult.error(400, "版本格式错误");
        }*/
        CommonResult<Long> commonResult = this.checkPackagePriceRule(param.getPaymentOptions(), List.of(param.getPackageRatePrices()), param.getInPackage());
        if (commonResult.getCode() != 0) {
            return commonResult;
        }

        ChargeProductServicePriceDO chargeProductServicePriceDO = new ChargeProductServicePriceDO();
        BeanUtils.copyProperties(param, chargeProductServicePriceDO);
        CommonResult<Long> checkVerResult = this.checkServiceChargeTypeAndVersion(param.getServiceName(), param.getChargeType(), "", chargeProductServicePriceDO);
        if (checkVerResult.getCode() != 0) {
            return checkVerResult;
        }

        // 套餐类型计费默认是未配置 数据库默认状态未激活
        chargeProductServicePriceDO.setConfigBusProto(1);
        // 生成serviceCode
//        String serviceCode = convertToCode(param.getServiceName());
//        chargeProductServicePriceDO.setServiceCode(serviceCode);

        ObjectMapper objectMapper = new ObjectMapper();
//        List<ChargePackageRatePriceDTO> packageRatePrices = param.getPackageRatePrices();
        ChargePackageRatePriceDTO packageRatePrices = param.getPackageRatePrices();
        chargeProductServicePriceDO.setCurrencyPriceJson(objectMapper.writeValueAsString(packageRatePrices));

        List<String> result = packageRatePrices.getPackages().stream()
                .map(ChargePackageRatePriceDetailDTO::getCurrency)
                .toList();
        chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", result));

        boolean save = this.save(chargeProductServicePriceDO);
        if (save) {
            return CommonResult.success(chargeProductServicePriceDO.getId());
        } else {
            return CommonResult.error(500, "保存失败");
        }
    }

    @Override
    @Transactional
    public CommonResult<Long> updateFixRateChargeProductServicePrice(ChargeProductServicePriceUpdateFixOrStairDTO param) throws JsonProcessingException {
        // 只有未激活可编辑
        CommonResult<Long> statusRs = onlyNoActiveDoEditAncCheckCode(param.getId(), param.getServiceCode());
        if (statusRs.getCode() != 0) {
            return statusRs;
        }
        if (param.getChargeType() == 2 || param.getChargeType() == 3) {
            return CommonResult.error(400, "不支持的计费类型");
        }
        if (param.getChargeType() == 1 && (ObjUtil.isNull(param.getStairRatePrices()) || param.getStairRatePrices().isEmpty())) {
            return CommonResult.error(400, "阶梯计费类型缺少阶梯计费参数");
        }
        if (param.getChargeType() == 0 && (ObjUtil.isNull(param.getFixRatePrices()) || param.getFixRatePrices().isEmpty())) {
            return CommonResult.error(400, "固定计费类型缺少固定计费参数");
        }
        ChargeProductServicePriceDO chargeProductServicePriceDO = new ChargeProductServicePriceDO();
        BeanUtils.copyProperties(param, chargeProductServicePriceDO);

        ObjectMapper objectMapper = new ObjectMapper();
        switch (param.getChargeType()) {
            case 0: // 固定费率
                List<ChargeFixRatePriceDTO> fixRatePrices = param.getFixRatePrices();
                chargeProductServicePriceDO.setCurrencyPriceJson(objectMapper.writeValueAsString(fixRatePrices));
                // 记录币种集合
                List<String> currencyFixList = fixRatePrices.stream().map(ChargeFixRatePriceDTO::getCurrency).toList();
                chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", currencyFixList));
                break;
            case 1: // 阶梯费率
                List<ChargeStairRatePriceDTO> stairRatePrices = param.getStairRatePrices();
                chargeProductServicePriceDO.setCurrencyPriceJson(objectMapper.writeValueAsString(stairRatePrices));
                // 记录币种集合
                List<String> currencyStairList = stairRatePrices.stream().map(ChargeStairRatePriceDTO::getCurrency).toList();
                chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", currencyStairList));
                break;
        }

        boolean updated = this.updateById(chargeProductServicePriceDO);
        if (updated) {
            return CommonResult.success(chargeProductServicePriceDO.getId());
        } else {
            return CommonResult.error(500, "更新失败");
        }
    }

    @Override
    @Transactional
    public CommonResult<Long> updateUsageRateChargeProductServicePrice(ChargeProductServicePriceUpdateUsageRateDTO param) throws JsonProcessingException {
        if (param.getChargeType() != 3) {
            return CommonResult.error(400, "不支持的计费类型");
        }
        // 只有未激活可编辑
        CommonResult<Long> statusRs = onlyNoActiveDoEditAncCheckCode(param.getId(), param.getServiceCode());
        if (statusRs.getCode() != 0) {
            return statusRs;
        }
        // 计费规则判断
        CommonResult<Long> usageRuleRs = this.checkUsagePriceRule(param.getPaymentOptions(), param.getUsageRatePrice(), param.getUsageRatePrice().getPriceModel());
        if (usageRuleRs.getCode() != 0) {
            return usageRuleRs;
        }

        ChargeProductServicePriceDO chargeProductServicePriceDO = new ChargeProductServicePriceDO();
        BeanUtils.copyProperties(param, chargeProductServicePriceDO);

        ObjectMapper objectMapper = new ObjectMapper();
        ChargeUsageRatePriceDTO usageRatePrice = param.getUsageRatePrice();
        chargeProductServicePriceDO.setCurrencyPriceJson(objectMapper.writeValueAsString(usageRatePrice));

        if ("fixed".equals(usageRatePrice.getPriceModel())) {
            List<ChargeUsageFixRatePriceDTO> fixRatePrices = usageRatePrice.getFixRatePrices();
            List<String> currencyList = fixRatePrices.stream().map(ChargeUsageFixRatePriceDTO::getCurrency).toList();
            chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", currencyList));
        }
        if ("level".equals(usageRatePrice.getPriceModel())) {
            List<ChargeUsageStairRatePriceDTO> stairRatePrices = usageRatePrice.getStairRatePrices();
            List<String> currencyList = stairRatePrices.stream().map(ChargeUsageStairRatePriceDTO::getCurrency).toList();
            chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", currencyList));
        }

        boolean updated = this.updateById(chargeProductServicePriceDO);
        if (updated) {
            return CommonResult.success();
        } else {
            return CommonResult.error(500, "更新失败");
        }
    }

    @Override
    @Transactional
    public CommonResult<Long> updatePackageRateChargeProductServicePrice(ChargeProductServicePriceUpdatePackageDTO param) throws JsonProcessingException {
        if (param.getChargeType() != 2) {
            return CommonResult.error(400, "不支持的计费类型");
        }
        // 只有未激活可编辑
        CommonResult<Long> statusRs = onlyNoActiveDoEditAncCheckCode(param.getId(), param.getServiceCode());
        if (statusRs.getCode() != 0) {
            return statusRs;
        }
        CommonResult<Long> commonResult = this.checkPackagePriceRule(param.getPaymentOptions(), List.of(param.getPackageRatePrices()), param.getInPackage());
        if (commonResult.getCode() != 0) {
            return commonResult;
        }

        ChargeProductServicePriceDO chargeProductServicePriceDO = new ChargeProductServicePriceDO();
        BeanUtils.copyProperties(param, chargeProductServicePriceDO);

        ObjectMapper objectMapper = new ObjectMapper();
        ChargePackageRatePriceDTO packageRatePrices = param.getPackageRatePrices();
        chargeProductServicePriceDO.setCurrencyPriceJson(objectMapper.writeValueAsString(packageRatePrices));

        List<String> result = packageRatePrices.getPackages().stream()
                .map(ChargePackageRatePriceDetailDTO::getCurrency)
                .toList();
        chargeProductServicePriceDO.setCurrencyDictIds(String.join(",", result));

        boolean updated = this.updateById(chargeProductServicePriceDO);
        if (updated) {
            return CommonResult.success(chargeProductServicePriceDO.getId());
        } else {
            return CommonResult.error(500, "更新失败");
        }
    }

    @Override
    @Transactional
    public boolean changeServiceResourceStatus(Long serviceId, Integer status) throws Exception {
        return lambdaUpdate().eq(ChargeProductServicePriceDO::getId, serviceId)
                .set(ChargeProductServicePriceDO::getConfigResource, status).update();
    }

    @Override
    @Transactional
    public boolean changeServiceScaleStatus(Long serviceId, Integer status) throws Exception {
        return lambdaUpdate().eq(ChargeProductServicePriceDO::getId, serviceId)
                .set(ChargeProductServicePriceDO::getConfigBusProto, status).update();
    }

    @Override
    @Transactional
    public boolean changeServiceStatus(List<Long> serviceIds, Integer status) throws Exception {
        return lambdaUpdate().in(ChargeProductServicePriceDO::getId, serviceIds)
                .set(ChargeProductServicePriceDO::getStatus, status).update();
    }

    /**
     * 状态修改
     * changeType:1 服务状态修改
     * status: 0未激活 1 激活 2 存档
     * changeType:2 服务量表关联修改
     * 量表对于按量和套餐是默认创建时 状态 1 未配置
     * 如果是固定和阶梯则 状态是 0 不涉及
     * 修改状态是 2(已配置) 则关联量表ID不能为空 并且更新状态
     *
     * @param params
     * @return
     * @throws Exception
     */
    @Override
    @Transactional
    public CommonResult<Long> ChargeProductServicePriceChangeStatusDTO(ChargeProductServicePriceChangeStatusDTO params) throws Exception {
        Integer changeType = params.getChangeType();
        List<Long> serviceIds = params.getServiceIds();
        Integer status = params.getStatus();

        switch (changeType) {
            case 1:
                if (status == 1) {
                    // 做激活时要校验 按量和套餐的 量表是否配置
                    List<ChargeProductServicePriceDO> servicePriceDOS = this.listByIds(params.getServiceIds());
                    List<ChargeProductServicePriceDO> checkList = servicePriceDOS.stream()
                            .filter(servicePriceDO -> (servicePriceDO.getChargeType() == 2 || servicePriceDO.getChargeType() == 3))
                            .toList();
                    if (CollectionUtils.isNotEmpty(checkList)) {
                        boolean isActive = checkList.stream().allMatch(servicePriceDO -> {
                            return servicePriceDO.getConfigBusProto() == 2;
                        });
                        if (!isActive) {
                            return CommonResult.error(400, "无法激活, 请先配置量表");
                        }
                    }
                }
                boolean changed = this.changeServiceStatus(serviceIds, status);
                if (!changed) {
                    throw new ServiceException(500, "状态修改失败");
                }
                return CommonResult.success();
            case 2:
                if (status == 2 && params.getScaleId() == null) {
                    return CommonResult.error(400, "关联的量表ID不能为空");
                }
                boolean changeScale = this.changeServiceScaleStatus(serviceIds.getFirst(), status);
                if (!changeScale) {
                    throw new ServiceException(500, "量表关联失败");
                }
                // 配置量表是需要统一更新相同服务所有版本的量表ID
                ChargeProductServicePriceDO productServicePriceDO = this.getById(serviceIds.getFirst());
                Integer chargeType = productServicePriceDO.getChargeType();
                if (1 == chargeType || 0 == chargeType) {
                    return CommonResult.error(400, "不支持的计费类型");
                }
                if (2 != status && 1 != status) {
                    return CommonResult.error(400, "配置量表状态不对");
                }
                String serviceName = productServicePriceDO.getServiceName();
                boolean updated = false;
                if (status == 2) {
                    // 配置量表
                    updated = lambdaUpdate().eq(ChargeProductServicePriceDO::getServiceName, serviceName)
                            .set(ChargeProductServicePriceDO::getScaleId, params.getScaleId())
                            .set(ChargeProductServicePriceDO::getConfigBusProto, status)
                            .update();
                    // 调用量表接口
                    chargeScaleInfoService.createScaleTable(productServicePriceDO.getServiceCode(), params.getScaleId(), ScaleTypeEnum.PRODUCT.getType());
                }
                if (status == 1) {
                    // 取消量表
                    updated = lambdaUpdate().eq(ChargeProductServicePriceDO::getServiceName, serviceName)
                            .set(ChargeProductServicePriceDO::getScaleId, null)
                            .set(ChargeProductServicePriceDO::getConfigBusProto, status)
                            .update();
                }
                if (!updated) {
                    throw new ServiceException(500, "量表关联失败");
                }
                return CommonResult.success();
            default:
                return CommonResult.error(400, "变更类型错误");
        }
    }

    /**
     * 复制版本时 自动配置后缀
     *
     * @param originalName
     * @param matchedNames
     * @return
     */
    public String generateCopyName(String originalName, List<ChargeProductServicePriceDO> matchedNames) {
        // 匹配 "_copyN" 尾缀（N是数字）
        int maxCopy = 0;
        String basePattern = Pattern.quote(originalName) + "_copy(\\d+)$";
        Pattern pattern = Pattern.compile(basePattern);

        for (ChargeProductServicePriceDO priceDO : matchedNames) {
            String name = priceDO.getServiceName();
            Matcher matcher = pattern.matcher(name);
            if (matcher.find()) {
                int num = Integer.parseInt(matcher.group(1));
                maxCopy = Math.max(maxCopy, num);
            }
        }

        return originalName + "_copy" + (maxCopy + 1);
    }

    /**
     * 复制服务（实际是新服务）
     * 复制完整服务包括资源, 但是量表需要置空
     *
     * @param serviceId
     * @return
     * @throws Exception
     */
    @Override
    @Transactional
    public CommonResult<Long> copyService(Long serviceId) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        ChargeProductServicePriceDO productServicePriceDO = this.getById(serviceId);
        if (productServicePriceDO == null) {
            return CommonResult.error(400, "服务不存在");
        }
        ChargeProductServicePriceDO newCopy = objectMapper.convertValue(productServicePriceDO, ChargeProductServicePriceDO.class);
        // 清空被 copy 服务的信息
        newCopy.clearBaseDO();
        newCopy.setId(null);

        // 新增版本号名称不变
        String serviceName = newCopy.getServiceName();
        // 处理新增版本的版本号递增问题和版本序列
        String versionName = productServicePriceDO.getVersionName();
        newCopy.setVersionName(incrementVersion(versionName));
        newCopy.setVersionOrder(productServicePriceDO.getVersionOrder() + 1);

        // 直接使用 _copy 做 likeRight 查询
        /*String likePattern = serviceName + "_copy";
        LambdaQueryWrapper<ChargeProductServicePriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(ChargeProductServicePriceDO::getServiceName, likePattern);
        queryWrapper.orderByDesc(ChargeProductServicePriceDO::getCreateTime);
        List<ChargeProductServicePriceDO> matchedNames = this.baseMapper.selectList(queryWrapper);

        String newServiceName = this.generateCopyName(serviceName, matchedNames);
        if (newServiceName.length() > 64) {
            return CommonResult.error(400, "服务名称过长");
        }
        newCopy.setServiceName(newServiceName);*/

        // 生成serviceCode 延续原 serviceCode
//        String serviceCode = convertToCode(serviceName);
//        newCopy.setServiceCode(serviceCode);
        // 默认未激活
        newCopy.setStatus(0);

        /*
        该逻辑成新增版本后直接复制原数据， 这里废弃
         * 复制后状态默认未激活
         * 按量和套餐类型 重新配置量表
         * 资源配置状态沿用旧有
        newCopy.setStatus(0);
        if (2 == productServicePriceDO.getChargeType() || 3 == productServicePriceDO.getChargeType()) {
            newCopy.setConfigBusProto(1);
            newCopy.setScaleId(null);
        } */

        boolean save = this.save(newCopy);
        if (!save) {
            throw new ServiceException(500, "复制失败");
        }

        // 开始复制资源表 如果资源表是已经配置则开始获取 服务资源关联关系
        if (1 == productServicePriceDO.getConfigResource()) {
            this.copyServiceResource(serviceId, newCopy.getId());
        }

        return CommonResult.success(newCopy.getId());
    }

    @Transactional
    public void copyServiceResource(Long serviceId, Long newServiceId) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        LambdaQueryWrapper<ChargeServiceResourcesDO> lambdaQueryWrapper = new LambdaQueryWrapper<ChargeServiceResourcesDO>();
        lambdaQueryWrapper.eq(ChargeServiceResourcesDO::getServiceId, serviceId);
        List<ChargeServiceResourcesDO> chargeServiceResourcesDOS = chargeServiceResourcesService.list(lambdaQueryWrapper);

        List<ChargeServiceResourcesDO> copySRList = chargeServiceResourcesDOS.stream().map(chargeServiceResourcesDO -> {
            ChargeServiceResourcesDO copySR = objectMapper.convertValue(chargeServiceResourcesDO, ChargeServiceResourcesDO.class);
            copySR.setId(null);
            copySR.setServiceId(newServiceId);
            copySR.clearBaseDO();
            return copySR;
        }).toList();

        boolean saveBatch = chargeServiceResourcesService.saveBatch(copySRList);
        if (!saveBatch) {
            throw new ServiceException(500, "复制失败");
        }
    }

    /**
     * 编辑激活状态的服务
     * 创建一个完全一致的服务 等于新增一个版本后状态未激活的基础上再编辑
     * 激活编辑 不同于复制 ， 量表要全量复制
     *
     * @param serviceId
     * @param newVersion
     * @return
     * @throws Exception
     */
    @Override
    @Transactional
    public CommonResult<Long> activeEdit(Long serviceId, String newVersion) throws Exception {
        ChargeProductServicePriceDO oldService = this.getById(serviceId);
        if (oldService == null) {
            return CommonResult.error(400, "服务不存在");
        }
        if (oldService.getStatus() != 1) {
            return CommonResult.error(400, "该服务不是激活状态");
        }
        ObjectMapper objectMapper = new ObjectMapper();
        ChargeProductServicePriceDO newService = objectMapper.convertValue(oldService, ChargeProductServicePriceDO.class);
        newService.setId(null);

        String oldServiceVersionName = oldService.getVersionName();
        int compared = this.compareVersion(oldServiceVersionName, newVersion);
        if (compared >= 0) {
            return CommonResult.error(400, "新版本号不能小于等于旧版本号");
        }
        newService.setVersionName(newVersion);

        // 获取同名服务的最新版本号是多少
        LambdaQueryWrapper<ChargeProductServicePriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeProductServicePriceDO::getServiceName, oldService.getServiceName());
        queryWrapper.orderByDesc(ChargeProductServicePriceDO::getVersionOrder);

        List<ChargeProductServicePriceDO> ordreVerList = this.list(queryWrapper);
        ChargeProductServicePriceDO first = ordreVerList.getFirst();
        newService.setVersionOrder(first.getVersionOrder() + 1);

        // 生成serviceCode
        String serviceCode = convertToCode(newService.getServiceName());
        newService.setServiceCode(serviceCode);

        boolean save = this.save(newService);
        if (!save) {
            throw new ServiceException(500, "编辑激活服务失败");
        }

        // 开始复制资源表 如果资源表是已经配置则开始获取 服务资源关联关系
        if (1 == oldService.getConfigResource()) {
            this.copyServiceResource(serviceId, newService.getId());
        }

        return CommonResult.success(newService.getId());
    }

    /**
     * 根据服务id删除， 只有存档状态可删
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public CommonResult<Long> removeBatch(List<Long> ids) {
        // 只有存档的服务才能允许删除
        List<ChargeProductServicePriceDO> chargeProductServicePriceDOS = this.listByIds(ids);
        if (chargeProductServicePriceDOS == null || chargeProductServicePriceDOS.isEmpty()) {
            return CommonResult.error(400, "未找到服务");
        }
        boolean checkServiceStatus = chargeProductServicePriceDOS.stream().allMatch(chargeProductServicePriceDO -> {
            return chargeProductServicePriceDO.getStatus() == 2;
        });
        if (!checkServiceStatus) {
            return CommonResult.error(400, "只有存档的服务才能允许删除");
        }
        boolean removed = this.removeBatchByIds(ids);
        return removed ? CommonResult.success() : CommonResult.error(500, "删除失败");
    }

    @Override
    public void export(HttpServletResponse response, ChargeProductServicePriceQueryDTO param) throws IOException {
        List<ChargeProductServicePriceDO> list = this.getBaseMapper().queryExport(param);

        List<ChargeProductServicePriceXlsVo> rebuildList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        list.forEach(chargeProductServicePriceDO -> {

            String chargeType = switch (chargeProductServicePriceDO.getChargeType()) {
                case 0 -> "固定费率";
                case 1 -> "阶梯费率";
                case 2 -> "套餐计费";
                case 3 -> "按量计费";
                default -> "";
            };

            String status = switch (chargeProductServicePriceDO.getStatus()) {
                case 0 -> "未激活";
                case 1 -> "激活";
                case 2 -> "存档";
                default -> "";
            };

            String configResource = switch (chargeProductServicePriceDO.getConfigResource()) {
                case 0 -> "未配置";
                case 1 -> "已配置";
                default -> "";
            };

            String configBusProto = switch (chargeProductServicePriceDO.getConfigBusProto()) {
                case 0 -> "不涉及";
                case 1 -> "未配置";
                case 2 -> "已配置";
                default -> "";
            };
            Long createTime = chargeProductServicePriceDO.getCreateTime();
            Long updateTime = chargeProductServicePriceDO.getUpdateTime();
            String formatCreateTime = sdf.format(new Date(createTime));
            String formatUpdateTime = sdf.format(new Date(updateTime));

            ChargeProductServicePriceXlsVo xlsVo = ChargeProductServicePriceXlsVo.builder()
                    .serviceName(chargeProductServicePriceDO.getServiceName())
                    .description(chargeProductServicePriceDO.getDescription())
                    .chargeType(chargeType)
                    .status(status)
                    .configResource(configResource)
                    .configBusProto(configBusProto)
                    .creator(chargeProductServicePriceDO.getCreator())
                    .createTime(formatCreateTime)
                    .updateTime(formatUpdateTime).build();

            rebuildList.add(xlsVo);
        });

        ExcelUtils.write(response, "服务列表", "服务列表", ChargeProductServicePriceXlsVo.class, rebuildList);

    }

    /**
     * 根据服务名称分组展示归属版本
     *
     * @param serviceName 支持模糊查询
     * @return
     */
    @Override
    public CommonResult<List<ChargeProductServicePriceGroupNameVo>> getServiceListGroupByName(String serviceName, String currencyCode, Integer paymentType) {
        if (1 != paymentType && 0 != paymentType) {
            // 产品获取服务只有现金或者积分
            return CommonResult.error(400, "产品获取服务只能选择现金或者积分");
        }

        LambdaQueryWrapper<ChargeProductServicePriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ObjUtil.isNotEmpty(serviceName), ChargeProductServicePriceDO::getServiceName, serviceName);
        // 筛选出 货币字符串中 包含的部分, 方便二次过滤
        queryWrapper.like(ChargeProductServicePriceDO::getCurrencyDictIds, currencyCode);
        // 只能选择已激活的服务提供给产品使用
        queryWrapper.eq(ChargeProductServicePriceDO::getStatus, 1);
        // 选择服务的支付类型
        if (1 == paymentType) {
            queryWrapper.in(ChargeProductServicePriceDO::getPaymentOptions, List.of(1, 2));
        }
        if (0 == paymentType) {
            queryWrapper.in(ChargeProductServicePriceDO::getPaymentOptions, List.of(2, 0));
        }
        queryWrapper.orderByDesc(ChargeProductServicePriceDO::getCreateTime);
        List<ChargeProductServicePriceDO> serviceList = this.list(queryWrapper);

        Map<String, List<ChargeProductServicePriceDO>> serviceGroupNameMap = serviceList.stream()
                .collect(Collectors.groupingBy(ChargeProductServicePriceDO::getServiceName));

        List<ChargeProductServicePriceGroupNameVo> serviceGroupNameList = serviceGroupNameMap.entrySet().stream()
                .map(entry -> {
                    ChargeProductServicePriceGroupNameVo groupNameVo = new ChargeProductServicePriceGroupNameVo();
                    groupNameVo.setServiceName(entry.getKey());

                    // 对该服务下的所有版本按 versionOrder 倒序排序，并精确匹配 currencyCode
                    List<ChargeProductServicePriceDO> sortedList = entry.getValue().stream()
                            .filter(chargeProductServicePriceDO -> {
                                String currencyDictIds = chargeProductServicePriceDO.getCurrencyDictIds();
                                if (currencyDictIds == null || currencyDictIds.isEmpty()) {
                                    return false;
                                }
                                // 判断逗号分隔的 currencyDictIds 中是否包含指定的 currencyCode
                                return Arrays.asList(currencyDictIds.split(",")).contains(currencyCode);
                            })
                            .sorted(Comparator.comparing(ChargeProductServicePriceDO::getVersionOrder).reversed())
                            .toList();

                    groupNameVo.setChargeProductServicePriceDO(sortedList);
                    return groupNameVo;
                })
                .toList();

        return CommonResult.success(serviceGroupNameList);
    }

    /**
     * 根据产品id查询服务
     *
     * @param productId
     * @return
     */
    public List<ChargeProductServicePriceDO> getServicesByProductId(Long productId) {
        return this.baseMapper.getServicesByProductId(productId);
    }

    /**
     * 根据服务名称获取该服务的所有版本
     *
     * @param serviceName 必填参数, 服务名精确查询
     * @return
     */
    @Override
    public CommonResult<List<ChargeProductServicePricePageVo>> versionList(String serviceName) {
        LambdaQueryWrapper<ChargeProductServicePriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeProductServicePriceDO::getServiceName, serviceName);
        queryWrapper.orderByDesc(ChargeProductServicePriceDO::getVersionOrder);
        List<ChargeProductServicePriceDO> list = this.list(queryWrapper);
        if (ObjUtil.isEmpty(list)) {
            return CommonResult.success();
        }
        List<ChargeProductServicePricePageVo> serviceList = list.stream().map(chargeProductServicePriceDO -> {
            return ChargeProductServicePricePageVo.builder()
                    .id(chargeProductServicePriceDO.getId())
                    .serviceName(chargeProductServicePriceDO.getServiceName())
                    .description(chargeProductServicePriceDO.getDescription())
                    .chargeType(chargeProductServicePriceDO.getChargeType())
                    .status(chargeProductServicePriceDO.getStatus())
                    .configResource(chargeProductServicePriceDO.getConfigResource())
                    .configBusProto(chargeProductServicePriceDO.getConfigBusProto())
                    .versionName(chargeProductServicePriceDO.getVersionName())
                    .build();
        }).toList();
        return CommonResult.success(serviceList);
    }

    @Override
    public List<CommonVO> findNameByIds(CommonDTO commonDTO) {
        QueryWrapper<ChargeProductServicePriceDO> wrapper = new QueryWrapper<>();
        wrapper.select("id", "service_name");
        wrapper.in("id", commonDTO.getIds());
        List<ChargeProductServicePriceDO> list = list(wrapper);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(t -> CommonVO.builder().id(t.getId()).name(t.getServiceName()).build()).toList();
        }
        return List.of();
    }

    @Override
    public List<ProductServiceVO> allService() {
        LambdaQueryWrapper<ChargeProductServicePriceDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChargeProductServicePriceDO::getDeleted, false);
        List<ChargeProductServicePriceDO> list = list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(t -> ProductServiceVO.builder().id(t.getId()).serviceName(t.getServiceName()).serviceCode(t.getServiceCode()).build()).toList();
        }
        return List.of();
    }

    @Override
    public CommonResult<com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo> findServiceById(Long serviceId) {
        ChargeProductServicePriceDO productServicePriceDO = this.getById(serviceId);
        com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo chargeProductServicePriceVo = new com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo();
        BeanUtils.copyProperties(productServicePriceDO, chargeProductServicePriceVo);
        return CommonResult.success(chargeProductServicePriceVo);
    }

    /**
     * 只有未激活可编辑
     *
     * @param id 根据id查询服务状态
     * @return
     */
    private CommonResult<Long> onlyNoActiveDoEditAncCheckCode(Long id, String serviceCode) {
        // 只有未激活状态才能编辑
        ChargeProductServicePriceDO productServicePriceDO = this.getById(id);
        Integer status = productServicePriceDO.getStatus();
        if (status != 0) {
            return CommonResult.error(400, "只有未激活状态才能编辑");
        }

        // 编码与原先不同
        String oldServiceCode = productServicePriceDO.getServiceCode();
        if (!serviceCode.equals(oldServiceCode)) {
            List<ChargeProductServicePriceDO> list = lambdaQuery().eq(ChargeProductServicePriceDO::getServiceCode, serviceCode)
                    .list();
            if (CollectionUtils.isNotEmpty(list)) {
                return CommonResult.error(400, "服务编码已存在");
            }
        }

        return CommonResult.success();
    }

    /**
     * * 这里主要处理数据库中存在的服务
     * * 1. 如果服务已存在，则不允许添加相同的版本和低于最新版本
     * * 2. 同样的服务名称不允许计费类型相同
     * * 3. 计算上传的版本号名称并赋值版本排序给 DO
     *
     * @param serviceName
     * @param chargeType
     * @param versionName
     * @param chargeProductServicePriceDO
     * @return
     */
    private CommonResult<Long> checkServiceChargeTypeAndVersion(String serviceName, Integer chargeType,
                                                                String versionName,
                                                                ChargeProductServicePriceDO chargeProductServicePriceDO) {
        LambdaQueryWrapper<ChargeProductServicePriceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeProductServicePriceDO::getServiceCode, chargeProductServicePriceDO.getServiceCode());
        List<ChargeProductServicePriceDO> serviceCodeList = this.getBaseMapper().selectList(queryWrapper);
        if (ObjUtil.isNotEmpty(serviceCodeList)) {
            log.info("[新增服务] 新增服务Code已存在");
            return CommonResult.error(400, "服务Code已存在");
        }

        queryWrapper.clear();
        queryWrapper.eq(ChargeProductServicePriceDO::getServiceName, serviceName);
        queryWrapper.orderByDesc(ChargeProductServicePriceDO::getVersionOrder);
        List<ChargeProductServicePriceDO> serviceList = this.getBaseMapper().selectList(queryWrapper);

        // 目前更改成 后端自动配置版本号做 自增 这里逻辑变更自增 到 x.9 时 x+1
        // 如果名称找不到服务说明没有创建过 赋值默认版本
        if (ObjUtil.isNull(serviceList) || serviceList.isEmpty()) {
            chargeProductServicePriceDO.setVersionOrder(1);
            chargeProductServicePriceDO.setVersionName("v1.0");
        } else {
            ChargeProductServicePriceDO latestService = serviceList.getFirst();

            Integer latestServiceChargeType = latestService.getChargeType();
            if (!Objects.equals(latestServiceChargeType, chargeType)) {
                log.info("[新增服务] 新增服务版本费率类型与原先版本不同");
                return CommonResult.error(400, "新增服务版本费率类型与原先版本不同");
            }

            ChargeProductServicePriceDO listFirst = serviceList.getFirst();
            chargeProductServicePriceDO.setVersionOrder(listFirst.getVersionOrder() + 1);
            chargeProductServicePriceDO.setVersionName(incrementVersion(listFirst.getVersionName()));
        }
        /*if (!ObjUtil.isNull(serviceList) && !serviceList.isEmpty()) {
            ChargeProductServicePriceDO latestService = serviceList.getFirst();

            Integer latestServiceChargeType = latestService.getChargeType();
            if (!Objects.equals(latestServiceChargeType, chargeType)) {
                log.info("[新增服务] 新增服务版本费率类型与原先版本不同");
                return CommonResult.error(400, "新增服务版本费率类型与原先版本不同");
            }

            String latestServiceVersionName = latestService.getVersionName();
            int compared = this.compareVersion(versionName, latestServiceVersionName);
            if (compared == 0) {
                log.info("[新增服务] 该服务已存在相同版本");
                return CommonResult.error(400, "该服务已存在相同版本");
            } else if (compared < 0) {
                log.info("[新增服务] 该服务已存在新版本");
                return CommonResult.error(400, "该服务已存在新版本");
            }
            chargeProductServicePriceDO.setVersionOrder(latestService.getVersionOrder() + 1);
        } else {
            // 该服务在数据库里没有创建历史
            chargeProductServicePriceDO.setVersionOrder(1);
        }*/
        return CommonResult.success();
    }

    /**
     * 版本号递增逻辑
     * 示例：v1.0 → v1.1 → ... → v1.9 → v2.0
     *
     * @param currentVersion 当前版本号，如 "v1.9"
     * @return 递增后的版本号，如 "v2.0"
     */
    private String incrementVersion(String currentVersion) {
        if (currentVersion == null || !currentVersion.matches("^v\\d+\\.\\d+$")) {
            throw new IllegalArgumentException("Invalid version format. Expected format: vX.Y");
        }

        // 去除前缀 'v' 并分割主版本和次版本
        String[] parts = currentVersion.substring(1).split("\\.");
        int major = Integer.parseInt(parts[0]); // 主版本号
        int minor = Integer.parseInt(parts[1]); // 次版本号

        // 次版本号到 9 后进位
        if (minor == 9) {
            major += 1;
            minor = 0;
        } else {
            minor += 1;
        }

        return "v" + major + "." + minor;
    }


    /**
     * 校验版本号是否符合 vX.X 格式（至少主版本和次版本）
     *
     * @param version 版本号字符串，如 "v1.0"
     * @return true 表示格式正确
     */
    private boolean isValidVersionFormat(String version) {
        if (version == null || version.isEmpty()) {
            return false;
        }
        Matcher matcher = pattern.matcher(version);
        return matcher.matches();
    }

    /**
     * 比较两个版本号的大小
     *
     * @param v1 版本号1，例如 "v1.0"
     * @param v2 版本号2，例如 "v0.9"
     * @return 1 表示v1>v2，-1 表示v1<v2，0 表示相等
     */
    private int compareVersion(String v1, String v2) {
        // 去除前缀 "v" 并按点分割
        String[] v1Parts = v1.substring(1).split("\\.");
        String[] v2Parts = v2.substring(1).split("\\.");

        int length = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < length; i++) {
            int v1Num = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2Num = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1Num > v2Num) return 1;
            if (v1Num < v2Num) return -1;
        }

        return 0;
    }

    /**
     * 将中文服务名转换为不限长度的英文编码
     *
     * @param serviceName 服务名称(可包含中文、英文、数字)
     * @return 大写英文编码
     */
    public static String convertToCode(String serviceName) {
        StringBuilder code = new StringBuilder();

        for (char c : serviceName.toCharArray()) {
            if (isChinese(c)) {
                // 中文转拼音首字母
                String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
                if (pinyinArray != null && pinyinArray.length > 0) {
                    code.append(pinyinArray[0].charAt(0));
                }
            } else {
                // 非中文字符直接保留
                code.append(c);
            }
        }
        LocalDateTime localDateTime = LocalDateTime.now();
        String timeStr = localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return code.toString().toUpperCase() + timeStr;
    }

    // 判断是否为中文字符
    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A;
    }

}
