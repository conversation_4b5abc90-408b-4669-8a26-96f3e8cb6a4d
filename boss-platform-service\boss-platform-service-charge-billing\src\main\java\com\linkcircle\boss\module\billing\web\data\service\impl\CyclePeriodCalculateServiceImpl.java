package com.linkcircle.boss.module.billing.web.data.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.crm.enums.PeriodUnitEnum;
import lombok.extern.slf4j.Slf4j;
import org.github.cloud.framework.common.constant.TimeConstants;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2025-06-20 17:24
 * @description 周期服务实现类 时间间隔计算
 */
@Slf4j
@Service
public class CyclePeriodCalculateServiceImpl implements CyclePeriodCalculateService {


    @Override
    public CyclePeriodResultVO calculateCyclePeriod(Integer periodUnit, String timezone, long businessTime, long startTime, int period) {
        // 调用新的重载方法，默认获取当前周期
        return calculateCyclePeriod(periodUnit, timezone, businessTime, startTime, period, false);
    }

    @Override
    public CyclePeriodResultVO calculateCyclePeriod(Integer periodUnit, String timezone, long businessTime, long startTime, int period, boolean getPreviousCycle) {
        PeriodUnitEnum periodUnitEnum = PeriodUnitEnum.getByUnit(periodUnit);
        CyclePeriodResultVO cyclePeriodResultVO = switch (periodUnitEnum) {
            case YEAR -> calculateYearCyclePeriod(timezone, businessTime, startTime, period, getPreviousCycle);
            case QUARTER -> calculateQuarterCyclePeriod(timezone, businessTime, startTime, period, getPreviousCycle);
            case MONTH -> calculateMonthCyclePeriod(timezone, businessTime, startTime, period, getPreviousCycle);
            case WEEK -> calculateWeekCyclePeriod(timezone, businessTime, startTime, period, getPreviousCycle);
            case DAY -> calculateDayCyclePeriod(timezone, businessTime, startTime, period, getPreviousCycle);
            case ONCE -> {
                if (getPreviousCycle) {
                    yield CyclePeriodResultVO.failure("一次性周期不支持获取上一周期");
                }
                yield CyclePeriodResultVO.success(1L, startTime, startTime, businessTime);
            }
        };
        cyclePeriodResultVO.setPeriodUnitEnum(periodUnitEnum);
        cyclePeriodResultVO.setPeriod(period);
        if (Objects.nonNull(cyclePeriodResultVO.getCycleStartTime()) && Objects.nonNull(cyclePeriodResultVO.getCycleEndTime())) {
            // 转成yyyyMMdd
            TimeZone tz = TimeZone.getTimeZone(timezone);
            String start = DateUtil.format(new DateTime(cyclePeriodResultVO.getCycleStartTime()).setTimeZone(tz), "yyyyMMdd");
            String end = DateUtil.format(new DateTime(cyclePeriodResultVO.getCycleEndTime()).setTimeZone(tz), "yyyyMMdd");
            cyclePeriodResultVO.setBillingCycle(start + "-" + end);
            cyclePeriodResultVO.setTimezone(timezone);
        }
        return cyclePeriodResultVO;
    }

    @Override
    public CyclePeriodResultVO calculateWeekCyclePeriod(String timezone, long businessTime, long startTime, int intervalWeeks) {
        return calculateWeekCyclePeriod(timezone, businessTime, startTime, intervalWeeks, false);
    }

    @Override
    public CyclePeriodResultVO calculateWeekCyclePeriod(String timezone, long businessTime, long startTime, int intervalWeeks, boolean getPreviousCycle) {
        if (intervalWeeks <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然周周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔周数: {}, 获取上一周期: {}",
                    timezone, businessDatetime, startDatetime, intervalWeeks, getPreviousCycle);

            // 检查业务时间是否已经到达开始时间（仅在获取当前周期时检查）
            if (!getPreviousCycle && businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将开始时间调整到所在周的周一开始（自然周：周一到周日）
            DateTime startWeekBegin = DateUtil.beginOfWeek(startDatetime, true);

            // 将业务时间调整到所在周的周一开始
            DateTime businessWeekBegin = DateUtil.beginOfWeek(businessDatetime, true);

            log.debug("开始时间所在周的周一: {}, 业务时间所在周的周一: {}", startWeekBegin, businessWeekBegin);

            // 使用毫秒差计算周数差，避免跨年问题
            long millisDiff = businessWeekBegin.getTime() - startWeekBegin.getTime();
            long weeksDiff = millisDiff / TimeConstants.MILLIS_PER_WEEK;

            log.debug("毫秒差: {}, 周数差: {}", millisDiff, weeksDiff);

            // 计算当前周期索引（从0开始）
            long currentCycleIndex = weeksDiff / intervalWeeks;

            // 根据参数决定使用当前周期还是上一周期
            long targetCycleIndex = getPreviousCycle ? currentCycleIndex - 1 : currentCycleIndex;

            // 边界检查：上一周期不能小于0
            if (!getPreviousCycle && targetCycleIndex < 0) {
                return CyclePeriodResultVO.failure("不存在上一周期，当前已是第一个周期");
            }

            // 计算目标周期的起始时间（使用毫秒偏移）
            long cycleStartMillis = startWeekBegin.getTime() + (targetCycleIndex * intervalWeeks * TimeConstants.MILLIS_PER_WEEK);

            // 计算目标周期的结束时间（周期最后一天的23:59:59.999）
            long cycleEndMillis = cycleStartMillis + (intervalWeeks * TimeConstants.MILLIS_PER_WEEK) - 1;

            log.debug("目标周期索引: {}, 周期开始时间戳: {}, 周期结束时间戳: {}", targetCycleIndex, cycleStartMillis, cycleEndMillis);

            return CyclePeriodResultVO.success(targetCycleIndex, cycleStartMillis, cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然周周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然周周期信息异常: " + e.getMessage());
        }
    }

    @Override
    public CyclePeriodResultVO calculateDayCyclePeriod(String timezone, long businessTime, long startTime, int intervalDays) {
        return calculateDayCyclePeriod(timezone, businessTime, startTime, intervalDays, false);
    }

    @Override
    public CyclePeriodResultVO calculateDayCyclePeriod(String timezone, long businessTime, long startTime, int intervalDays, boolean getPreviousCycle) {
        if (intervalDays <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然日周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔天数: {}, 获取上一周期: {}",
                    timezone, businessDatetime, startDatetime, intervalDays, getPreviousCycle);

            // 检查业务时间是否已经到达开始时间（仅在获取当前周期时检查）
            if (!getPreviousCycle && businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将时间调整到当天开始
            DateTime startDayBegin = DateUtil.beginOfDay(startDatetime);
            DateTime businessDayBegin = DateUtil.beginOfDay(businessDatetime);

            log.debug("开始时间当天开始: {}, 业务时间当天开始: {}", startDayBegin, businessDayBegin);

            // 使用毫秒差计算天数差
            long millisDiff = businessDayBegin.getTime() - startDayBegin.getTime();
            long daysDiff = millisDiff / TimeConstants.MILLIS_PER_DAY;

            log.debug("毫秒差: {}, 天数差: {}", millisDiff, daysDiff);

            // 计算当前周期索引（从0开始）
            long currentCycleIndex = daysDiff / intervalDays;

            // 根据参数决定使用当前周期还是上一周期
            long targetCycleIndex = getPreviousCycle ? currentCycleIndex - 1 : currentCycleIndex;

            // 边界检查：上一周期不能小于0
            if (!getPreviousCycle && targetCycleIndex < 0) {
                return CyclePeriodResultVO.failure("不存在上一周期，当前已是第一个周期");
            }

            // 计算目标周期的起始时间（使用毫秒偏移）
            long cycleStartMillis = startDayBegin.getTime() + (targetCycleIndex * intervalDays * TimeConstants.MILLIS_PER_DAY);

            // 计算目标周期的结束时间（当天的23:59:59.999）
            long cycleEndMillis = cycleStartMillis + (intervalDays * TimeConstants.MILLIS_PER_DAY) - 1;

            log.debug("目标周期索引: {}, 周期开始时间戳: {}, 周期结束时间戳: {}", targetCycleIndex, cycleStartMillis, cycleEndMillis);

            return CyclePeriodResultVO.success(targetCycleIndex, cycleStartMillis, cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然日周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然日周期信息异常: " + e.getMessage());
        }
    }

    @Override
    public CyclePeriodResultVO calculateMonthCyclePeriod(String timezone, long businessTime, long startTime, int intervalMonths) {
        return calculateMonthCyclePeriod(timezone, businessTime, startTime, intervalMonths, false);
    }

    @Override
    public CyclePeriodResultVO calculateMonthCyclePeriod(String timezone, long businessTime, long startTime, int intervalMonths, boolean getPreviousCycle) {
        if (intervalMonths <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然月周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔月数: {}, 获取上一周期: {}",
                    timezone, businessDatetime, startDatetime, intervalMonths, getPreviousCycle);

            // 检查业务时间是否已经到达开始时间（仅在获取当前周期时检查）
            if (!getPreviousCycle && businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将时间调整到当月开始
            DateTime startMonthBegin = DateUtil.beginOfMonth(startDatetime);
            DateTime businessMonthBegin = DateUtil.beginOfMonth(businessDatetime);

            log.debug("开始时间当月开始: {}, 业务时间当月开始: {}", startMonthBegin, businessMonthBegin);

            // 计算月数差
            int startYear = startMonthBegin.year();
            int startMonth = startMonthBegin.month() + 1; // hutool月份从0开始
            int businessYear = businessMonthBegin.year();
            int businessMonth = businessMonthBegin.month() + 1;

            long monthsDiff = (businessYear - startYear) * 12L + (businessMonth - startMonth);

            log.debug("月数差: {}", monthsDiff);

            // 计算当前周期索引（从0开始）
            long currentCycleIndex = monthsDiff / intervalMonths;

            // 根据参数决定使用当前周期还是上一周期
            long targetCycleIndex = getPreviousCycle ? currentCycleIndex - 1 : currentCycleIndex;

            // 边界检查：上一周期不能小于0
            if (!getPreviousCycle && targetCycleIndex < 0) {
                return CyclePeriodResultVO.failure("不存在上一周期，当前已是第一个周期");
            }

            // 计算目标周期的起始时间
            DateTime cycleStartTime = DateUtil.offsetMonth(startMonthBegin, (int) (targetCycleIndex * intervalMonths));

            // 计算目标周期的结束时间（周期最后一天的23:59:59.999）
            DateTime nextCycleStartTime = DateUtil.offsetMonth(cycleStartTime, intervalMonths);
            long cycleEndMillis = nextCycleStartTime.getTime() - 1;

            log.debug("目标周期索引: {}, 周期开始时间: {}, 周期结束时间: {}", targetCycleIndex, cycleStartTime, new DateTime(cycleEndMillis));

            return CyclePeriodResultVO.success(targetCycleIndex, cycleStartTime.getTime(), cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然月周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然月周期信息异常: " + e.getMessage());
        }
    }

    @Override
    public CyclePeriodResultVO calculateQuarterCyclePeriod(String timezone, long businessTime, long startTime, int intervalQuarters) {
        return calculateQuarterCyclePeriod(timezone, businessTime, startTime, intervalQuarters, false);
    }

    @Override
    public CyclePeriodResultVO calculateQuarterCyclePeriod(String timezone, long businessTime, long startTime, int intervalQuarters, boolean getPreviousCycle) {
        if (intervalQuarters <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然季度周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔季度数: {}, 获取上一周期: {}",
                    timezone, businessDatetime, startDatetime, intervalQuarters, getPreviousCycle);

            // 检查业务时间是否已经到达开始时间（仅在获取当前周期时检查）
            if (!getPreviousCycle && businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将开始时间调整到所在季度的第一个月开始
            DateTime startQuarterBegin = getQuarterBegin(startDatetime);

            // 将业务时间调整到所在季度的第一个月开始
            DateTime businessQuarterBegin = getQuarterBegin(businessDatetime);

            log.debug("开始时间所在季度开始: {}, 业务时间所在季度开始: {}", startQuarterBegin, businessQuarterBegin);

            // 计算季度差
            int startYear = startQuarterBegin.year();
            int startQuarter = getQuarter(startQuarterBegin);
            int businessYear = businessQuarterBegin.year();
            int businessQuarter = getQuarter(businessQuarterBegin);

            long quartersDiff = (businessYear - startYear) * 4L + (businessQuarter - startQuarter);

            log.debug("季度差: {}", quartersDiff);

            // 计算当前周期索引（从0开始）
            long currentCycleIndex = quartersDiff / intervalQuarters;

            // 根据参数决定使用当前周期还是上一周期
            long targetCycleIndex = getPreviousCycle ? currentCycleIndex - 1 : currentCycleIndex;

            // 边界检查：上一周期不能小于0
            if (!getPreviousCycle && targetCycleIndex < 0) {
                return CyclePeriodResultVO.failure("不存在上一周期，当前已是第一个周期");
            }

            // 计算目标周期的起始时间（按季度偏移）
            DateTime cycleStartTime = offsetQuarter(startQuarterBegin, (int) (targetCycleIndex * intervalQuarters));

            // 计算目标周期的结束时间（周期最后一天的23:59:59.999）
            DateTime nextCycleStartTime = offsetQuarter(cycleStartTime, intervalQuarters);
            long cycleEndMillis = nextCycleStartTime.getTime() - 1;

            log.debug("目标周期索引: {}, 周期开始时间: {}, 周期结束时间: {}", targetCycleIndex, cycleStartTime, new DateTime(cycleEndMillis));

            return CyclePeriodResultVO.success(targetCycleIndex, cycleStartTime.getTime(), cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然季度周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然季度周期信息异常: " + e.getMessage());
        }
    }

    @Override
    public CyclePeriodResultVO calculateYearCyclePeriod(String timezone, long businessTime, long startTime, int intervalYears) {
        return calculateYearCyclePeriod(timezone, businessTime, startTime, intervalYears, false);
    }

    @Override
    public CyclePeriodResultVO calculateYearCyclePeriod(String timezone, long businessTime, long startTime, int intervalYears, boolean getPreviousCycle) {
        if (intervalYears <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然年周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔年数: {}, 获取上一周期: {}",
                    timezone, businessDatetime, startDatetime, intervalYears, getPreviousCycle);

            // 检查业务时间是否已经到达开始时间（仅在获取当前周期时检查）
            if (!getPreviousCycle && businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将时间调整到当年开始（1月1日）
            DateTime startYearBegin = DateUtil.beginOfYear(startDatetime);
            DateTime businessYearBegin = DateUtil.beginOfYear(businessDatetime);

            log.debug("开始时间当年开始: {}, 业务时间当年开始: {}", startYearBegin, businessYearBegin);

            // 计算年数差
            int startYear = startYearBegin.year();
            int businessYear = businessYearBegin.year();
            long yearsDiff = businessYear - startYear;

            log.debug("年数差: {}", yearsDiff);

            // 计算当前周期索引（从0开始）
            long currentCycleIndex = yearsDiff / intervalYears;

            // 根据参数决定使用当前周期还是上一周期
            long targetCycleIndex = getPreviousCycle ? currentCycleIndex - 1 : currentCycleIndex;

            // 边界检查：上一周期不能小于0
            if (!getPreviousCycle && targetCycleIndex < 0) {
                return CyclePeriodResultVO.failure("不存在上一周期，当前已是第一个周期");
            }

            // 计算目标周期的起始时间
            DateTime cycleStartTime = DateUtil.offsetYear(startYearBegin, (int) (targetCycleIndex * intervalYears));

            // 计算目标周期的结束时间（周期最后一天的23:59:59.999）
            DateTime nextCycleStartTime = DateUtil.offsetYear(cycleStartTime, intervalYears);
            long cycleEndMillis = nextCycleStartTime.getTime() - 1;

            log.debug("目标周期索引: {}, 周期开始时间: {}, 周期结束时间: {}", targetCycleIndex, cycleStartTime, new DateTime(cycleEndMillis));

            return CyclePeriodResultVO.success(targetCycleIndex, cycleStartTime.getTime(), cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然年周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然年周期信息异常: " + e.getMessage());
        }
    }

    /**
     * 获取指定时间所在季度的开始时间
     *
     * @param dateTime 指定时间
     * @return 季度开始时间
     */
    private DateTime getQuarterBegin(DateTime dateTime) {
        int month = dateTime.month() + 1; // hutool月份从0开始，转换为1-12
        int quarterStartMonth;

        if (month <= 3) {
            quarterStartMonth = 1; // Q1: 1-3月
        } else if (month <= 6) {
            quarterStartMonth = 4; // Q2: 4-6月
        } else if (month <= 9) {
            quarterStartMonth = 7; // Q3: 7-9月
        } else {
            quarterStartMonth = 10; // Q4: 10-12月
        }

        // 先设置到目标月份，然后获取月初
        DateTime targetMonth = DateUtil.offsetMonth(DateUtil.beginOfYear(dateTime), quarterStartMonth - 1);
        return DateUtil.beginOfMonth(targetMonth);
    }

    /**
     * 获取指定时间所在的季度（1-4）
     *
     * @param dateTime 指定时间
     * @return 季度编号
     */
    private int getQuarter(DateTime dateTime) {
        int month = dateTime.month() + 1; // hutool月份从0开始，转换为1-12
        return (month - 1) / 3 + 1;
    }

    /**
     * 按季度偏移时间
     *
     * @param dateTime 基准时间
     * @param quarters 偏移的季度数
     * @return 偏移后的时间
     */
    private DateTime offsetQuarter(DateTime dateTime, int quarters) {
        // 每个季度3个月
        return DateUtil.offsetMonth(dateTime, quarters * 3);
    }

}
