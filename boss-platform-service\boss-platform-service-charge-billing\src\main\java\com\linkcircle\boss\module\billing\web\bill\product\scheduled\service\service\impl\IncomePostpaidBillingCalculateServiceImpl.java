package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.impl;

import cn.hutool.core.lang.Pair;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PostpaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.PackageRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.enums.BillingProcessStatusEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.IncomePostpaidBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidBillingCalculateService;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidProductServiceBillService;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.AbstractIncomeRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.BillStatusEnum;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-07 16:12
 * @description 后付费出账计算服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomePostpaidBillingCalculateServiceImpl extends AbstractIncomeRateChargeStrategy implements IncomePostpaidBillingCalculateService {

    private final IncomePostpaidProductServiceBillService incomePostpaidProductServiceBillService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return null;
    }

    @Override
    public void processBillingCalculation(IncomePostpaidBillingMessageDTO billingMessage) {
        PostpaidProductServiceIncomeBillDO serviceIncomeBillDO = billingMessage.getServiceIncomeBillDO();
        Long billingRecordId = serviceIncomeBillDO.getProductServiceBillId();

        log.info("开始处理出账计算, billingRecordId: {}, accountId: {}, serviceId: {}",
                billingRecordId, serviceIncomeBillDO.getAccountId(), serviceIncomeBillDO.getServiceId());

        try {
            // 1. 更新出账记录状态为处理中
            if (!markBillingProcessing(billingRecordId)) {
                log.error("更新出账记录状态为处理中失败, billingRecordId: {}", billingRecordId);
                return;
            }

            // 2. 查询使用量数据并计算费用
            boolean calculateSuccess = calculateUsageAndFee(billingMessage);

            if (!calculateSuccess) {
                // 4. 计算失败，更新状态为失败
                markBillingFailed(billingRecordId, "出账计算失败", 0);
                log.error("出账计算失败, billingRecordId: {}", billingRecordId);
            }
        } catch (Exception e) {
            log.error("处理出账计算异常, billingRecordId: {}", billingRecordId, e);
            // 更新状态为失败
            markBillingFailed(billingRecordId, "处理出账计算异常: " + e.getMessage(), 0);
            throw e;
        }
    }

    @Override
    public boolean calculateUsageAndFee(IncomePostpaidBillingMessageDTO billingMessage) {
        PostpaidProductServiceIncomeBillDO serviceIncomeBillDO = billingMessage.getServiceIncomeBillDO();
        AccountSubscriptionsVO subscription = billingMessage.getSubscription();

        Integer billingType = serviceIncomeBillDO.getBillingType();
        Long serviceId = serviceIncomeBillDO.getServiceId();
        String serviceCode = serviceIncomeBillDO.getServiceCode();
        String billingCycle = serviceIncomeBillDO.getBillingCycle();
        Long billingStartTime = serviceIncomeBillDO.getBillingStartTime();
        Long billingEndTime = serviceIncomeBillDO.getBillingEndTime();
        Long accountId = serviceIncomeBillDO.getAccountId();
        BigDecimal taxRate = subscription.getRate();

        log.info("开始计算使用量和费用, serviceId: {}, serviceCode: {}, billingCycle: {}, accountId: {}",
                serviceId, serviceCode, billingCycle, accountId);

        try {
            // 1. 统计使用量（使用SQL统计sum(usage_count)）
            PostpaidIncomeBillDetailDO detailDO = incomePostpaidProductServiceBillService.getTotalUsageByServiceAndTime(
                    billingType, subscription.getId(), serviceCode, billingStartTime, billingEndTime);

            if (Objects.isNull(detailDO) || detailDO.getChargeUsageCount().compareTo(BigDecimal.ZERO) == 0) {
                log.warn("使用量为0, serviceCode: {}, accountId: {}, 时间范围: {} - {}",
                        serviceCode, accountId, billingStartTime, billingEndTime);
                return true;
            }
            BigDecimal chargeUsageCount = detailDO.getChargeUsageCount();
            log.info("统计使用量完成, totalUsage: {}", chargeUsageCount);

            // 2. 根据消耗量计算原价
            OriginalPriceCalculateResponse priceResponse = calculateRateFee(billingMessage, detailDO, serviceIncomeBillDO, taxRate);
            if (!Boolean.TRUE.equals(priceResponse.getSuccess())) {
                log.error("原价计算失败, accountId: {}, serviceId: {}, priceResponse: {}", accountId, serviceId, priceResponse);
                return false;
            }
            // 4. 更新服务账单记录的费用信息
            boolean updateSuccess = updateServiceBillFeeInfo(serviceIncomeBillDO, priceResponse, detailDO);
            if (!updateSuccess) {
                log.error("更新服务账单费用信息失败, billId: {}", serviceIncomeBillDO.getProductServiceBillId());
                return false;
            }

            log.info("使用量和费用计算完成, serviceId: {}, serviceCode: {}, totalUsage: {}, originalPrice: {}",
                    serviceId, serviceCode, chargeUsageCount, priceResponse.getOriginalPrice());
            return true;
        } catch (Exception e) {
            log.error("计算使用量和费用异常, serviceId: {}, serviceCode: {}", serviceId, serviceCode, e);
            return false;
        }
    }

    @Override
    public boolean markBillingProcessing(Long billingRecordId) {
        try {
            boolean success = incomePostpaidProductServiceBillService.updateBillStatus(billingRecordId, BillingProcessStatusEnum.PROCESSING);
            log.info("更新服务账单状态为处理中, billId: {}, 结果: {}", billingRecordId, success);
            return success;
        } catch (Exception e) {
            log.error("更新服务账单状态为处理中异常, billId: {}", billingRecordId, e);
            return false;
        }
    }

    @Override
    public boolean markBillingCompleted(Long billingRecordId) {
        try {
            boolean success = incomePostpaidProductServiceBillService.updateBillStatus(billingRecordId, BillingProcessStatusEnum.COMPLETED);
            log.info("更新服务账单状态为已完成, billId: {}, 结果: {}", billingRecordId, success);
            return success;
        } catch (Exception e) {
            log.error("更新服务账单状态为已完成异常, billId: {}", billingRecordId, e);
            return false;
        }
    }

    @Override
    public boolean markBillingFailed(Long billingRecordId, String errorMessage, Integer retryCount) {
        try {
            boolean success = incomePostpaidProductServiceBillService.updateBillStatus(billingRecordId, BillingProcessStatusEnum.FAILED);
            log.info("更新服务账单状态为失败, billId: {}, errorMessage: {}, retryCount: {}, 结果: {}",
                    billingRecordId, errorMessage, retryCount, success);
            return success;
        } catch (Exception e) {
            log.error("更新服务账单状态为失败异常, billId: {}", billingRecordId, e);
            return false;
        }
    }


    /**
     * 根据消耗量计算原价
     *
     * @param billingMessage      账单消息
     * @param detailDO            统计数据
     * @param serviceIncomeBillDO 服务账单DO
     * @return 原价计算响应
     */
    private OriginalPriceCalculateResponse calculateRateFee(IncomePostpaidBillingMessageDTO billingMessage,
                                                            PostpaidIncomeBillDetailDO detailDO,
                                                            PostpaidProductServiceIncomeBillDO serviceIncomeBillDO,
                                                            BigDecimal taxRate) {
        AccountSubscriptionsVO subscription = billingMessage.getSubscription();
        AccountSubscriptionsVO.Service service = billingMessage.getService();
        CyclePeriodResultVO cyclePeriodResultVO = billingMessage.getCyclePeriodResultVO();
        AccountSubscriptionsVO.Detail detail = billingMessage.getDetail();
        try {
            Integer rateType = service.getChargeType();
            ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(rateType);
            String currency = serviceIncomeBillDO.getCurrency();

            OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
            request.setTotalUsageWithCurrent(detailDO.getChargeUsageCount());
            request.setPreviousUsage(BigDecimal.ZERO);
            request.setCurrentUsage(detailDO.getChargeUsageCount());
            request.setCurrency(serviceIncomeBillDO.getCurrency());
            request.setPaymentOptions(service.getPaymentOptions());
            request.setTaxRate(taxRate);
            request.setCalculateTaxEnabled(true);
            request.setByProportion(detail.getByProportion());
            request.setStartTime(detail.getStartTime());
            request.setEndTime(detail.getEndTime());
            request.setCycleStartTime(cyclePeriodResultVO.getCycleStartTime());
            request.setCycleEndTime(cyclePeriodResultVO.getCycleEndTime());
            convertRateConfig(request, rateTypeEnum, service, currency);

            // 调用原价计算策略
            OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                    rateTypeEnum.name(), request);

            if (response == null || !Boolean.TRUE.equals(response.getSuccess())) {
                log.error("原价计算失败, serviceId: {}, response: {}", serviceIncomeBillDO.getServiceId(), response);
                return OriginalPriceCalculateResponse.fail(ErrorCodeConstants.INCOME_BILL_CALCULATION_FAILED);
            }

            return response;

        } catch (Exception e) {
            log.error("原价计算异常, subscriptionId: {}, serviceId: {}",
                    subscription.getId(), serviceIncomeBillDO.getServiceId(), e);
            return OriginalPriceCalculateResponse.fail(ErrorCodeConstants.INCOME_BILL_CALCULATION_FAILED);
        }
    }

    /**
     * 处理费率对象
     */
    private void convertRateConfig(OriginalPriceCalculateRequest request,
                                   ChargeRateTypeEnum rateTypeEnum,
                                   AccountSubscriptionsVO.Service service,
                                   String currency) {
        switch (rateTypeEnum) {
            case FIXED:
                request.setRateConfig(getFixedRateConfigDTO(service, currency));
                break;
            case TIERED:
                request.setRateConfig(getTierRateConfigDTO(service, currency));
                break;
            case PACKAGE:
                Pair<PackageRateConfigDTO, PackageRateConfigDTO.PackageConfigDTO> pair = getPackageRateConfigDTO(service, currency);
                request.setRateConfig(pair.getKey());
                request.setPackageConfig(pair.getValue());
                break;
            case USAGE:
                request.setRateConfig(getUsageBasedRateConfigDTO(service));
                break;
            default:
                break;
        }
    }

    /**
     * 更新服务账单费用信息
     *
     * @param serviceIncomeBillDO 服务账单DO
     * @param priceResponse       原价计算响应
     * @param detailDO            统计数据
     * @return 是否更新成功
     */
    private boolean updateServiceBillFeeInfo(PostpaidProductServiceIncomeBillDO serviceIncomeBillDO,
                                             OriginalPriceCalculateResponse priceResponse,
                                             PostpaidIncomeBillDetailDO detailDO) {
        try {
            // 更新费用信息
            PostpaidProductServiceIncomeBillDO updateDO = new PostpaidProductServiceIncomeBillDO();
            updateDO.setProductServiceBillId(serviceIncomeBillDO.getProductServiceBillId());
            updateDO.setUsageCount(detailDO.getUsage());
            updateDO.setUsageUnit(priceResponse.getUsageUnit());
            updateDO.setChargeUsageCount(priceResponse.getChargeUsageCount());
            updateDO.setChargeMeasure(priceResponse.getMeasure());
            updateDO.setChargeMeasureUnit(priceResponse.getMeasureUnit());
            updateDO.setChargeMeasureCeil(priceResponse.getMeasureCeil());

            updateDO.setOriginalPrice(priceResponse.getOriginalPrice());
            updateDO.setDiscountedPrice(priceResponse.getDiscountedPrice());
            updateDO.setDiscountAmount(priceResponse.getDiscountAmount());

            updateDO.setAmountWithTax(priceResponse.getAmountWithTax());
            updateDO.setAmountWithoutTax(priceResponse.getAmountWithoutTax());
            updateDO.setRateDetails(JsonUtils.toJsonString(priceResponse.getRateConfig()));
            updateDO.setDiscountDetails(JsonUtils.toJsonString(priceResponse.getCouponList()));
            updateDO.setBillStatus(BillStatusEnum.DRAFT.getStatus());
            updateDO.setBillingStatus(BillingProcessStatusEnum.COMPLETED.getStatus());
            return incomePostpaidProductServiceBillService.updateServiceBillFeeInfo(updateDO);
        } catch (Exception e) {
            log.error("更新服务账单费用信息异常, billId: {}", serviceIncomeBillDO.getProductServiceBillId(), e);
            return false;
        }
    }
}