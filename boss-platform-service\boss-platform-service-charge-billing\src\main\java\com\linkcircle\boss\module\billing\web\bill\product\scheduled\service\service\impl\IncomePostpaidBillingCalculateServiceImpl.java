package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.impl;

import cn.hutool.core.lang.Pair;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PostpaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.PackageRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.enums.BillingProcessStatusEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.IncomePostpaidBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidBillingCalculateService;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidProductServiceBillService;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.AbstractIncomeRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.BillStatusEnum;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-07 16:12
 * @description 后付费出账计算服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomePostpaidBillingCalculateServiceImpl extends AbstractIncomeRateChargeStrategy implements IncomePostpaidBillingCalculateService {

    private final IncomePostpaidProductServiceBillService incomePostpaidProductServiceBillService;
    private final StrategyFactory strategyFactory;
    private final RedissonUtil redissonUtil;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return null;
    }

    @Override
    public void processBillingCalculation(IncomePostpaidBillingMessageDTO billingMessage) {
        PostpaidProductServiceIncomeBillDO serviceIncomeBillDO = billingMessage.getServiceIncomeBillDO();
        Long billingRecordId = serviceIncomeBillDO.getProductServiceBillId();

        try {
            boolean calculateSuccess = calculateUsageAndFee(billingMessage);
            log.info("服务出账计算完成, calculateSuccess: {}, billingRecordId: {}", calculateSuccess, billingRecordId);
        } catch (Exception e) {
            log.error("处理服务出账计算异常, billingRecordId: {}", billingRecordId, e);
            throw e;
        }
    }

    @Override
    public boolean calculateUsageAndFee(IncomePostpaidBillingMessageDTO billingMessage) {
        PostpaidProductServiceIncomeBillDO serviceIncomeBillDO = billingMessage.getServiceIncomeBillDO();
        AccountSubscriptionsVO subscription = billingMessage.getSubscription();

        Integer billingType = serviceIncomeBillDO.getBillingType();
        Long subscribeId = serviceIncomeBillDO.getSubscribeId();
        Long serviceId = serviceIncomeBillDO.getServiceId();
        String serviceCode = serviceIncomeBillDO.getServiceCode();
        String billingCycle = serviceIncomeBillDO.getBillingCycle();
        Long billingStartTime = serviceIncomeBillDO.getBillingStartTime();
        Long billingEndTime = serviceIncomeBillDO.getBillingEndTime();
        Long accountId = serviceIncomeBillDO.getAccountId();
        BigDecimal taxRate = subscription.getRate();

        log.info("开始处理服务出账计算, serviceId: {}, serviceCode: {}, billingCycle: {}, accountId: {}",
                serviceId, serviceCode, billingCycle, accountId);

        // 1. 统计使用量（使用SQL统计sum(usage_count)）
        PostpaidIncomeBillDetailDO detailDO = incomePostpaidProductServiceBillService.getTotalUsageByServiceAndTime(
                billingType, subscription.getId(), getServiceCode(serviceCode, serviceIncomeBillDO.getBillingType()),
                serviceId, billingStartTime, billingEndTime);

        if (Objects.isNull(detailDO) || detailDO.getChargeUsageCount().compareTo(BigDecimal.ZERO) == 0) {
            log.info("使用量为0, serviceCode: {}, accountId: {}, 时间范围: {} - {}",
                    serviceCode, accountId, billingStartTime, billingEndTime);
            String cacheKey = ChargeCacheUtils.getServiceBillCacheKey(serviceId, subscribeId, billingStartTime, billingEndTime);
            redissonUtil.setNx(cacheKey, System.currentTimeMillis(), Duration.ofDays(1));
            return true;
        }

        BigDecimal chargeUsageCount = detailDO.getChargeUsageCount();
        log.info("统计使用量完成, totalUsage: {}", chargeUsageCount);

        // 2. 根据消耗量计算原价
        OriginalPriceCalculateResponse priceResponse = calculateRateFee(billingMessage, detailDO, serviceIncomeBillDO, taxRate);
        if (!Boolean.TRUE.equals(priceResponse.getSuccess())) {
            log.error("原价计算失败, accountId: {}, serviceId: {}, priceResponse: {}", accountId, serviceId, priceResponse);
            return false;
        }
        // 4. 更新服务账单记录的费用信息
        boolean updateSuccess = saveServiceBillFeeInfo(serviceIncomeBillDO, priceResponse, detailDO);
        if (!updateSuccess) {
            log.error("更新服务账单费用信息失败, billId: {}", serviceIncomeBillDO.getProductServiceBillId());
            return false;
        }

        log.info("使用量和费用计算完成, serviceId: {}, serviceCode: {}, totalUsage: {}, originalPrice: {}",
                serviceId, serviceCode, chargeUsageCount, priceResponse.getOriginalPrice());
        return true;
    }

    private String getServiceCode(String serviceCode, Integer billingType) {
        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(billingType);
        switch (rateTypeEnum) {
            case FIXED, TIERED -> {
                return rateTypeEnum.name().toLowerCase();
            }
            case USAGE, PACKAGE -> {
                return serviceCode;
            }
        }
        return "";
    }

    /**
     * 根据消耗量计算原价
     *
     * @param billingMessage      账单消息
     * @param detailDO            统计数据
     * @param serviceIncomeBillDO 服务账单DO
     * @return 原价计算响应
     */
    private OriginalPriceCalculateResponse calculateRateFee(IncomePostpaidBillingMessageDTO billingMessage,
                                                            PostpaidIncomeBillDetailDO detailDO,
                                                            PostpaidProductServiceIncomeBillDO serviceIncomeBillDO,
                                                            BigDecimal taxRate) {
        AccountSubscriptionsVO.Service service = billingMessage.getService();
        CyclePeriodResultVO cyclePeriodResultVO = billingMessage.getCyclePeriodResultVO();
        AccountSubscriptionsVO.Detail detail = billingMessage.getDetail();

        Integer rateType = service.getChargeType();
        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(rateType);
        String currency = serviceIncomeBillDO.getCurrency();

        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setTotalUsageWithCurrent(detailDO.getChargeUsageCount());
        request.setPreviousUsage(BigDecimal.ZERO);
        request.setCurrentUsage(detailDO.getChargeUsageCount());
        request.setCurrency(serviceIncomeBillDO.getCurrency());
        request.setPaymentOptions(service.getPaymentOptions());
        request.setTaxRate(taxRate);
        request.setCalculateTaxEnabled(true);
        request.setByProportion(detail.getByProportion());
        request.setStartTime(detail.getStartTime());
        request.setEndTime(detail.getEndTime());
        request.setCycleStartTime(cyclePeriodResultVO.getCycleStartTime());
        request.setCycleEndTime(cyclePeriodResultVO.getCycleEndTime());
        request.setIgnoreUnit(true);
        convertRateConfig(request, rateTypeEnum, service, currency);

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                rateTypeEnum.name(), request);

        if (response == null || !Boolean.TRUE.equals(response.getSuccess())) {
            log.error("原价计算失败, serviceId: {}, response: {}", serviceIncomeBillDO.getServiceId(), response);
            return OriginalPriceCalculateResponse.fail(ErrorCodeConstants.INCOME_BILL_CALCULATION_FAILED);
        }

        return response;
    }

    /**
     * 处理费率对象
     */
    private void convertRateConfig(OriginalPriceCalculateRequest request,
                                   ChargeRateTypeEnum rateTypeEnum,
                                   AccountSubscriptionsVO.Service service,
                                   String currency) {
        switch (rateTypeEnum) {
            case FIXED:
                request.setRateConfig(getFixedRateConfigDTO(service, currency));
                break;
            case TIERED:
                request.setRateConfig(getTierRateConfigDTO(service, currency));
                break;
            case PACKAGE:
                Pair<PackageRateConfigDTO, PackageRateConfigDTO.PackageConfigDTO> pair = getPackageRateConfigDTO(service, currency);
                request.setRateConfig(pair.getKey());
                request.setPackageConfig(pair.getValue());
                break;
            case USAGE:
                request.setRateConfig(getUsageBasedRateConfigDTO(service));
                break;
            default:
                break;
        }
    }

    /**
     * 保存服务账单费用信息
     *
     * @param serviceIncomeBillDO 服务账单DO
     * @param priceResponse       原价计算响应
     * @param detailDO            统计数据
     * @return 是否更新成功
     */
    private boolean saveServiceBillFeeInfo(PostpaidProductServiceIncomeBillDO serviceIncomeBillDO,
                                           OriginalPriceCalculateResponse priceResponse,
                                           PostpaidIncomeBillDetailDO detailDO) {
        serviceIncomeBillDO.setProductServiceBillId(serviceIncomeBillDO.getProductServiceBillId());
        serviceIncomeBillDO.setUsageCount(detailDO.getUsage());
        serviceIncomeBillDO.setUsageUnit(priceResponse.getUsageUnit());
        serviceIncomeBillDO.setChargeUsageCount(priceResponse.getChargeUsageCount());
        serviceIncomeBillDO.setChargeUnitCount(priceResponse.getChargeUnitCount());
        serviceIncomeBillDO.setChargeMeasure(priceResponse.getMeasure());
        serviceIncomeBillDO.setChargeMeasureUnit(priceResponse.getMeasureUnit());
        serviceIncomeBillDO.setChargeMeasureCeil(priceResponse.getMeasureCeil());

        serviceIncomeBillDO.setOriginalPrice(priceResponse.getOriginalPrice());
        serviceIncomeBillDO.setDiscountedPrice(priceResponse.getDiscountedPrice());
        serviceIncomeBillDO.setDiscountAmount(priceResponse.getDiscountAmount());

        serviceIncomeBillDO.setAmountWithTax(priceResponse.getAmountWithTax());
        serviceIncomeBillDO.setAmountWithoutTax(priceResponse.getAmountWithoutTax());
        serviceIncomeBillDO.setRateDetails(JsonUtils.toJsonString(priceResponse.getRateConfig()));
        serviceIncomeBillDO.setDiscountDetails(JsonUtils.toJsonString(priceResponse.getCouponList()));
        serviceIncomeBillDO.setBillStatus(BillStatusEnum.DRAFT.getStatus());
        serviceIncomeBillDO.setBillingStatus(BillingProcessStatusEnum.COMPLETED.getStatus());
        return incomePostpaidProductServiceBillService.saveServiceBill(serviceIncomeBillDO);
    }
}