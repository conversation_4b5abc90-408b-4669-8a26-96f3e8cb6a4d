package com.linkcircle.boss.module.billing.web.bill.transaction.processor.impl;

import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.BillCallbackDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.IncomePostpaidBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidProductServiceBillService;
import com.linkcircle.boss.module.billing.web.bill.transaction.enums.TransactionMessageTypeEnum;
import com.linkcircle.boss.module.billing.web.bill.transaction.processor.TransactionProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionState;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-29 10:50
 * @description 收入服务计费事务处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IncomeServiceBillingTransactionProcessor implements TransactionProcessor {

    private final IncomePostpaidProductServiceBillService incomePostpaidProductServiceBillService;

    @Override
    public TransactionMessageTypeEnum getSupportedType() {
        return TransactionMessageTypeEnum.INCOME_SERVICE_BILLING;
    }

    /**
     * 执行本地事务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
        try {
            IncomePostpaidBillingMessageDTO context = (IncomePostpaidBillingMessageDTO) arg;
            PostpaidProductServiceIncomeBillDO serviceBill = context.getServiceIncomeBillDO();

            // 执行本地事务：保存单个服务账单
            boolean saved = incomePostpaidProductServiceBillService.saveServiceBill(serviceBill);

            if (saved) {
                log.info("收入服务计费本地事务执行成功, billId: {}", serviceBill.getProductServiceBillId());
                return RocketMQLocalTransactionState.COMMIT;
            }
            log.error("收入服务计费本地事务执行失败, billId: {}", serviceBill.getProductServiceBillId());
            return RocketMQLocalTransactionState.ROLLBACK;
        } catch (Exception e) {
            log.error("收入服务计费本地事务执行异常", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }

    /**
     * 检查本地事务状态（用于消息回查）
     */
    @Override
    public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
        try {
            // 从消息头中获取账单ID
            String billData = (String) msg.getHeaders().get("extra");
            if (StrUtil.isEmpty(billData)) {
                log.error("回查消息中未找到billId");
                return RocketMQLocalTransactionState.ROLLBACK;
            }
            BillCallbackDTO billCallbackDTO = JsonUtils.parseObject(billData, BillCallbackDTO.class);
            if (Objects.isNull(billCallbackDTO)) {
                log.error("回查消息中未找到billId");
                return RocketMQLocalTransactionState.ROLLBACK;
            }
            Long billId = billCallbackDTO.getBillId();
            Long startTime = billCallbackDTO.getStartTime();

            PostpaidProductServiceIncomeBillDO bill = incomePostpaidProductServiceBillService.getBillById(billId, startTime);
            if (Objects.nonNull(bill)) {
                log.info("回查确认：收入服务计费本地事务已提交, billId: {}", billId);
                return RocketMQLocalTransactionState.COMMIT;
            }
            log.info("回查确认：收入服务计费本地事务未提交, billId: {}", billId);
            return RocketMQLocalTransactionState.ROLLBACK;
        } catch (Exception e) {
            log.error("检查收入服务计费本地事务状态异常", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }
}
