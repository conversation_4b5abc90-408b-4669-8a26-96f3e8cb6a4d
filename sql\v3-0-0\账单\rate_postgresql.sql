-- PostgreSQL版本的费率表建表SQL

-- 收入-固定费率周期状态表
drop table if exists income_fixed_rate_cycle_status;
CREATE TABLE income_fixed_rate_cycle_status
(
    id                  BIGINT         NOT NULL,                -- 主键ID
    subscription_id     BIGINT         NOT NULL,                -- 订阅ID
    account_id          BIGINT         NOT NULL,                -- 账户ID
    service_id          BIGINT         NOT NULL,                -- 服务ID
    billing_cycle       VARCHAR(32)    NOT NULL,                -- 计费周期标识，如********-********
    billing_cycle_start BIGINT         NOT NULL,                -- 计费周期开始时间戳
    billing_cycle_end   BIGINT         NOT NULL,                -- 计费周期结束时间戳
    total_usage         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000, -- 周期内累计消耗量
    billing_time        BIGINT                  DEFAULT NULL,   -- 计费时间戳
    currency            VARCHAR(8)     NOT NULL,                -- 货币类型

    -- 基础字段
    create_time         BIGINT         NOT NULL,                -- 创建时间

    CONSTRAINT pk_income_fixed_rate_cycle_status PRIMARY KEY (id),
    CONSTRAINT uk_income_subscription_cycle UNIQUE (service_id, subscription_id, billing_cycle)
);

-- 添加表注释
COMMENT ON TABLE income_fixed_rate_cycle_status IS '收入-固定费率周期状态表';

-- 添加字段注释
COMMENT ON COLUMN income_fixed_rate_cycle_status.id IS '主键ID';
COMMENT ON COLUMN income_fixed_rate_cycle_status.subscription_id IS '订阅ID';
COMMENT ON COLUMN income_fixed_rate_cycle_status.account_id IS '账户ID';
COMMENT ON COLUMN income_fixed_rate_cycle_status.service_id IS '服务ID';
COMMENT ON COLUMN income_fixed_rate_cycle_status.billing_cycle IS '计费周期标识，如********-********';
COMMENT ON COLUMN income_fixed_rate_cycle_status.billing_cycle_start IS '计费周期开始时间戳';
COMMENT ON COLUMN income_fixed_rate_cycle_status.billing_cycle_end IS '计费周期结束时间戳';
COMMENT ON COLUMN income_fixed_rate_cycle_status.billing_time IS '计费时间戳';
COMMENT ON COLUMN income_fixed_rate_cycle_status.currency IS '货币类型';
COMMENT ON COLUMN income_fixed_rate_cycle_status.create_time IS '创建时间';

-- 收入-阶梯费率用量表
drop table if exists income_tier_rate_usage;
CREATE TABLE income_tier_rate_usage
(
    id                  BIGINT         NOT NULL,                -- 主键ID
    subscription_id     BIGINT         NOT NULL,                -- 订阅ID
    account_id          BIGINT         NOT NULL,                -- 账户ID
    service_id          BIGINT         NOT NULL,                -- 服务ID
    billing_cycle       VARCHAR(32)    NOT NULL,                -- 计费周期标识，如********-********
    billing_cycle_start BIGINT         NOT NULL,                -- 计费周期开始时间戳
    billing_cycle_end   BIGINT         NOT NULL,                -- 计费周期结束时间戳
    total_usage         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000, -- 周期内累计消耗量
    usage_unit          VARCHAR(32)    NOT NULL,                -- 用量单位
    billing_time        BIGINT                  DEFAULT NULL,   -- 计费时间戳
    currency            VARCHAR(8)     NOT NULL,                -- 货币类型

    -- 基础字段
    create_time         BIGINT         NOT NULL,                -- 创建时间

    CONSTRAINT pk_income_tier_rate_usage PRIMARY KEY (id),
    CONSTRAINT uk_income_tier_subscription_cycle UNIQUE (service_id, subscription_id, billing_cycle)
);

-- 添加表注释
COMMENT ON TABLE income_tier_rate_usage IS '收入-阶梯费率用量表，记录周期内累计消耗量';

-- 添加字段注释
COMMENT ON COLUMN income_tier_rate_usage.id IS '主键ID';
COMMENT ON COLUMN income_tier_rate_usage.subscription_id IS '订阅ID';
COMMENT ON COLUMN income_tier_rate_usage.account_id IS '账户ID';
COMMENT ON COLUMN income_tier_rate_usage.service_id IS '服务ID';
COMMENT ON COLUMN income_tier_rate_usage.billing_cycle IS '计费周期标识，如********-********';
COMMENT ON COLUMN income_tier_rate_usage.billing_cycle_start IS '计费周期开始时间戳';
COMMENT ON COLUMN income_tier_rate_usage.billing_cycle_end IS '计费周期结束时间戳';
COMMENT ON COLUMN income_tier_rate_usage.total_usage IS '周期内累计消耗量';
COMMENT ON COLUMN income_tier_rate_usage.usage_unit IS '用量单位';
COMMENT ON COLUMN income_tier_rate_usage.billing_time IS '计费时间戳';
COMMENT ON COLUMN income_tier_rate_usage.currency IS '货币类型';
COMMENT ON COLUMN income_tier_rate_usage.create_time IS '创建时间';

-- 收入-套餐费率用量表
drop table if exists income_package_rate_usage;
CREATE TABLE income_package_rate_usage
(
    id                  BIGINT         NOT NULL,                -- 主键ID
    subscription_id     BIGINT         NOT NULL,                -- 订阅ID
    account_id          BIGINT         NOT NULL,                -- 账户ID
    service_id          BIGINT         NOT NULL,                -- 服务ID
    billing_cycle       VARCHAR(32)    NOT NULL,                -- 计费周期标识，如********-********
    billing_cycle_start BIGINT         NOT NULL,                -- 计费周期开始时间戳
    billing_cycle_end   BIGINT         NOT NULL,                -- 计费周期结束时间戳
    total_usage         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000, -- 周期内累计消耗量
    usage_unit          VARCHAR(32)    NOT NULL,                -- 用量单位
    billing_time        BIGINT                  DEFAULT NULL,   -- 计费时间戳
    currency            VARCHAR(8)     NOT NULL,                -- 货币类型

    -- 基础字段
    create_time         BIGINT         NOT NULL,                -- 创建时间

    CONSTRAINT pk_income_package_rate_usage PRIMARY KEY (id),
    CONSTRAINT uk_income_package_subscription_cycle UNIQUE (service_id, subscription_id, billing_cycle)
);

-- 添加表注释
COMMENT ON TABLE income_package_rate_usage IS '收入-套餐费率用量表，记录周期内累计消耗量';

-- 添加字段注释
COMMENT ON COLUMN income_package_rate_usage.id IS '主键ID';
COMMENT ON COLUMN income_package_rate_usage.subscription_id IS '订阅ID';
COMMENT ON COLUMN income_package_rate_usage.account_id IS '账户ID';
COMMENT ON COLUMN income_package_rate_usage.service_id IS '服务ID';
COMMENT ON COLUMN income_package_rate_usage.billing_cycle IS '计费周期标识，如********-********';
COMMENT ON COLUMN income_package_rate_usage.billing_cycle_start IS '计费周期开始时间戳';
COMMENT ON COLUMN income_package_rate_usage.billing_cycle_end IS '计费周期结束时间戳';
COMMENT ON COLUMN income_package_rate_usage.total_usage IS '周期内累计消耗量';
COMMENT ON COLUMN income_package_rate_usage.usage_unit IS '用量单位';
COMMENT ON COLUMN income_package_rate_usage.billing_time IS '计费时间戳';
COMMENT ON COLUMN income_package_rate_usage.currency IS '货币类型';
COMMENT ON COLUMN income_package_rate_usage.create_time IS '创建时间';

-- 收入-按量费率用量表
drop table if exists income_usage_rate_usage;
CREATE TABLE income_usage_rate_usage
(
    id                  BIGINT         NOT NULL,                -- 主键ID
    account_id          BIGINT         NOT NULL,                -- 账户ID
    subscription_id     BIGINT         NOT NULL,                -- 订阅ID
    service_id          BIGINT         NOT NULL,                -- 服务ID
    billing_cycle       VARCHAR(32)    NOT NULL,                -- 账户出账周期标识，如********-********
    billing_cycle_start BIGINT         NOT NULL,                -- 出账周期开始时间戳
    billing_cycle_end   BIGINT         NOT NULL,                -- 出账周期结束时间戳
    total_usage         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000, -- 周期内累计消耗量
    usage_unit          VARCHAR(32)    NOT NULL,                -- 用量单位
    billing_time        BIGINT                  DEFAULT NULL,   -- 计费时间戳
    currency            VARCHAR(8)     NOT NULL,                -- 货币类型

    -- 基础字段
    create_time         BIGINT         NOT NULL,                -- 创建时间

    CONSTRAINT pk_income_usage_rate_usage PRIMARY KEY (id),
    CONSTRAINT uk_income_account_service_cycle UNIQUE (service_id, subscription_id, billing_cycle)
);

-- 添加表注释
COMMENT ON TABLE income_usage_rate_usage IS '收入-按量费率用量表，记录账户出账周期内累计消耗量';

-- 添加字段注释
COMMENT ON COLUMN income_usage_rate_usage.id IS '主键ID';
COMMENT ON COLUMN income_usage_rate_usage.subscription_id IS '订阅ID';
COMMENT ON COLUMN income_usage_rate_usage.account_id IS '账户ID，按量计费以账户为维度';
COMMENT ON COLUMN income_usage_rate_usage.service_id IS '服务ID';
COMMENT ON COLUMN income_usage_rate_usage.billing_cycle IS '账户出账周期标识，如********-********';
COMMENT ON COLUMN income_usage_rate_usage.billing_cycle_start IS '出账周期开始时间戳';
COMMENT ON COLUMN income_usage_rate_usage.billing_cycle_end IS '出账周期结束时间戳';
COMMENT ON COLUMN income_usage_rate_usage.total_usage IS '周期内累计消耗量';
COMMENT ON COLUMN income_usage_rate_usage.usage_unit IS '用量单位';
COMMENT ON COLUMN income_usage_rate_usage.billing_time IS '计费时间戳';
COMMENT ON COLUMN income_usage_rate_usage.currency IS '货币类型';
COMMENT ON COLUMN income_usage_rate_usage.create_time IS '创建时间';

-- 成本-固定费率周期状态表
drop table if exists cost_fixed_rate_cycle_status;
CREATE TABLE cost_fixed_rate_cycle_status
(
    id                  BIGINT         NOT NULL,                -- 主键ID
    purchase_id         BIGINT         NOT NULL,                -- 采购ID
    account_id          BIGINT         NOT NULL,                -- 账户ID
    service_id          BIGINT         NOT NULL,                -- 服务ID
    billing_cycle       VARCHAR(32)    NOT NULL,                -- 计费周期标识，如********-********
    billing_cycle_start BIGINT         NOT NULL,                -- 计费周期开始时间戳
    billing_cycle_end   BIGINT         NOT NULL,                -- 计费周期结束时间戳
    total_usage         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000, -- 周期内累计消耗量
    billing_time        BIGINT                  DEFAULT NULL,   -- 计费时间戳
    currency            VARCHAR(8)     NOT NULL,                -- 货币类型

    -- 基础字段
    create_time         BIGINT         NOT NULL,                -- 创建时间

    CONSTRAINT pk_cost_fixed_rate_cycle_status PRIMARY KEY (id),
    CONSTRAINT uk_cost_purchase_cycle UNIQUE (service_id, purchase_id, billing_cycle)
);

-- 添加表注释
COMMENT ON TABLE cost_fixed_rate_cycle_status IS '成本-固定费率周期状态表';

-- 添加字段注释
COMMENT ON COLUMN cost_fixed_rate_cycle_status.id IS '主键ID';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.purchase_id IS '采购ID';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.account_id IS '账户ID';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.service_id IS '服务ID';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.billing_cycle IS '计费周期标识，如********-********';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.billing_cycle_start IS '计费周期开始时间戳';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.billing_cycle_end IS '计费周期结束时间戳';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.billing_time IS '计费时间戳';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.currency IS '货币类型';
COMMENT ON COLUMN cost_fixed_rate_cycle_status.create_time IS '创建时间';

-- 成本-阶梯费率用量表
drop table if exists cost_tier_rate_usage;
CREATE TABLE cost_tier_rate_usage
(
    id                  BIGINT         NOT NULL,                -- 主键ID
    purchase_id         BIGINT         NOT NULL,                -- 采购ID
    account_id          BIGINT         NOT NULL,                -- 账户ID
    service_id          BIGINT         NOT NULL,                -- 服务ID
    billing_cycle       VARCHAR(32)    NOT NULL,                -- 计费周期标识，如********-********
    billing_cycle_start BIGINT         NOT NULL,                -- 计费周期开始时间戳
    billing_cycle_end   BIGINT         NOT NULL,                -- 计费周期结束时间戳
    total_usage         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000, -- 周期内累计消耗量
    usage_unit          VARCHAR(32)    NOT NULL,                -- 用量单位
    billing_time        BIGINT                  DEFAULT NULL,   -- 计费时间戳
    currency            VARCHAR(8)     NOT NULL,                -- 货币类型

    -- 基础字段
    create_time         BIGINT         NOT NULL,                -- 创建时间

    CONSTRAINT pk_cost_tier_rate_usage PRIMARY KEY (id),
    CONSTRAINT uk_cost_tier_purchase_cycle UNIQUE (service_id, purchase_id, billing_cycle)
);

-- 添加表注释
COMMENT ON TABLE cost_tier_rate_usage IS '成本-阶梯费率用量表，记录周期内累计消耗量';

-- 添加字段注释
COMMENT ON COLUMN cost_tier_rate_usage.id IS '主键ID';
COMMENT ON COLUMN cost_tier_rate_usage.purchase_id IS '采购ID';
COMMENT ON COLUMN cost_tier_rate_usage.account_id IS '账户ID';
COMMENT ON COLUMN cost_tier_rate_usage.service_id IS '服务ID';
COMMENT ON COLUMN cost_tier_rate_usage.billing_cycle IS '计费周期标识，如********-********';
COMMENT ON COLUMN cost_tier_rate_usage.billing_cycle_start IS '计费周期开始时间戳';
COMMENT ON COLUMN cost_tier_rate_usage.billing_cycle_end IS '计费周期结束时间戳';
COMMENT ON COLUMN cost_tier_rate_usage.total_usage IS '周期内累计消耗量';
COMMENT ON COLUMN cost_tier_rate_usage.usage_unit IS '用量单位';
COMMENT ON COLUMN cost_tier_rate_usage.billing_time IS '计费时间戳';
COMMENT ON COLUMN cost_tier_rate_usage.currency IS '货币类型';
COMMENT ON COLUMN cost_tier_rate_usage.create_time IS '创建时间';

-- 成本-套餐费率用量表
drop table if exists cost_package_rate_usage;
CREATE TABLE cost_package_rate_usage
(
    id                  BIGINT         NOT NULL,                -- 主键ID
    purchase_id         BIGINT         NOT NULL,                -- 采购ID
    account_id          BIGINT         NOT NULL,                -- 账户ID
    service_id          BIGINT         NOT NULL,                -- 服务ID
    billing_cycle       VARCHAR(32)    NOT NULL,                -- 计费周期标识，如********-********
    billing_cycle_start BIGINT         NOT NULL,                -- 计费周期开始时间戳
    billing_cycle_end   BIGINT         NOT NULL,                -- 计费周期结束时间戳
    total_usage         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000, -- 周期内累计消耗量
    usage_unit          VARCHAR(32)    NOT NULL,                -- 用量单位
    billing_time        BIGINT                  DEFAULT NULL,   -- 计费时间戳
    currency            VARCHAR(8)     NOT NULL,                -- 货币类型

    -- 基础字段
    create_time         BIGINT         NOT NULL,                -- 创建时间

    CONSTRAINT pk_cost_package_rate_usage PRIMARY KEY (id),
    CONSTRAINT uk_cost_package_purchase_cycle UNIQUE (service_id, purchase_id, billing_cycle)
);

-- 添加表注释
COMMENT ON TABLE cost_package_rate_usage IS '成本-套餐费率用量表，记录周期内累计消耗量';

-- 添加字段注释
COMMENT ON COLUMN cost_package_rate_usage.id IS '主键ID';
COMMENT ON COLUMN cost_package_rate_usage.purchase_id IS '采购ID';
COMMENT ON COLUMN cost_package_rate_usage.account_id IS '账户ID';
COMMENT ON COLUMN cost_package_rate_usage.service_id IS '服务ID';
COMMENT ON COLUMN cost_package_rate_usage.billing_cycle IS '计费周期标识，如********-********';
COMMENT ON COLUMN cost_package_rate_usage.billing_cycle_start IS '计费周期开始时间戳';
COMMENT ON COLUMN cost_package_rate_usage.billing_cycle_end IS '计费周期结束时间戳';
COMMENT ON COLUMN cost_package_rate_usage.total_usage IS '周期内累计消耗量';
COMMENT ON COLUMN cost_package_rate_usage.usage_unit IS '用量单位';
COMMENT ON COLUMN cost_package_rate_usage.billing_time IS '计费时间戳';
COMMENT ON COLUMN cost_package_rate_usage.currency IS '货币类型';
COMMENT ON COLUMN cost_package_rate_usage.create_time IS '创建时间';

-- 成本-按量费率用量表
drop table if exists cost_usage_rate_usage;
CREATE TABLE cost_usage_rate_usage
(
    id                  BIGINT         NOT NULL,                -- 主键ID
    purchase_id         BIGINT         NOT NULL,                -- 采购ID
    account_id          BIGINT         NOT NULL,                -- 账户ID
    service_id          BIGINT         NOT NULL,                -- 服务ID
    billing_cycle       VARCHAR(32)    NOT NULL,                -- 账户出账周期标识，如********-********
    billing_cycle_start BIGINT         NOT NULL,                -- 出账周期开始时间戳
    billing_cycle_end   BIGINT         NOT NULL,                -- 出账周期结束时间戳
    total_usage         DECIMAL(15, 4) NOT NULL DEFAULT 0.0000, -- 周期内累计消耗量
    usage_unit          VARCHAR(32)    NOT NULL,                -- 用量单位
    billing_time        BIGINT                  DEFAULT NULL,   -- 计费时间戳
    currency            VARCHAR(8)     NOT NULL,                -- 货币类型

    -- 基础字段
    create_time         BIGINT         NOT NULL,                -- 创建时间

    CONSTRAINT pk_cost_usage_rate_usage PRIMARY KEY (id),
    CONSTRAINT uk_cost_account_service_cycle UNIQUE (service_id, purchase_id, billing_cycle)
);

-- 添加表注释
COMMENT ON TABLE cost_usage_rate_usage IS '成本-按量费率用量表，记录账户出账周期内累计消耗量';

-- 添加字段注释
COMMENT ON COLUMN cost_usage_rate_usage.id IS '主键ID';
COMMENT ON COLUMN cost_usage_rate_usage.purchase_id IS '采购ID';
COMMENT ON COLUMN cost_usage_rate_usage.account_id IS '账户ID';
COMMENT ON COLUMN cost_usage_rate_usage.service_id IS '服务ID';
COMMENT ON COLUMN cost_usage_rate_usage.billing_cycle IS '账户出账周期标识，如********-********';
COMMENT ON COLUMN cost_usage_rate_usage.billing_cycle_start IS '出账周期开始时间戳';
COMMENT ON COLUMN cost_usage_rate_usage.billing_cycle_end IS '出账周期结束时间戳';
COMMENT ON COLUMN cost_usage_rate_usage.total_usage IS '周期内累计消耗量';
COMMENT ON COLUMN cost_usage_rate_usage.usage_unit IS '用量单位';
COMMENT ON COLUMN cost_usage_rate_usage.billing_time IS '计费时间戳';
COMMENT ON COLUMN cost_usage_rate_usage.currency IS '货币类型';
COMMENT ON COLUMN cost_usage_rate_usage.create_time IS '创建时间';
