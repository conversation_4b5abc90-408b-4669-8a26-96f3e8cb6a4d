package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.consumer;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.IncomePostpaidBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidBillingCalculateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-07 19:10
 * @description 后付费出账事务消息消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = ChargeTopicConstant.CHARGE_TRANSACTION_TOPIC,
        selectorExpression = ChargeTopicConstant.TAG_INCOME_BILL_SERVICE_OUT,
        consumerGroup = ChargeTopicConstant.GROUP_INCOME_BILL_SERVICE_OUT,
        maxReconsumeTimes = 16
)
public class IncomePostpaidServiceBillingTransactionConsumer implements RocketMQListener<IncomePostpaidBillingMessageDTO> {

    private final IncomePostpaidBillingCalculateService incomePostpaidBillingCalculateService;

    @Override
    public void onMessage(IncomePostpaidBillingMessageDTO message) {
        try {
            log.info("=== 收到后付费出账事务消息开始处理 ===");
            log.info("消息内容: {}", message);
            log.info("开始调用业务处理服务...");
            incomePostpaidBillingCalculateService.processBillingCalculation(message);
            log.info("=== 后付费出账事务消息处理完成 ===");
        } catch (Exception e) {
            log.error("=== 处理后付费出账事务消息异常 ===", e);
            log.error("异常消息: {}", e.getMessage());
            log.error("消息内容: {}", message);
            // TODO 注意：事务消息消费失败会导致消息重试
            // 如果是业务异常，可以考虑记录日志后正常返回，避免无限重试
            throw new RuntimeException("处理出账消息失败", e);
        }
    }

}
