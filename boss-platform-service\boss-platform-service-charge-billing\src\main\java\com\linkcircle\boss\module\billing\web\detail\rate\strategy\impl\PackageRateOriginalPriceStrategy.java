package com.linkcircle.boss.module.billing.web.detail.rate.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.linkcircle.boss.module.billing.api.rate.model.dto.PackageRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-24 13:43
 * @description 套餐费率原价计算策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = OriginalPriceRateTypeEnum.class, strategyType = RateTypeConstant.PACKAGE)
public class PackageRateOriginalPriceStrategy extends AbstractOriginalPriceStrategy implements IStrategy<OriginalPriceCalculateRequest, OriginalPriceCalculateResponse> {

    @Override
    public OriginalPriceCalculateResponse execute(OriginalPriceCalculateRequest request) {
        PackageRateConfigDTO rateConfig = (PackageRateConfigDTO) request.getRateConfig();
        PackageRateConfigDTO.PackageConfigDTO packageConfig = (PackageRateConfigDTO.PackageConfigDTO) request.getPackageConfig();

        convertUsageUnit(request, rateConfig.getMeasureUnit());
        OriginalPriceCalculateResponse response = calculateOriginalPrice(request, rateConfig, packageConfig);
        response.setChargeUsageCount(response.getMeasure().multiply(response.getChargeUnitCount()));
        inTrial(request, response);

        calculateTax(request, response);
        response.setRateConfig(rateConfig);
        response.setCouponList(request.getCouponList());

        response.setDiscountAmount(response.getOriginalPrice().subtract(response.getDiscountedPrice()));
        return response;
    }

    /**
     * 计算目录价（套餐计费，根据支付方式选择现金或积分）
     *
     * @param rateConfig    套餐费率配置
     * @param packageConfig 套餐配置
     * @return 本次计费金额
     */
    private OriginalPriceCalculateResponse calculateOriginalPrice(OriginalPriceCalculateRequest request,
                                                                  PackageRateConfigDTO rateConfig,
                                                                  PackageRateConfigDTO.PackageConfigDTO packageConfig) {
        BigDecimal totalUsageWithCurrent = request.getTotalUsageWithCurrent();
        BigDecimal previousUsage = request.getPreviousUsage();
        BigDecimal currentUsage = request.getCurrentUsage();
        String currentUsageUnit = request.getCurrentUsageUnit();

        BigDecimal packageInclude = rateConfig.getPackageInclude();
        String packageIncludeUnit = rateConfig.getPackageIncludeUnit();
        String measureUnit = rateConfig.getMeasureUnit();
        log.info("开始计算套餐费率 - 之前累计用量: {} {}, 本次用量: {} {}, 总累计用量: {} {}, 套餐包含量: {} {}",
                previousUsage, measureUnit, currentUsage, currentUsageUnit, totalUsageWithCurrent, measureUnit,
                packageInclude, packageIncludeUnit);

        // 单位转化
        BigDecimal actualCurrentUsage = convertUnit(currentUsage, currentUsageUnit, rateConfig.getMeasureUnit());
        OriginalPriceCalculateResponse response = OriginalPriceCalculateResponse.success();
        response.setUsage(actualCurrentUsage);
        response.setUsageUnit(rateConfig.getMeasureUnit());
        response.setOriginalPrice(BigDecimal.ZERO);
        response.setDiscountedPrice(BigDecimal.ZERO);
        response.setMeasure(rateConfig.getMeasure());
        response.setMeasureUnit(rateConfig.getMeasureUnit());
        response.setMeasureCeil(rateConfig.getMeasureCeil());

        // 1. 套餐内用量完全免费
        // 只有当总累计用量超过套餐包含量时，才开始计费 单位转为和计量单位一致!!!
        BigDecimal packageIncludeMeasure = convertUnit(packageInclude, packageIncludeUnit, rateConfig.getMeasureUnit());
        if (totalUsageWithCurrent.compareTo(packageIncludeMeasure) <= 0) {
            // 当前周期使用总量在套餐内，不扣钱，当前这次费用为0
            log.info("当前周期使用总量 {} 在套餐包含量 {} 内，本次费用为0", totalUsageWithCurrent, packageIncludeMeasure);
            PackageRateConfigDTO.InPackageRateDTO inPackage = packageConfig.getInPackage();
            inPackage.setUsage(currentUsage);
            inPackage.setUsageUnit(rateConfig.getMeasureUnit());
            inPackage.setOriginalPrice(BigDecimal.ZERO);
            inPackage.setOriginalUnitPrice(BigDecimal.ZERO);
            inPackage.setDiscountedPrice(BigDecimal.ZERO);
            inPackage.setDiscountedUnitPrice(BigDecimal.ZERO);
            inPackage.setInPackage(true);

            response.setOriginalPrice(BigDecimal.ZERO);
            response.setDiscountedPrice(BigDecimal.ZERO);
            response.setOriginalUnitPrice(BigDecimal.ZERO);
            response.setDiscountedUnitPrice(BigDecimal.ZERO);
            response.setInPackage(true);

            BigDecimal units = roundUnit(actualCurrentUsage, rateConfig.getMeasure(), rateConfig.getMeasureCeil());
            response.setChargeUnitCount(units);
            inPackage.setChargeUnitCount(units);
            inPackage.setIsHit(true);
            return response;
        }

        // 2. 计算套餐外费用
        // 当总累计用量超过套餐包含量时，需要计算套餐外费用
        log.info("当前周期使用总量 {} {} 超过套餐包含量 {} {}，开始计算套餐外费用", totalUsageWithCurrent, measureUnit, packageIncludeMeasure, measureUnit);

        // 计算本次用量中超出套餐包含量的部分
        BigDecimal overageUsage = calculateOverageUsageForCurrentBilling(
                totalUsageWithCurrent, previousUsage, actualCurrentUsage, packageIncludeMeasure);

        if (overageUsage.compareTo(BigDecimal.ZERO) > 0) {
            // 计算之前累计套餐外用量和总累计套餐外用量
            BigDecimal previousOverageUsage = previousUsage.compareTo(packageIncludeMeasure) > 0 ?
                    previousUsage.subtract(packageIncludeMeasure) : BigDecimal.ZERO;
            BigDecimal totalOverageUsage = totalUsageWithCurrent.subtract(packageIncludeMeasure);

            calculateOveragePrice(request, response, rateConfig, packageConfig.getOutPackage(),
                    overageUsage, totalOverageUsage, previousOverageUsage);
        }
        return response;
    }

    /**
     * 计算本次计费中的套餐外用量
     *
     * @param totalUsageWithCurrent 包含本次用量的总累计用量
     * @param previousUsage         之前的累计用量
     * @param currentUsage          本次用量
     * @param packageInclude        套餐包含量
     * @return 本次计费中的套餐外用量
     */
    private BigDecimal calculateOverageUsageForCurrentBilling(BigDecimal totalUsageWithCurrent,
                                                              BigDecimal previousUsage,
                                                              BigDecimal currentUsage,
                                                              BigDecimal packageInclude) {
        // 如果之前累计用量已经超过套餐包含量，则本次用量全部为套餐外用量
        if (previousUsage.compareTo(packageInclude) >= 0) {
            return currentUsage;
        }

        // 如果总累计用量超过套餐包含量，则计算本次用量中超出套餐包含量的部分
        if (totalUsageWithCurrent.compareTo(packageInclude) > 0) {
            // 本次用量中超出套餐包含量的部分 = 总累计用量 - 套餐包含量
            // 但不能超过本次用量
            BigDecimal overageFromTotal = totalUsageWithCurrent.subtract(packageInclude);
            return overageFromTotal.min(currentUsage);
        }

        // 否则没有套餐外用量
        return BigDecimal.ZERO;
    }

    /**
     * 计算套餐外费用（根据支付方式选择现金或积分）
     */
    private void calculateOveragePrice(OriginalPriceCalculateRequest request,
                                       OriginalPriceCalculateResponse response,
                                       PackageRateConfigDTO rateConfig,
                                       PackageRateConfigDTO.OutPackageRateDTO outPackage,
                                       BigDecimal overageUsage,
                                       BigDecimal totalOverageUsage,
                                       BigDecimal previousOverageUsage) {
        Integer paymentOptions = request.getPaymentOptions();
        List<Coupon> couponList = request.getCouponList();
        if (outPackage == null) {
            return;
        }

        String priceModel = outPackage.getOutPackagePriceModel();

        if ("fixed".equals(priceModel)) {
            // 固定价格模式
            PackageRateConfigDTO.FixedOutPackageRateDTO fixedRate = outPackage.getFixRatePricesOutPackage();
            if (fixedRate != null) {
                BigDecimal measure = rateConfig.getMeasure();
                Integer measureCeil = rateConfig.getMeasureCeil();
                fixedRate.setUsage(overageUsage);
                fixedRate.setUsageUnit(rateConfig.getMeasureUnit());
                // 计量单位计算
                BigDecimal units = roundUnit(overageUsage, measure, measureCeil);

                // 目录价
                BigDecimal originalUnitPrice = getUnitPrice(paymentOptions, fixedRate.getFixCharge(), fixedRate.getIntegralCharge());
                BigDecimal originalPrice = getTotalPrice(paymentOptions, units, originalUnitPrice);
                fixedRate.setOriginalUnitPrice(originalUnitPrice);
                fixedRate.setOriginalPrice(originalPrice);

                // 优惠价
                BigDecimal discountUnitPrice = calculateDiscountPrice(originalPrice, false, couponList);
                BigDecimal discountPrice = getTotalPrice(paymentOptions, units, discountUnitPrice);
                fixedRate.setDiscountedUnitPrice(discountUnitPrice);
                fixedRate.setDiscountedPrice(discountPrice);

                fixedRate.setChargeUnitCount(units);
                fixedRate.setIsHit(true);

                log.debug("固定价格 套餐外用量: {}, 之前套餐外用量: {}, 总套餐外用量: {}, 套餐外, 目录价: {}, 优惠价: {}",
                        overageUsage, previousOverageUsage, totalOverageUsage, originalPrice, discountPrice);
                response.setOriginalPrice(originalPrice);
                response.setDiscountedPrice(discountPrice);
                response.setChargeUnitCount(units);
                return;
            }
        }
        if ("level".equals(priceModel)) {
            // 阶梯价格模式
            calculateStairOveragePrice(request, response, rateConfig, outPackage, overageUsage,
                    totalOverageUsage, previousOverageUsage);
        }

    }

    /**
     * 计算阶梯套餐外费用（根据支付方式选择现金或积分）
     *
     * @param overageUsage         本次套餐外用量
     * @param totalOverageUsage    总累计套餐外用量（包含本次）
     * @param previousOverageUsage 之前累计套餐外用量
     */
    private void calculateStairOveragePrice(OriginalPriceCalculateRequest request,
                                            OriginalPriceCalculateResponse response,
                                            PackageRateConfigDTO rateConfig,
                                            PackageRateConfigDTO.OutPackageRateDTO outPackage,
                                            BigDecimal overageUsage,
                                            BigDecimal totalOverageUsage,
                                            BigDecimal previousOverageUsage) {
        List<PackageRateConfigDTO.StairOutPackageRateDTO> stairRates = outPackage.getStairRatePricesOutPackage();
        if (CollUtil.isEmpty(stairRates)) {
            return;
        }

        Integer paymentOptions = request.getPaymentOptions();
        List<Coupon> couponList = request.getCouponList();
        BigDecimal measure = rateConfig.getMeasure();
        Integer measureCeil = rateConfig.getMeasureCeil();

        log.info("开始计算套餐外阶梯费用 - 之前套餐外用量: {}, 本次套餐外用量: {} {}, 套餐外总用量: {} {}",
                previousOverageUsage, overageUsage, rateConfig.getMeasureUnit(), totalOverageUsage, rateConfig.getMeasureUnit());

        BigDecimal originalPriceTotal = BigDecimal.ZERO;
        BigDecimal discountPriceTotal = BigDecimal.ZERO;

        BigDecimal processedUsage = previousOverageUsage;
        BigDecimal remainingUsage = overageUsage;

        // 按阶梯计算价格
        BigDecimal chargeUnitCount = BigDecimal.ZERO;
        String packageIncludeUnit = rateConfig.getPackageIncludeUnit();
        String measureUnit = rateConfig.getMeasureUnit();
        for (PackageRateConfigDTO.StairOutPackageRateDTO stairRate : stairRates) {
            if (remainingUsage.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            BigDecimal tierMin = convertUnit(stairRate.getMin(), packageIncludeUnit, measureUnit);
            BigDecimal tierMax = stairRate.getMax();

            // 处理无限大的情况（max为-1）
            if (tierMax.compareTo(BigDecimal.valueOf(-1)) == 0) {
                tierMax = BigDecimal.valueOf(Long.MAX_VALUE);
            } else {
                tierMax = convertUnit(stairRate.getMax(), packageIncludeUnit, measureUnit);
            }

            log.info("处理套餐外阶梯 [{} - {} {}], 已处理用量: {} {}, 剩余用量: {} {}",
                    tierMin, tierMax, measureUnit, processedUsage, measureUnit, remainingUsage, measureUnit);

            // 判断当前阶梯是否与本次套餐外用量有交集
            if (processedUsage.compareTo(tierMax) >= 0) {
                // 已处理用量已超过当前阶梯上限，跳过
                continue;
            }

            // 计算本次套餐外用量在当前阶梯中的部分
            BigDecimal tierUsageStart = processedUsage.max(tierMin);
            BigDecimal tierUsageEnd = totalOverageUsage.min(tierMax);
            BigDecimal usageInThisTier = tierUsageEnd.subtract(tierUsageStart).max(BigDecimal.ZERO);

            if (usageInThisTier.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            log.info("本次套餐外用量在阶梯 [{} - {} {}] 中消费: {} {}", tierMin, tierMax, measureUnit, usageInThisTier, measureUnit);

            // 向上取整
            BigDecimal units = roundUnit(usageInThisTier, measure, measureCeil);
            chargeUnitCount = chargeUnitCount.add(units);

            // 根据是否全额支付和支付方式计算价格
            Pair<BigDecimal, BigDecimal> pricePair = calculateStairTierAmount(measure, measureCeil, rateConfig, stairRate,
                    units, usageInThisTier, paymentOptions, previousOverageUsage, tierMin, tierMax, couponList);

            originalPriceTotal = originalPriceTotal.add(pricePair.getKey());
            discountPriceTotal = discountPriceTotal.add(pricePair.getValue());

            processedUsage = tierUsageEnd;
            remainingUsage = remainingUsage.subtract(usageInThisTier);
        }
        log.info("套餐外阶梯费用计算完成 - 本次套餐外用量: {}, 目录价: {}, 优惠价: {}", overageUsage, originalPriceTotal, discountPriceTotal);
        response.setDiscountedPrice(discountPriceTotal);
        response.setOriginalPrice(originalPriceTotal);
        response.setChargeUnitCount(chargeUnitCount);
    }

    /**
     * 计算套餐外单个阶梯的费用
     */
    private Pair<BigDecimal, BigDecimal> calculateStairTierAmount(BigDecimal measure,
                                                                  Integer measureCeil,
                                                                  PackageRateConfigDTO rateConfig,
                                                                  PackageRateConfigDTO.StairOutPackageRateDTO tierPrice,
                                                                  BigDecimal units,
                                                                  BigDecimal usageInThisTier,
                                                                  Integer paymentOptions,
                                                                  BigDecimal previousOverageUsage,
                                                                  BigDecimal tierMin,
                                                                  BigDecimal tierMax,
                                                                  List<Coupon> couponList) {
        // 如果有配置全额支付
        if (tierPrice.getIsAllPay() != null && tierPrice.getIsAllPay() == 1) {
            return calculateAllPayStairTierAmount(measure, measureCeil, rateConfig, tierPrice, usageInThisTier, paymentOptions,
                    previousOverageUsage, tierMin, couponList);
        }

        // 按用量计算：支付类型的单价 * 单位数
        // 根据支付方式计算目录价
        BigDecimal unitPrice = getUnitPrice(paymentOptions, tierPrice.getFixCharge(), tierPrice.getIntegralCharge());
        BigDecimal originalPrice = getTotalPrice(paymentOptions, units, unitPrice);
        tierPrice.setOriginalUnitPrice(unitPrice);
        tierPrice.setOriginalPrice(originalPrice);

        // 优惠价
        BigDecimal discountUnitPrice = calculateDiscountPrice(unitPrice, false, couponList);
        BigDecimal discountPrice = getTotalPrice(paymentOptions, units, discountUnitPrice);
        tierPrice.setDiscountedUnitPrice(discountUnitPrice);
        tierPrice.setDiscountedPrice(discountPrice);

        log.info("套餐外阶梯 [{} - {}] 计费: 用量={}, 单位数={}, 实际单位数={}, 目录价: {}, 优惠价: {}",
                tierMin, tierMax, usageInThisTier, units, units, originalPrice, discountPrice);
        tierPrice.setChargeUnitCount(units);
        tierPrice.setIsHit(true);
        return Pair.of(originalPrice, discountPrice);
    }

    /**
     * 计算套餐外全额支付阶梯的费用（参考阶梯费率逻辑）
     *
     * @param tierPrice            套餐外阶梯费率配置
     * @param usageInThisTier      在当前阶梯的用量
     * @param paymentOptions       支付方式 0-现金 1-积分
     * @param previousOverageUsage 之前累计套餐外用量
     * @param tierMin              当前阶梯下限
     * @return 该阶梯的费用
     */
    private Pair<BigDecimal, BigDecimal> calculateAllPayStairTierAmount(BigDecimal measure,
                                                                        Integer measureCeil,
                                                                        PackageRateConfigDTO rateConfig,
                                                                        PackageRateConfigDTO.StairOutPackageRateDTO tierPrice,
                                                                        BigDecimal usageInThisTier,
                                                                        Integer paymentOptions,
                                                                        BigDecimal previousOverageUsage,
                                                                        BigDecimal tierMin,
                                                                        List<Coupon> couponList) {

        // 判断之前的套餐外用量是否已经进入过该阶梯
        boolean hasEnteredThisTierBefore = previousOverageUsage.compareTo(tierMin) > 0;

        if (hasEnteredThisTierBefore) {
            // 之前已经进入过该套餐外阶梯，说明已经付过全额费用，本次免费
            log.info("[全额支付] 套餐外阶梯{}之前已付费，本次使用免费", tierPrice.getTierLevel());
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 第一次进入该套餐外阶梯，需要按全额支付计算
        // 计算阶梯的范围大小（左开右闭区间：max - min）
        String packageIncludeUnit = rateConfig.getPackageIncludeUnit();
        String measureUnit = rateConfig.getMeasureUnit();
        BigDecimal tierRangeSize;
        if (tierPrice.getMax().compareTo(BigDecimal.valueOf(-1)) == 0) {
            // 处理无限大(值为-1)的情况，如果是无限大阶梯，则按实际用量计算
            tierRangeSize = convertUnit(usageInThisTier, packageIncludeUnit, measureUnit);
        } else {
            // 阶梯范围大小 = max - min（左开右闭区间）
            tierRangeSize = convertUnit(tierPrice.getMax().subtract(tierPrice.getMin()), packageIncludeUnit, measureUnit);
        }

        // 计算阶梯范围大小对应的单位数
        //  计量单位数2 = 实际使用量31 / 计量单位30 向上取整
        BigDecimal units = roundUnit(tierRangeSize, measure, measureCeil);
        // 计量单位数2 /每单位1 = 2个价格单位
        BigDecimal perPayUnit = units.divide(BigDecimal.valueOf(tierPrice.getPayUnit()), 2, RoundingMode.UP);
        log.info("[全额支付] 套餐外, 阶梯范围: {}, 计量单位: {}, 是否向上取整: {}, 计量单位数: {}, 每单位: {}, 价格单位数: {}",
                tierRangeSize, measure, measureCeil, units, tierPrice.getPayUnit(), perPayUnit);

        // 根据支付方式计算目录价
        BigDecimal originalUnitPrice = getUnitPrice(paymentOptions, tierPrice.getFixCharge(), tierPrice.getIntegralCharge());
        BigDecimal originalPrice = getTotalPrice(paymentOptions, perPayUnit, originalUnitPrice);
        tierPrice.setOriginalUnitPrice(originalUnitPrice);
        tierPrice.setOriginalPrice(originalPrice);

        // 优惠价
        BigDecimal discountUnitPrice = calculateDiscountPrice(originalUnitPrice, false, couponList);
        BigDecimal discountPrice = getTotalPrice(paymentOptions, perPayUnit, discountUnitPrice);
        tierPrice.setDiscountedUnitPrice(discountUnitPrice);
        tierPrice.setDiscountedPrice(discountPrice);
        tierPrice.setChargeUnitCount(perPayUnit);
        tierPrice.setIsHit(true);

        log.info("[全额支付] 套餐外阶梯 {} 首次进入，阶梯范围[{}-{} {}]，范围大小: {} {}，价格单位数: {}, 目录价: {}, 优惠价: {}",
                tierPrice.getTierLevel(), tierPrice.getMin(), tierPrice.getMax(), packageIncludeUnit, tierRangeSize,
                measureUnit, perPayUnit, originalPrice, discountPrice);
        return Pair.of(originalPrice, discountPrice);
    }
}
