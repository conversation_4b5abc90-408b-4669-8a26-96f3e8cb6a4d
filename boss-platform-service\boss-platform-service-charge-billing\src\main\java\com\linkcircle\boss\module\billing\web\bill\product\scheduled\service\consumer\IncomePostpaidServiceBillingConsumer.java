package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.consumer;

import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.module.billing.exception.ScaleTableNotBindException;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.IncomePostpaidBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidBillingCalculateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-07 19:10
 * @description 后付费服务出账消息消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = ChargeTopicConstant.CHARGE_NORMAL_TOPIC,
        selectorExpression = ChargeTopicConstant.TAG_INCOME_BILL_SERVICE_OUT,
        consumerGroup = ChargeTopicConstant.GROUP_INCOME_BILL_SERVICE_OUT,
        maxReconsumeTimes = 16
)
public class IncomePostpaidServiceBillingConsumer implements RocketMQListener<IncomePostpaidBillingMessageDTO> {

    private final IncomePostpaidBillingCalculateService incomePostpaidBillingCalculateService;

    @Override
    public void onMessage(IncomePostpaidBillingMessageDTO message) {
        try {
            incomePostpaidBillingCalculateService.processBillingCalculation(message);
        } catch (Exception e) {
            log.error("处理后付费服务出账消息异常: {}", message, e);
            boolean ignoreException = ignoreException(e);
            if (ignoreException) {
                return;
            }
            throw e;
        }
    }

    private boolean ignoreException(Exception e) {
        if (e instanceof ScaleTableNotBindException) {
            return true;
        }
        if (StrUtil.isNotEmpty(e.getMessage()) && e.getMessage().contains("duplicate key value violates unique constraint")) {
            return true;
        }
        return false;
    }

}
