package com.linkcircle.boss.module.crm.api.basicConfig;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> zyuan
 * @data : 2025-07-01
 */
@FeignClient(name = ApiConstants.NAME,path = ApiConstants.PREFIX+"/basic-config",fallback = BasicConfigApiFallback.class)
@Tag(name = "RPC 服务 - 基础配置信息")
public interface BasicConfigApi {


    @PostMapping("/queryInvoiceOrBill")
    @Operation(summary = "查看发票、账单配置")
    public CommonResult<InvoiceDetailsVO> queryInvoice(@Parameter(description = "主体id（主键id）", required = true, example = "123456")
                                                       @RequestParam Long id,
                                                       @Parameter(description = "配置类型：0-发票 1-账单", required = true, example = "1")
                                                       @RequestParam Integer type);
}
