package com.linkcircle.boss.module.charge.crm.web.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.module.charge.crm.enums.ChargeResourcePurchaseStatusEnum;
import com.linkcircle.boss.module.charge.crm.manager.ChargeCacheManager;
import com.linkcircle.boss.module.charge.crm.web.purchase.mapper.ChargePurchaseMapper;
import com.linkcircle.boss.module.charge.crm.web.purchase.mapper.ChargePurchaseServicePriceMapper;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.ChargePurchase;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.ChargePurchaseServicePrice;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseAddDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseUpdateDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ServiceVersionDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.vo.ChargePurchaseQueryVO;
import com.linkcircle.boss.module.charge.crm.web.purchase.service.IChargePurchaseService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.mapper.ChargeResourceServiceMapper;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO;
import com.linkcircle.boss.module.charge.crm.web.supplier.mapper.SupplierMapper;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants.RESOURCE_SERVICE_PURCHASE_RESOURCE_SERVICE_REPEATED_EXCEPTION;


/**
 * <p>
 * 供应商资源采购信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChargePurchaseServiceImpl extends ServiceImpl<ChargePurchaseMapper, ChargePurchase> implements IChargePurchaseService {

    private final ChargePurchaseMapper chargePurchaseMapper;

    private final ChargeResourceServiceMapper chargeResourceServiceMapper;

    private final ChargePurchaseServicePriceMapper chargePurchaseServicePriceMapper;

    private final SupplierMapper supplierMapper;


    @Override
    public PageResult<ChargePurchaseQueryVO> pageQuery(ChargePurchaseQueryDTO queryDTO) {
        Page<?> page = MyBatisUtils.buildPage(queryDTO);
        List<ChargePurchaseQueryVO> list = chargePurchaseMapper.pageQuery(page, queryDTO);
        list.forEach(chargePurchaseQueryVO -> {
            chargePurchaseQueryVO.setStartTimeFormat(DateUtil.format(new Date(chargePurchaseQueryVO.getStartTime()), DatePattern.NORM_DATETIME_PATTERN));
            chargePurchaseQueryVO.setEndTimeFormat(DateUtil.format(new Date(chargePurchaseQueryVO.getEndTime()), DatePattern.NORM_DATETIME_PATTERN));
            chargePurchaseQueryVO.setCreateTimeFormat(DateUtil.format(new Date(chargePurchaseQueryVO.getCreateTime()), DatePattern.NORM_DATETIME_PATTERN));
        });
        return MyBatisUtils.convert2PageResult(page, list);
    }

    @Override
    public List<ChargeResourceServiceVersionInfoVO> getResourceServiceBySupplierId(Long supplierId, String currencyCode) {
        String resourceServiceIdList = supplierMapper.getResourceServiceIdList(supplierId);
        if (StrUtil.isBlank(resourceServiceIdList)) {
            log.warn("当前supplierId {},资源id为空", supplierId);
            return List.of();
        } else {
            List<String> versionIds = StrUtil.split(resourceServiceIdList, StrUtil.COMMA);
            List<Long> versionIdList = versionIds.stream()
                    .map(Long::parseLong)
                    .toList();
            List<ChargeResourceServiceVersionInfoVO> chargeResourceServiceVersionInfoVOList = chargeResourceServiceMapper.getVersionInfoById(null, versionIdList);
            if (StrUtil.isNotBlank(currencyCode)) {
                return chargeResourceServiceVersionInfoVOList.stream()
                        .filter(vo -> vo.getCurrencyCollection() != null && vo.getCurrencyCollection().contains(currencyCode))
                        .collect(Collectors.toList());
            }
            return chargeResourceServiceVersionInfoVOList;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(ChargePurchaseAddDTO addDTO) {
        //todo 采购id有生成规则？
        ChargePurchase chargePurchase = new ChargePurchase();
        BeanUtil.copyProperties(addDTO, chargePurchase);
        ChargeResourcePurchaseStatusEnum statusEnum = getStatusEnum(addDTO.getStartTime(), addDTO.getEndTime());
        chargePurchase.setStatus(statusEnum.getCode());
        save(chargePurchase);
        //存储服务详细信息
        List<ServiceVersionDTO> serviceVersionDTOS = addDTO.getServiceVersionDTOList();
        //是否有重复服务记录
        boolean hasDuplicates = serviceVersionDTOS.stream()
                .collect(Collectors.groupingBy(ServiceVersionDTO::getResourceServiceId, Collectors.counting()))
                .entrySet().stream()
                .anyMatch(entry -> entry.getValue() > 1);
        if (hasDuplicates) {
            throw new ServiceException(RESOURCE_SERVICE_PURCHASE_RESOURCE_SERVICE_REPEATED_EXCEPTION);
        }
        Map<Long, Integer> countMap = serviceVersionDTOS.stream()
                .filter(dto -> dto.getCount() != null) // 先过滤null值
                .collect(Collectors.groupingBy(
                        ServiceVersionDTO::getVersionId,
                        Collectors.summingInt(ServiceVersionDTO::getCount))
                );
        List<Long> versionIds = serviceVersionDTOS.stream().map(ServiceVersionDTO::getVersionId).toList();
        List<ChargeResourceServiceVersionInfoVO> versionInfoVOS = chargeResourceServiceMapper.getVersionInfoById(null, versionIds);
        List<ChargePurchaseServicePrice> chargePurchaseServicePriceList = new ArrayList<>();
        versionInfoVOS.forEach(versionInfoVO -> {
            ChargePurchaseServicePrice chargePurchaseServicePrice = new ChargePurchaseServicePrice();
            chargePurchaseServicePrice.setPurchaseId(chargePurchase.getId());
            chargePurchaseServicePrice.setSuppliersId(addDTO.getSuppliersId());
            chargePurchaseServicePrice.setAccountId(addDTO.getAccountId());
            chargePurchaseServicePrice.setResourceServiceId(versionInfoVO.getVersionId());
            BeanUtil.copyProperties(versionInfoVO, chargePurchaseServicePrice);
            chargePurchaseServicePrice.setPaymentOptions(addDTO.getPaymentOptions());
            Integer count = countMap.get(versionInfoVO.getVersionId());
            if (ObjectUtil.isNotNull(count)) {
                chargePurchaseServicePrice.setCount(count);
            }

            chargePurchaseServicePriceList.add(chargePurchaseServicePrice);
        });
        chargePurchaseServicePriceMapper.insert(chargePurchaseServicePriceList);
        ChargeCacheManager.deleteAccountResourcePurchasesCache(addDTO.getAccountId());
        return chargePurchase.getId();
    }

    @Override
    public boolean edit(ChargePurchaseUpdateDTO updateDTO) {
        LambdaUpdateWrapper<ChargePurchase> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ChargePurchase::getId, updateDTO.getId());
        queryWrapper.set(ChargePurchase::getStartTime, updateDTO.getStartTime());
        queryWrapper.set(ChargePurchase::getEndTime, updateDTO.getEndTime());
        ChargeResourcePurchaseStatusEnum statusEnum = getStatusEnum(updateDTO.getStartTime(), updateDTO.getEndTime());
        queryWrapper.set(ChargePurchase::getStatus, statusEnum.getCode());
        update(queryWrapper);
        ChargeCacheManager.deleteAccountResourcePurchasesCache(updateDTO.getAccountId());
        return ChargeCacheManager.deleteResourcePurchaseDetailCache(updateDTO.getId());
    }


    @Override
    public boolean cancel(Long id) {
        ChargePurchase chargePurchase = chargePurchaseMapper.selectById(id);
        LambdaUpdateWrapper<ChargePurchase> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ChargePurchase::getId, id);
        queryWrapper.set(ChargePurchase::getStatus, ChargeResourcePurchaseStatusEnum.cancelled.getCode());
        update(queryWrapper);
        ChargeCacheManager.deleteAccountResourcePurchasesCache(chargePurchase.getAccountId());
        return ChargeCacheManager.deleteResourcePurchaseDetailCache(id);
    }

    private ChargeResourcePurchaseStatusEnum getStatusEnum(Long startTime, Long endTime) {
        long currentTime = System.currentTimeMillis();
        if (currentTime < startTime) {
            return ChargeResourcePurchaseStatusEnum.onTrial;
        }
        if (currentTime > endTime) {
            return ChargeResourcePurchaseStatusEnum.itSOver;
        }
        return ChargeResourcePurchaseStatusEnum.hasEffect;
    }


    @Override
    public List<ResourcePurchaseVO> getAccountSubscriptionsList(Long accountId) {
        LambdaQueryWrapper<ChargePurchase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargePurchase::getAccountId, accountId)
                .and(wq -> wq.eq(ChargePurchase::getStatus, ChargeResourcePurchaseStatusEnum.hasEffect.getCode())
                        .or()
                        .eq(ChargePurchase::getStatus, ChargeResourcePurchaseStatusEnum.onTrial.getCode())
                );
        List<ChargePurchase> chargePurchaseList = chargePurchaseMapper.selectList(queryWrapper);
        if (chargePurchaseList.isEmpty()) {
            return List.of();
        }
        List<Long> purchaseIds = chargePurchaseList.stream().map(ChargePurchase::getId).toList();
        //获取价格列表
        return getResourcePurchaseVOS(purchaseIds, chargePurchaseList, Map.of());
    }


    @Override
    public List<Long> getAllPurchaseAccountIds(Integer paymentType) {
        LambdaQueryWrapper<ChargePurchase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargePurchase::getPaymentType, paymentType);
        List<ChargePurchase> chargePurchaseList = chargePurchaseMapper.selectList(queryWrapper);
        return chargePurchaseList.isEmpty() ? List.of() : chargePurchaseList.stream().map(ChargePurchase::getAccountId).toList();
    }

    @Override
    public ResourcePurchaseVO getPurchaseDetail(Long purchaseId) {
        ChargePurchase chargePurchase = chargePurchaseMapper.selectById(purchaseId);
        ResourcePurchaseVO resourcePurchaseVO = new ResourcePurchaseVO();
        BeanUtil.copyProperties(chargePurchase, resourcePurchaseVO);
        //获取价格列表
        LambdaQueryWrapper<ChargePurchaseServicePrice> priceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        priceLambdaQueryWrapper.eq(ChargePurchaseServicePrice::getPurchaseId, purchaseId);
        List<ChargePurchaseServicePrice> priceList = chargePurchaseServicePriceMapper.selectList(priceLambdaQueryWrapper);
        List<ResourcePurchaseVO.Detail> detailList = new ArrayList<>();
        priceList.forEach(price -> {
            ResourcePurchaseVO.Detail detail = new ResourcePurchaseVO.Detail();
            BeanUtil.copyProperties(price, detail);
            detailList.add(detail);
        });
        resourcePurchaseVO.setDetails(detailList);
        return resourcePurchaseVO;
    }

    @Override
    public List<ResourcePurchaseVO> getPurchasesListByBillingType(Integer billingType) {
        //获取价格列表
        LambdaQueryWrapper<ChargePurchaseServicePrice> chargeTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        chargeTypeLambdaQueryWrapper.eq(ChargePurchaseServicePrice::getChargeType, billingType);
        List<ChargePurchaseServicePrice> typePriceList = chargePurchaseServicePriceMapper.selectList(chargeTypeLambdaQueryWrapper);
        if (typePriceList.isEmpty()) {
            return List.of();
        }
        List<Long> purchaseIds = typePriceList.stream().map(ChargePurchaseServicePrice::getPurchaseId).toList();
        LambdaQueryWrapper<ChargePurchase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChargePurchase::getId, purchaseIds)
                .and(wq -> wq.eq(ChargePurchase::getStatus, ChargeResourcePurchaseStatusEnum.hasEffect.getCode())
                        .or()
                        .eq(ChargePurchase::getStatus, ChargeResourcePurchaseStatusEnum.onTrial.getCode())
                );
        List<ChargePurchase> chargePurchaseList = chargePurchaseMapper.selectList(queryWrapper);
        Map<Long, List<ChargePurchaseServicePrice>> priceMap = typePriceList.stream()
                .collect(Collectors.groupingBy(ChargePurchaseServicePrice::getPurchaseId));
        //获取价格列表
        return getResourcePurchaseVOS(purchaseIds, chargePurchaseList, priceMap);
    }

    @NotNull
    private List<ResourcePurchaseVO> getResourcePurchaseVOS(List<Long> purchaseIds, List<ChargePurchase> chargePurchaseList, Map<Long, List<ChargePurchaseServicePrice>> priceMap) {
        List<ResourcePurchaseVO> resourcePurchaseVOS = new ArrayList<>();
        chargePurchaseList.forEach(chargePurchase -> {
            ResourcePurchaseVO resourcePurchaseVO = new ResourcePurchaseVO();
            BeanUtil.copyProperties(chargePurchase, resourcePurchaseVO);
            resourcePurchaseVOS.add(resourcePurchaseVO);
        });
        LambdaQueryWrapper<ChargePurchaseServicePrice> priceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        priceLambdaQueryWrapper.in(ChargePurchaseServicePrice::getPurchaseId, purchaseIds);
        if (CollUtil.isEmpty(priceMap)) {
            List<ChargePurchaseServicePrice> priceList = chargePurchaseServicePriceMapper.selectList(priceLambdaQueryWrapper);
            priceMap = priceList.stream()
                    .collect(Collectors.groupingBy(ChargePurchaseServicePrice::getPurchaseId));
        }
        List<ResourcePurchaseVO.Detail> detailList = new ArrayList<>();
        Map<Long, List<ChargePurchaseServicePrice>> finalPriceMap = priceMap;
        resourcePurchaseVOS.forEach(resourcePurchaseVO -> {
            List<ChargePurchaseServicePrice> prices = finalPriceMap.get(resourcePurchaseVO.getId());
            prices.forEach(price -> {
                ResourcePurchaseVO.Detail detail = new ResourcePurchaseVO.Detail();
                BeanUtil.copyProperties(price, detail);
                detailList.add(detail);
            });
            resourcePurchaseVO.setDetails(detailList);
        });
        return resourcePurchaseVOS;
    }

}
