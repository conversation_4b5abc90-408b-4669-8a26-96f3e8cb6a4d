package com.linkcircle.boss.module.billing.exception;

import cn.hutool.core.text.StrFormatter;
import com.linkcircle.boss.framework.common.exception.ErrorCode;
import com.linkcircle.boss.framework.common.exception.enums.ServiceErrorCodeRange;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 量表未绑定异常 Exception
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public final class ScaleTableNotBindException extends RuntimeException {

    /**
     * 业务错误码
     *
     * @see ServiceErrorCodeRange
     */
    private Integer code;
    /**
     * 错误提示
     */
    private String message;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ScaleTableNotBindException() {
    }

    public ScaleTableNotBindException(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.message = errorCode.getMsg();
    }

    public ScaleTableNotBindException(ErrorCode errorCode, Object... params) {
        this.code = errorCode.getCode();
        this.message = StrFormatter.format(errorCode.getMsg(), params);
    }

    public ScaleTableNotBindException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public ScaleTableNotBindException setCode(Integer code) {
        this.code = code;
        return this;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public ScaleTableNotBindException setMessage(String message) {
        this.message = message;
        return this;
    }

}
