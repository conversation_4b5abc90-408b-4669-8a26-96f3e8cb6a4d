package com.linkcircle.boss.module.crm.api.customer.subscriptions;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-02 09:08
 * @description 客户订阅API降级工厂实现
 */
@Slf4j
@Component
public class ChargeSubscriptionsApiFallback implements FallbackFactory<ChargeSubscriptionsApi> {

    @Override
    public ChargeSubscriptionsApi create(Throwable cause) {
        return new ChargeSubscriptionsApi() {

            @Override
            public CommonResult<List<AccountSubscriptionsVO>> getAccountSubscriptionsList(Long accountId, Integer paymentType) {
                log.error("调用客户订阅API失败，accountId: {}, paymentType: {}, 异常信息: ", accountId, paymentType, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<AccountSubscriptionsVO> getSubscriptionDetail(Long subscriptionId) {
                log.error("调用客户订阅详情API失败，subscriptionId: {}, 异常信息: ", subscriptionId, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<Boolean> changeSubscriptionStatus(Long id, Integer status) {
                log.error("调用修改订阅状态API失败，id: {}, status: {} 异常信息: ", id, status, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<List<Coupon>> getSubscriptionCouponList(Long subscriptionId, Long serviceId) {
                log.error("调用客户订阅详情API失败，subscriptionId: {},serviceId: {}, 异常信息: ", subscriptionId, serviceId, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<List<Long>> getAllSubscriptionAccountIds(Integer paymentType) {
                log.error("调用客户订阅账户id列表API失败，paymentType: {}, 异常信息: ", paymentType, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<List<AccountSubscriptionsVO>> getSubscriptionsListByBillingType(Integer billingType) {
                log.error("调用客户订阅列表API失败，billingType: {}, 异常信息: ", billingType, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
