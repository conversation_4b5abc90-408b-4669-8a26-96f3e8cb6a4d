package com.linkcircle.boss.module.billing.web.cache.consumer;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.module.billing.api.cache.dto.CacheUpdateMessageDTO;
import com.linkcircle.boss.module.billing.web.cache.manager.LocalCacheManager;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-24 10:50
 * @description 缓存更新消费者
 */
@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = ChargeTopicConstant.CHARGE_BROADCAST_TOPIC,
        selectorExpression = ChargeTopicConstant.TAG_CACHE_UPDATE,
        consumerGroup = ChargeTopicConstant.GROUP_CACHE_UPDATE,
        messageModel = MessageModel.BROADCASTING,
        consumeThreadNumber = 1
)
public class CacheUpdateConsumer implements RocketMQListener<CacheUpdateMessageDTO> {

    private final LocalCacheManager localCacheManager;

    @Override
    public void onMessage(CacheUpdateMessageDTO message) {
        try {
            log.info("收到缓存更新消息: businessType={}, businessId={}",
                    message.getBusinessType(), message.getBusinessId());

            if (CacheUpdateMessageDTO.Type.ACCOUNT_SUBSCRIPTION.equals(message.getBusinessType())) {
                localCacheManager.evict(message.getBusinessType(), message.getBusinessId() + PaymentTypeEnum.POSTPAID.getMethod());
                localCacheManager.evict(message.getBusinessType(), message.getBusinessId() + PaymentTypeEnum.PREPAID.getMethod());
                return;
            }

            localCacheManager.evict(message.getBusinessType(), message.getBusinessId());

            log.debug("缓存删除成功: businessType={}, businessId={}",
                    message.getBusinessType(), message.getBusinessId());

        } catch (Exception e) {
            log.error("处理缓存更新消息失败: message={}", message, e);
        }
    }
}
