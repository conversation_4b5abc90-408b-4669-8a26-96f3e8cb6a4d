package com.linkcircle.boss.framework.sharding.algorithms;

import com.google.common.collect.Range;
import com.linkcircle.boss.framework.sharding.cache.ActualTableNameCache;
import com.linkcircle.boss.framework.sharding.utils.ShardingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-08 16:27
 * @description 复合分表算法 - 按年分表
 * 逻辑表_year格式
 * 字段：product_bill_id, product_service_bill_id, billing_start_time
 * <p>
 * 分表逻辑：
 * 1. 优先使用product_bill_id或product_service_bill_id（主键，格式220250708，取2025）
 * 2. 如果主键字段都没有，使用billing_start_time（UTC时间获取年份）
 * 3. 所有字段都没有抛出异常
 */
@Slf4j
public class CompositeYearShardingAlgorithm implements ComplexKeysShardingAlgorithm<Comparable<?>> {

    private static final String BILLING_START_TIME_COLUMN = "billing_start_time";
    private static final String PRODUCT_BILL_ID_COLUMN = "product_bill_id";
    private static final String SUB_TABLE_PRODUCT_BILL_ID_COLUMN = "bill_id";

    private static final String PRODUCT_SERVICE_BILL_ID_COLUMN = "product_service_bill_id";

    @Override
    public String getType() {
        return "COMPOSITE_YEAR_SHARDING_ALGORITHM";
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                         ComplexKeysShardingValue<Comparable<?>> complexKeysShardingValue) {

        String logicTableName = complexKeysShardingValue.getLogicTableName();
        Map<String, Collection<Comparable<?>>> columnNameAndShardingValuesMap = complexKeysShardingValue.getColumnNameAndShardingValuesMap();
        Map<String, Range<Comparable<?>>> columnNameAndRangeValuesMap = complexKeysShardingValue.getColumnNameAndRangeValuesMap();

        List<String> tableNames = new ArrayList<>();

        // 1. 优先使用product_bill_id（主键）
        if (columnNameAndShardingValuesMap.containsKey(PRODUCT_BILL_ID_COLUMN)) {
            Collection<Comparable<?>> productBillIds = columnNameAndShardingValuesMap.get(PRODUCT_BILL_ID_COLUMN);
            for (Comparable<?> productBillId : productBillIds) {
                if (productBillId != null) {
                    int year = getYearFromBillId((Long) productBillId);
                    String tableName = logicTableName + "_" + year;
                    if (ActualTableNameCache.contains(tableName)) {
                        return List.of(tableName);
                    }
                }
            }
        }

        // 2. 如果没有product_bill_id，使用product_service_bill_id（主键）
        if (columnNameAndShardingValuesMap.containsKey(PRODUCT_SERVICE_BILL_ID_COLUMN)) {
            Collection<Comparable<?>> productServiceBillIds = columnNameAndShardingValuesMap.get(PRODUCT_SERVICE_BILL_ID_COLUMN);
            for (Comparable<?> productServiceBillId : productServiceBillIds) {
                if (productServiceBillId != null) {
                    int year = getYearFromBillId((Long) productServiceBillId);
                    String tableName = logicTableName + "_" + year;
                    if (ActualTableNameCache.contains(tableName)) {
                        return List.of(tableName);
                    }
                }
            }
        }

        // 3. 如果主键字段都没有，使用billing_start_time
        if (columnNameAndShardingValuesMap.containsKey(BILLING_START_TIME_COLUMN)) {
            Collection<Comparable<?>> billingStartTimes = columnNameAndShardingValuesMap.get(BILLING_START_TIME_COLUMN);
            for (Comparable<?> billingStartTime : billingStartTimes) {
                if (billingStartTime != null) {
                    int year = getYearFromTimestamp((Long) billingStartTime);
                    String tableName = logicTableName + "_" + year;
                    if (ActualTableNameCache.contains(tableName)) {
                        return List.of(tableName);
                    }
                }
            }
        }

        // 4. 处理billing_start_time的范围查询
        if (columnNameAndRangeValuesMap.containsKey(BILLING_START_TIME_COLUMN)) {
            Range<Comparable<?>> billingStartTimeRange = columnNameAndRangeValuesMap.get(BILLING_START_TIME_COLUMN);
            if (billingStartTimeRange != null) {
                int lowerYear = getYearFromTimestamp((Long) billingStartTimeRange.lowerEndpoint());
                int upperYear = getYearFromTimestamp((Long) billingStartTimeRange.upperEndpoint());
                for (int year = lowerYear; year <= upperYear; year++) {
                    String tableName = logicTableName + "_" + year;
                    if (ActualTableNameCache.contains(tableName) && !tableNames.contains(tableName)) {
                        tableNames.add(tableName);
                    }
                }
            }
        }

        // 后付费子表通过主表bill_id字段关联出所有子表账单信息
        if (columnNameAndShardingValuesMap.containsKey(SUB_TABLE_PRODUCT_BILL_ID_COLUMN)) {
            Collection<Comparable<?>> productBillIds = columnNameAndShardingValuesMap.get(SUB_TABLE_PRODUCT_BILL_ID_COLUMN);
            for (Comparable<?> productBillId : productBillIds) {
                if (productBillId != null) {
                    int year = getYearFromBillId((Long) productBillId);
                    String tableName = logicTableName + "_" + year;
                    if (ActualTableNameCache.contains(tableName) && !tableNames.contains(tableName)) {
                        tableNames.add(tableName);
                    }
                }
            }
        }

        // 5. 所有字段都没有，抛出异常
        if (tableNames.isEmpty()) {
            throw new IllegalArgumentException(
                    String.format("无法确定分表年份：product_bill_id、product_service_bill_id和billing_start_time都为空。logicTableName: %s",
                            logicTableName));
        }

        log.debug("复合分表算法计算结果，logicTableName: {}, tableNames: {}", logicTableName, tableNames);
        return tableNames;
    }

    /**
     * 从时间戳获取年份（UTC时间）
     *
     * @param timestamp 时间戳（毫秒）
     * @return 年份
     */
    private int getYearFromTimestamp(Long timestamp) {
        if (timestamp == null) {
            throw new IllegalArgumentException("billing_start_time不能为空");
        }
        return ShardingUtils.getYear(timestamp);
    }

    /**
     * 从账单ID获取年份
     * 格式：220250708，取2025
     *
     * @param billId 账单ID（product_bill_id或product_service_bill_id）
     * @return 年份
     */
    private int getYearFromBillId(Long billId) {
        if (billId == null) {
            throw new IllegalArgumentException("账单ID不能为空");
        }

        String billIdStr = String.valueOf(billId);
        if (billIdStr.length() < 9) {
            throw new IllegalArgumentException(
                    String.format("账单ID格式错误，长度不足9位: %s", billIdStr));
        }

        try {
            // 从第2位开始取4位作为年份（220250708 -> 2025）
            String yearStr = billIdStr.substring(1, 5);
            int year = Integer.parseInt(yearStr);

            // 验证年份合理性（1970-2100）
            if (year < 1970 || year > 2100) {
                throw new IllegalArgumentException(
                        String.format("从账单ID解析的年份不合理: %d, billId: %s", year, billIdStr));
            }

            return year;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(
                    String.format("从账单ID解析年份失败: %s", billIdStr), e);
        }
    }
}
