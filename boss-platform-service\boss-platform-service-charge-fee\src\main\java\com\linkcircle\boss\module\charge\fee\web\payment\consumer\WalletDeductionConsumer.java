package com.linkcircle.boss.module.charge.fee.web.payment.consumer;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.module.billing.api.wallet.model.dto.WalletDeductionMqDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IWalletPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * WalletDeductionMqDTO
 * 首次支付流程
 */
@Slf4j
@Service
@RocketMQMessageListener(
//        topic = ChargeTopicConstant.CHARGE_NORMAL_TOPIC,
        topic = "boss-platform-charge-normal-payment-topic",
//        consumerGroup = ChargeTopicConstant.GROUP_WALLET_PAYMENT_BILL,
        consumerGroup = "wallet-payment-bill-payment-group999",
//        selectorExpression = ChargeTopicConstant.TAG_WALLET_DEDUCTION_NOTICE,
        selectorExpression = "wallet-deduction-notice999",
        maxReconsumeTimes = 3
)
public class WalletDeductionConsumer implements RocketMQListener<MessageExt> {

    /*@Autowired
    private Map<String, RedissonClient> redissonClients;*/
    @Autowired
    private IWalletPaymentService walletPaymentService;
    @Autowired
    private RedissonClient redissonClient;
    private final Integer MAX_RETRY_TO_DELAY_COUNT = 3;
    private final ObjectMapper objectMapper = new ObjectMapper();


    @Override
    @Transactional
//    public void onMessage(WalletDeductionMqDTO walletDeductionMqDTO) {
    public void onMessage(MessageExt messageExt) {
        RLock rLock = null;
        WalletDeductionMqDTO walletDeductionMqDTO = null;
        int reconsumeTimes = messageExt.getReconsumeTimes();
        boolean locked = false;
        String message = new String(messageExt.getBody());
        log.info("[支付流程]扣费账单详情: {}", message);

        try {
            walletDeductionMqDTO = objectMapper.readValue(message, WalletDeductionMqDTO.class);

            if (ObjectUtil.isNull(walletDeductionMqDTO.getPaymentMethod()) ||
                    ObjectUtil.isNull(walletDeductionMqDTO.getPaymentType()) ||
                    ObjectUtil.isNull(walletDeductionMqDTO.getBillId()) ||
                    ObjectUtil.isNull(walletDeductionMqDTO.getBusinessTime())) {
                log.error("[支付流程]账单信息缺少账单ID、支付方式、支付类型; {}", walletDeductionMqDTO);
                return;
            }

            if (reconsumeTimes == MAX_RETRY_TO_DELAY_COUNT) {
                // 重试满3次, 延迟消费
                walletPaymentService.sendReplayOrder(walletDeductionMqDTO);
                return;
            }

            rLock = redissonClient.getLock(ChargeCacheUtils.getConsumerPaymentLockKey(walletDeductionMqDTO.getAccountId()));
            // 尝试获取锁，等待10秒，锁自动过期30秒
            locked = rLock.tryLock(10, TimeUnit.SECONDS);
            if (!locked) {
                log.error("[支付流程]账户{}获取锁失败, 钱包: {}", walletDeductionMqDTO.getAccountId(), walletDeductionMqDTO.getWalletsId());
                throw new RuntimeException("获取锁失败，触发重试");
            }

            // 账户上锁，处理账单
            walletPaymentService.dealPaymentBusiness(walletDeductionMqDTO);
        } catch (UnrecognizedPropertyException e) {
            // 滞留在 首次支付消费DLQ 是反序列化失败的消息, 不管
            log.error("[支付流程]JSON反序列化失败消息: {};", message, e);
        } catch (Exception e) {
            log.error("[支付流程]消费失败，将触发重试: {}", walletDeductionMqDTO, e);
            throw new RuntimeException("消费失败", e);
        } finally {
            if (locked) {
                rLock.unlock();
                log.info("[支付流程]释放账户{}的锁, 钱包: {}", walletDeductionMqDTO.getAccountId(), walletDeductionMqDTO.getWalletsId());
            }
        }

    }
}
