package com.linkcircle.boss.module.crm.api.supplier.supplier;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import org.springframework.cloud.openfeign.FallbackFactory;

@org.springframework.stereotype.Component
@lombok.extern.slf4j.Slf4j
public class SupplierApiFallback implements FallbackFactory<SupplierApi> {
    @Override
    public SupplierApi create(Throwable cause) {
        return new SupplierApi() {
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<java.util.List<com.linkcircle.boss.module.crm.api.common.vo.CommonVO>> findNameByIds(CommonDTO commonDTO) {
                log.error("调用供应商API失败，commonDTO: {} 异常信息: ", commonDTO, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<java.util.List<com.linkcircle.boss.module.crm.api.supplier.supplier.vo.SupplierListRespVO>> listSuppliers() {
                log.error("调用供应商API失败， 异常信息: " , cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}