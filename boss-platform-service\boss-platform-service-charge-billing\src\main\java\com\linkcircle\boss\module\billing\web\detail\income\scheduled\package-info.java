/**
 * <AUTHOR>
 * @date 2025-07-10 10:39
 * @description 收入固定费率, 阶梯费率 根据间隔时长周期 定时模拟发送详单到mq
 * 1. 查询有固定费率, 阶梯费率的订阅 @SubscriptionDataService#getSubscriptionsListByBillingType
 * 1.1 要判断订阅是否生效..
 * 2. 计算间隔时长 晚1小时 是否到期 @CyclePeriodCalculateService#calculateCyclePeriod
 * 3. 发送详单消息到mq @IncomeBillDetailService#createIncomeBillDetail
 */
package com.linkcircle.boss.module.billing.web.detail.income.scheduled;