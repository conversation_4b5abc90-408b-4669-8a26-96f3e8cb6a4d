package com.linkcircle.boss.module.system.config.rpc.config;

import com.linkcircle.boss.module.system.api.config.ConfigApi;
import com.linkcircle.boss.module.system.api.file.FileApi;
import com.linkcircle.boss.module.system.api.websocket.WebSocketSenderApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, WebSocketSenderApi.class, ConfigApi.class})
public class RpcConfiguration {
}
