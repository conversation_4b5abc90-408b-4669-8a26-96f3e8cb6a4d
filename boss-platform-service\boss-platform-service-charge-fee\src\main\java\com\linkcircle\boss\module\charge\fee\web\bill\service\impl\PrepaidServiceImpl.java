package com.linkcircle.boss.module.charge.fee.web.bill.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.enums.BillEnum;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.convert.PrepaidIncomeBillDetailConvert;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.PrepaidMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqHistoryDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.InvoiceBillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.history.IncomeBillHistoryVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.prepaid.PrepaidIncomeBillDetailVO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillContentHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.CurrencySymbolService;
import com.linkcircle.boss.module.charge.fee.web.bill.service.InvoiceIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.PrepaidService;
import com.linkcircle.boss.module.charge.fee.web.bill.util.NumberUtil;
import com.linkcircle.boss.module.charge.fee.web.invoice.mapper.InvoiceMapper;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.*;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.entity.ChargeInvoiceDO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import com.linkcircle.boss.module.crm.api.productservice.ChargeProductServiceApi;
import com.linkcircle.boss.module.crm.api.productservice.vo.ProductServiceVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/30 9:25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PrepaidServiceImpl implements PrepaidService {


    private final PrepaidMapper prepaidMapper;

    private final CurrencySymbolService currencySymbolService;
    private final InvoiceMapper invoiceMapper;

    private final ChargeProductServiceApi chargeProductServiceApi;

    private final RocketMQTemplate rocketMQTemplate;
    private final CustomerApi customerApi;
    private final EntityApi entityApi;
    private final InvoiceIdGenerator invoiceIdGenerator;

    public List<String> listServiceCodes() {
        CommonResult<List<ProductServiceVO>> listCommonResult = chargeProductServiceApi.allService();
        if (listCommonResult.isSuccess()) {
            return listCommonResult.getData().stream().map(ProductServiceVO::getServiceCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        }
        return List.of();
    }


    private final JdbcTemplate JdbcTemplate;


    @Override
    public void payOffBill(BillReqDto reqDto) {
        List<String> serviceCodes = new ArrayList<>();
        if (StringUtils.isNotBlank(reqDto.getServiceCode())) {
            serviceCodes.add(reqDto.getServiceCode());
        } else {
            serviceCodes = listServiceCodes();
        }
        PrepaidIncomeBillDetailDO prepaidBill = executeIgnore(reqDto.getBillingTime(), serviceCodes, BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(), () -> prepaidMapper.queryById(reqDto.getBillId()));
        if (prepaidBill == null) {
            log.info("账单不存在，id:{}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST);
        }
        if (!InvoiceEnum.BillStatus.WAIT_PAYMENT.is(prepaidBill.getBillStatus()) && !InvoiceEnum.BillStatus.UNSETTLED.is(prepaidBill.getBillStatus())) {
            log.info("账单状态不允许支付，id:{},billStatus:{}", reqDto.getBillId(), prepaidBill.getBillStatus());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_PAY_OFF_NOT_MATCH);
        }
        rocketMQTemplate.convertAndSend(ChargeTopicUtils.getWalletDeductionNoticeTopic(), buildPayOffDto(prepaidBill));
    }


    /**
     * 检查账单发票
     *
     * @param checkReqDTO 检查请求对象，包含账单详情ID列表、提交类型等信息
     * @throws ServiceException 如果检查失败，抛出ServiceException异常
     */
    public void checkInvoice(BillInvoiceCheckDTO checkReqDTO) {
        if (checkReqDTO.getIdDTO() == null || CollectionUtils.isEmpty(checkReqDTO.getIdDTO().getBillDetailIds())) {
            log.info("账单详情ID列表为空");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_CHECK_IDS);
        }
        List<Long> detailIds = checkReqDTO.getIdDTO().getBillDetailIds();
        //tax_rate 税率  available_invoice_amount 可开票金额 >0  entity_id 出账主体id
        long startTime = strToLong.apply(checkReqDTO.getIdDTO().getStartTime());
        long endTime = strToLong.apply(checkReqDTO.getIdDTO().getEndTime());
        List<BillInvoiceCheckGroupDTO> checkResults = executeIgnore(startTime
                , endTime,
                listServiceCodes(), BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(),
                () -> prepaidMapper.checkByIds(detailIds));
        if (CollectionUtils.isNotEmpty(checkResults) && Objects.equals(checkResults.size(), 1) && Objects.equals(checkResults.getFirst().getCount().intValue(), detailIds.size())) {
            log.info("账单详情ID列表符合开票条件,ids:{}", detailIds);
        } else {
            log.info("账单详情ID列表不符合开票条件,ids:{},result:{}", detailIds, JSONUtil.toJsonStr(checkResults));
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_CHECK_NOT_MATCH);
        }

    }

    @Transactional
    public void createInvoice(BillInvoiceCreateReqDTO createReqDTO) {
        checkInvoiceCustomerAndEntity(createReqDTO, ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_CREATE_CUSTOMER_NOT_EXIST, ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_CREATE_ENTITY_NOT_EXIST);

        // 获取发票金额列表
        List<BillInvoiceAmountDTO> invoiceAmounts = createReqDTO.getInvoiceAmounts();

        // 计算最大和最小账单时间
        Long maxTime = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillingTime).max(Long::compareTo).orElse(0L);
        Long minTime = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillingTime).min(Long::compareTo).orElse(0L);

        // 获取所有唯一的服务代码
        List<String> serviceCodes = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getServiceCode).distinct().toList();
        List<Long> productServiceBillIds = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillDetailId).toList();
        // 根据账单详情ID查询账单详情
        List<PrepaidIncomeBillDetailDO> bills = executeIgnore(minTime, maxTime, serviceCodes, BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(), () -> prepaidMapper.selectByIds(productServiceBillIds));

        // 如果账单详情为空，则抛出异常
        if (CollectionUtils.isEmpty(bills)) {
            log.info("账单详情不存在,billDetailIds:{}", invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillDetailId).toList());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST);
        }
        // 将账单详情转换为Map，方便后续查询
        Map<String, PrepaidIncomeBillDetailDO> detailMap = bills.stream().collect(Collectors.toMap(t -> t.getBillDetailId().toString(), t -> t, (d1, d2) -> d2));
        // 创建发票DTO列表和账单详情DO列表
        List<ChargeInvoiceDO> invoiceDOs = new ArrayList<>();
        // 遍历发票金额列表，处理每个发票金额
        for (BillInvoiceAmountDTO invoiceAmount : invoiceAmounts) {
            // check and handle 发票 和 账单 开票金额
            handleCreateInvoice(invoiceAmount, detailMap, invoiceDOs, createReqDTO);
        }
        invoiceMapper.insertOrUpdate(invoiceDOs);
        // 更新账单详情 已开票金额 已可开票金额
        executeIgnore(minTime, maxTime, serviceCodes, BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(), () -> prepaidMapper.updateById(detailMap.values()));
    }

    /**
     * 查询预付费收入账单详情
     *
     * @param billReqDto 请求参数对象，包含账单ID、计费时间、服务编码等信息
     * @return 预付费收入账单详情展示对象
     */
    @Override
    public PrepaidIncomeBillDetailVO queryInvoiceBillDetail(InvoiceBillReqDto billReqDto) {
        List<String> serviceCodes = StringUtils.isNotBlank(billReqDto.getServiceCode()) ? List.of(billReqDto.getServiceCode()) : listServiceCodes();
        // 执行查询账单详情操作
        PrepaidIncomeBillDetailDO bill = executeIgnore(billReqDto.getBillingTime(), serviceCodes, BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(),
                () -> prepaidMapper.selectById(billReqDto.getBillId())
        );
        if (bill == null) {
            log.info("账单详情不存在,billId:{}", billReqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST);
        }
        // 将查询到的账单详情转换为 AccountPrepaidIncomeBillDetailShowVO 对象
        PrepaidIncomeBillDetailVO showVO = PrepaidIncomeBillDetailConvert.INSTANCE.convertDetail(bill);
        handSingleBillColumn(showVO);
        // 处理账单费率详情信息
        handleRateDetail(List.of(showVO), PrepaidIncomeBillDetailVO::getRateDetails, PrepaidIncomeBillDetailVO::getBillingType,
                PrepaidIncomeBillDetailVO::getTaxRate
                , PrepaidIncomeBillDetailVO::setFixedRateConfig, PrepaidIncomeBillDetailVO::setPackageRateConfig,
                PrepaidIncomeBillDetailVO::setTierRateConfig, PrepaidIncomeBillDetailVO::setUsageBasedRateConfig);
        ChargeInvoiceDO chargeInvoiceDO = invoiceMapper.selectById(billReqDto.getBillId());

        if (chargeInvoiceDO == null) {
            log.info("发票不存在,billId:{}", billReqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_NOT_EXIST);
        }
        assign(chargeInvoiceDO, t -> JSONUtil.toBean(t.getCustomerJsonStr(), ChargeCustomerInvoiceDTO.class), showVO, PrepaidIncomeBillDetailVO::setCustomerInvoice);
        assign(chargeInvoiceDO, t -> JSONUtil.toBean(t.getEntityJsonStr(), EntityDetailsVO.class), showVO, PrepaidIncomeBillDetailVO::setEntityInvoice);
        // 返回 AccountPrepaidIncomeBillDetailShowVO 对象
        processBillContentAndCoupons(showVO);
        return showVO;
    }


    private final BillContentHandler billContentHandler;


    private void processBillContentAndCoupons(PrepaidIncomeBillDetailVO showVO) {
        List<BillDiscountConfigDTO> billCoupons = showVO.getCoupons();
        List<BillCoupon> coupons = new ArrayList<>(billContentHandler.handleCoupons(billCoupons, showVO.getProductId(), showVO.getServiceId(), showVO.getCurrency()));
        BillContent productContent = new BillContent();
        productContent.setDescription(StringUtils.isNotEmpty(showVO.getProductName())?showVO.getProductName():"");
        billContentHandler.handleContent(productContent,showVO);
        showVO.setShowCoupons(coupons);
        showVO.setShowContents(List.of(productContent));
    }

    @Override
    public PageResult<IncomeBillHistoryVO<PrepaidIncomeBillDetailVO>> historyBills(BillReqHistoryDto reqDto) {
        return null;
    }

    @Override
    public void deleteBill(BillReqDto reqDto) {
        List<String> serviceCodes = StringUtils.isNotBlank(reqDto.getServiceCode()) ? List.of(reqDto.getServiceCode()) : listServiceCodes();
        PrepaidIncomeBillDetailDO bill = executeIgnore(reqDto.getBillingTime(), serviceCodes, BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(),
                () -> prepaidMapper.selectById(reqDto.getBillId())
        );
        if (bill == null) {
            log.info("账单详情不存在,billId:{}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST);
        }
        executeIgnore(reqDto.getBillingTime(), serviceCodes, BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(),
                () -> prepaidMapper.deleteById(bill)
        );
    }


    /**
     * 处理创建发票的逻辑
     *
     * @param invoiceAmount 发票金额对象，包含发票金额等信息
     * @param detailMap     账单详情映射，包含账单详情对象
     * @param invoiceDOs    发票对象列表，用于存储创建的发票对象
     * @param createReqDTO  创建发票请求对象，包含发票类型等信息
     */
    public void handleCreateInvoice(BillInvoiceAmountDTO invoiceAmount,
                                    Map<String, PrepaidIncomeBillDetailDO> detailMap,
                                    List<ChargeInvoiceDO> invoiceDOs,
                                    BillInvoiceCreateReqDTO createReqDTO) {
        Long billDetailId = invoiceAmount.getBillDetailId();
        PrepaidIncomeBillDetailDO billDetailDO = detailMap.get(billDetailId.toString());
        // 如果账单详情不存在，则抛出异常
        if (billDetailDO == null) {
            log.info("账单详情不存在,billDetailId:{}", billDetailId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST);
        }

        // 如果开票金额小于等于0，则跳过
        if (invoiceAmount.getInvoiceAmount() == null || invoiceAmount.getInvoiceAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("开票金额小于等于0 不需要开票,billDetailId:{},invoiceAmount:{}", billDetailId, invoiceAmount.getInvoiceAmount());
            return;
        }

        // 如果账单已结清，则抛出异常
        if (!billDetailDO.getBillStatus().equals(InvoiceEnum.BillStatus.PAID.getCode())) {
            log.info("账单已结清,billDetailId:{}", billDetailId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_NOT_PAID_INVOICE);
        }

        // 如果开票金额大于可开票金额，则抛出异常
        if (billDetailDO.getAvailableInvoiceAmount().compareTo(invoiceAmount.getInvoiceAmount()) < 0) {
            log.info("开票金额大于可开票金额,billDetailId:{},invoiceAmount:{}", billDetailId, invoiceAmount.getInvoiceAmount());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_AMOUNT_NOT_ENOUGH);
        }
        String invoiceNo = invoiceIdGenerator.generateId(createReqDTO.getEntityId());
        // 创建发票DTO
        ChargeInvoiceDO invoiceDTO = ChargeInvoiceDO.builder()
                .id(IdUtil.getSnowflakeNextId())
                .invoiceId(StringUtils.isNotBlank(invoiceNo) ? invoiceNo : billDetailDO.getBillNo())
                .invoiceBillingId(billDetailId.toString())
                .invoiceBillingNo(billDetailDO.getBillNo())
                .invoiceAmount(invoiceAmount.getInvoiceAmount())
                .invoiceType(createReqDTO.getInvoiceType())
                .billType(InvoiceEnum.BillType.PREPAID.getCode())
                .type(createReqDTO.getType())
                .entityId(billDetailDO.getEntityId())
                .customerId(billDetailDO.getCustomerId())
                .accountId(billDetailDO.getAccountId())
                .status(InvoiceEnum.Status.WAIT_AUDIT.getCode())
                .billServiceCode(billDetailDO.getServiceCode())
                .currencySymbol(currencySymbolService.getCurrencySymbol(billDetailDO.getCurrency()))
                .billTime(billDetailDO.getBillingTime())
                .accountId(createReqDTO.getAccountId())
                .customerJsonStr(JSONUtil.toJsonStr(createReqDTO.getCustomer()))
                .entityJsonStr(JSONUtil.toJsonStr(createReqDTO.getEntity()))
                .build();

        // 发送mq 消息
        invoiceDOs.add(invoiceDTO);

        // 更新账单详情 已开票金额 已可开票金额
        BigDecimal invoicedAmount = billDetailDO.getInvoicedAmount().add(invoiceAmount.getInvoiceAmount());
        BigDecimal availableInvoiceAmount = billDetailDO.getDiscountAmount().subtract(invoicedAmount);

        // 更新账单可开票金额
        billDetailDO.setInvoicedAmount(invoicedAmount);
        billDetailDO.setAvailableInvoiceAmount(availableInvoiceAmount);

        // 发送mq消息
        // 更新账单详情 已开票金额 已可开票金额
        BigDecimal billInvoicedAmount = billDetailDO.getInvoicedAmount().add(invoiceAmount.getInvoiceAmount());
        BigDecimal billAvailableInvoiceAmount = billDetailDO.getDiscountAmount().subtract(invoicedAmount);

        // 更新账单可开票金额
        billDetailDO.setInvoicedAmount(billInvoicedAmount);
        billDetailDO.setAvailableInvoiceAmount(billAvailableInvoiceAmount);
    }

    @Override
    public void cancelInvoice(BillInvoiceAmountCancelDTO cancelDTO) {
        log.info("取消发票,{}", JSONUtil.toJsonStr(cancelDTO));
        // 查询 账单数据
        PrepaidIncomeBillDetailDO prepaidIncomeBillDetailDO = executeIgnore(cancelDTO.getBillingTime(), StringUtils.isNotBlank(cancelDTO.getServiceCode()) ? List.of(cancelDTO.getServiceCode()) : listServiceCodes()
                , BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(), () -> prepaidMapper.selectById(cancelDTO.getBillDetailId()));
        if (prepaidIncomeBillDetailDO == null) {
            log.info("账单详情不存在,invoiceId:{},billId:{}", cancelDTO.getInvoiceId(), cancelDTO.getBillDetailId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST);
        }
        prepaidIncomeBillDetailDO.setAvailableInvoiceAmount(cancelDTO.getInvoiceAmount().add(prepaidIncomeBillDetailDO.getInvoicedAmount()));
        prepaidIncomeBillDetailDO.setInvoicedAmount(prepaidIncomeBillDetailDO.getInvoicedAmount().subtract(cancelDTO.getInvoiceAmount()));
        executeIgnore(prepaidIncomeBillDetailDO.getBillingTime(), prepaidIncomeBillDetailDO.getServiceCode(), BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(),
                () -> prepaidMapper.updateById(prepaidIncomeBillDetailDO));
    }


    @Override
    public boolean refundInvoice(Long billId, Long billingTime, BigDecimal refundAmount) {
        PrepaidIncomeBillDetailDO prepaidIncomeBillDetailDO = executeIgnore(billingTime, listServiceCodes(), BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(), () -> prepaidMapper.queryById(billId));
        if (prepaidIncomeBillDetailDO == null) {
            log.info("账单不存在,billId:{}", billId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        BigDecimal refundInvoiceAmount = prepaidIncomeBillDetailDO.getRefundInvoiceAmount();
        BigDecimal finalRefundAmount = NumberUtil.add(refundInvoiceAmount, refundAmount);
        return executeIgnore(billingTime, List.of(prepaidIncomeBillDetailDO.getServiceCode())
                , BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(), () -> prepaidMapper.refundInvoice(billId, billingTime, finalRefundAmount) > 0);
    }

    @Override
    public CustomerApi getCustomerApi() {
        return customerApi;
    }

    @Override
    public EntityApi getEntityApi() {
        return entityApi;
    }

    @Override
    public Logger getLogger() {
        return log;
    }

}
