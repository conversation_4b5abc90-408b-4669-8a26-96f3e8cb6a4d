package com.linkcircle.boss.module.charge.fee.constants;

import com.linkcircle.boss.framework.common.exception.ErrorCode;

/**
 * <AUTHOR>
 * @date 2025-06-05 9:54
 * @description 错误码 <p>
 * charge 系统，使用 1-004-000-000 段
 * 1-004业务模块_序号
 */
public interface ErrorCodeConstants {

    ErrorCode CONFIG_NOT_EXISTS = new ErrorCode(1_004_000_001, "参数配置不存在");


    //==========  账单模块 1_004_001_000 ==========
    ErrorCode PREPAID_INCOME_BILL_NOT_EXIST = new ErrorCode(1_004_001_000, "预付费账单不存在");
    ErrorCode PREPAID_INCOME_BILL_PAYOFF_STATUS_NOT_ALLOW = new ErrorCode(1_004_001_001, "状态为待支付时才能触发预付费账单结清流程");
    ErrorCode PREPAID_INCOME_BILL_INVOICE_CHECK_IDS = new ErrorCode(1_004_001_002, "预付费账单发票校验失败,账单id列表为空");
    ErrorCode PREPAID_INCOME_BILL_INVOICE_CHECK_NOT_MATCH = new ErrorCode(1_004_001_003, "预付费账单发票校验失败,不符合开票条件");
    ErrorCode PREPAID_INCOME_BILL_INVOICE_NOT_PAID_INVOICE = new ErrorCode(1_004_001_004, "预付费账单未支付,不能开票");
    ErrorCode PREPAID_INCOME_BILL_INVOICE_AMOUNT_NOT_ENOUGH = new ErrorCode(1_004_001_005, "预付费账单可开票金额不足");
    ErrorCode PREPAID_INCOME_BILL_PAY_OFF_NOT_MATCH = new ErrorCode(1_004_001_006, "预付费账单结清校验失败,不符合结清账单条件");
    ErrorCode PREPAID_INCOME_BILL_INVOICE_NOT_EXIST = new ErrorCode(1_004_001_007, "预付费账单的发票不存在");

    ErrorCode POSTPAID_INCOME_BILL_NOT_EXIST = new ErrorCode(1_004_001_036, "后付费账单不存在");
    ErrorCode POSTPAID_INCOME_BILL_PAYOFF_STATUS_NOT_ALLOW = new ErrorCode(1_004_001_037, "状态为待支付时才能触发后付费账单结清流程");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_CHECK_IDS = new ErrorCode(1_004_001_038, "后付费账单发票校验失败,账单id列表为空");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_CHECK_BILL_TIME_RANGE = new ErrorCode(1_004_001_039, "后付费账单发票校验失败,账单时间范围为空");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_CHECK_DETAIL = new ErrorCode(1_004_001_040, "后付费账单发票校验失败,账单详情查询条件为空");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_CHECK_NOT_MATCH = new ErrorCode(1_004_001_041, "后付费账单发票校验失败,不符合开票条件");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_NOT_PAID_INVOICE = new ErrorCode(1_004_001_042, "后付费账单未支付,不能开票");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_AMOUNT_NOT_ENOUGH = new ErrorCode(1_004_001_043, "后付费账单可开票金额不足");
    ErrorCode POSTPAID_INCOME_BILL_PAY_OFF_NOT_MATCH = new ErrorCode(1_004_001_044, "后付费账单结清校验失败,不符合结清账单条件");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_NOT_EXIST = new ErrorCode(1_004_001_007, "后付费账单的发票不存在");


    ErrorCode MAKEUP_INCOME_BILL_DISCOUNT_PRODUCT_ID = new ErrorCode(1_004_001_062, "手工账单优惠的范围为产品,产品id不能为空");
    ErrorCode MAKEUP_INCOME_BILL_DISCOUNT_SERVICE_ID = new ErrorCode(1_004_001_063, "手工账单优惠的范围为服务,产品id或者服务id不能为空");
    ErrorCode MAKEUP_INCOME_BILL_NOT_EXIST = new ErrorCode(1_004_001_064, "手工账单不存在");
    ErrorCode MAKEUP_INCOME_BILL_PAY_OFF_NOT_MATCH = new ErrorCode(1_004_001_065, "手工账单结清校验失败,不符合结清账单条件");
    ErrorCode MAKEUP_INCOME_BILL_MAKE_SURE_STATUS_NOT_MATCH = new ErrorCode(1_004_001_066, "手工账单状态验失败,不符合账单确认条件");
    ErrorCode MAKEUP_INCOME_BILL_NOT_DRAFT_NOT_MATCH = new ErrorCode(1_004_001_067, "手工账单状态验失败,不符合账单退回草稿状态的条件");
    ErrorCode MAKEUP_INCOME_BILL_INVOICE_CHECK_IDS = new ErrorCode(1_004_001_068, "手工账单发票校验失败,账单id列表为空");
    ErrorCode MAKEUP_INCOME_BILL_INVOICE_CHECK_BILL_TIME_RANGE = new ErrorCode(1_004_001_069, "手工账单发票校验失败,账单时间范围为空");
    ErrorCode MAKEUP_INCOME_BILL_INVOICE_CHECK_NOT_MATCH = new ErrorCode(1_004_001_070, "手工账单发票校验失败,不符合开票条件");
    ErrorCode MAKEUP_INCOME_BILL_INVOICE_CHECK_DETAIL = new ErrorCode(1_004_001_071, "手工账单发票校验失败,账单详情查询条件为空");
    ErrorCode MAKEUP_INCOME_BILL_INVOICE_NOT_EXIST = new ErrorCode(1_004_001_072, "手工账单的发票不存在");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_CREATE_CUSTOMER_NOT_EXIST = new ErrorCode(1_004_001_073, "后付费账单开票失败,客户不存在");
    ErrorCode POSTPAID_INCOME_BILL_INVOICE_CREATE_ENTITY_NOT_EXIST = new ErrorCode(1_004_001_077, "后付费账单开票失败,主体不存在");
    ErrorCode MAKEUP_INCOME_BILL_INVOICE_CREATE_CUSTOMER_NOT_EXIST = new ErrorCode(1_004_001_074, "手工账单开票失败,客户不存在");
    ErrorCode MAKEUP_INCOME_BILL_INVOICE_CREATE_ENTITY_NOT_EXIST = new ErrorCode(1_004_001_075, "手工账单开票失败,主体不存在");
    ErrorCode PREPAID_INCOME_BILL_INVOICE_CREATE_CUSTOMER_NOT_EXIST = new ErrorCode(1_004_001_076, "预付费账单开票失败,客户不存在");

    ErrorCode PREPAID_INCOME_BILL_INVOICE_CREATE_ENTITY_NOT_EXIST = new ErrorCode(1_004_001_078, "预付费账单开票失败,主体不存在");

    // 发票模块
    ErrorCode INVOICE_STATUS_ERROR = new ErrorCode(1_005_001_000, "当前发票状态不可用");
    ErrorCode SEND_MAIL_ADDRESS_ERROR = new ErrorCode(1_005_001_001, "邮箱地址不可用");
    ErrorCode PDF_CONTENT_IS_EMPTY = new ErrorCode(1_005_001_002, "生成PDF失败，内容为空");
    ErrorCode PDF_DOWNLOAD_FILE_ERROR = new ErrorCode(1_005_001_003, "下载发票PDF文件异常");
    ErrorCode CREDIT_NOTE_ERROR = new ErrorCode(1_005_001_005, "当前发票无法开信用票据");
    ErrorCode CREDIT_NOTE_CREATE_ERROR = new ErrorCode(1_005_001_006, "生成信用票据失败");


    // ========== 计费处理相关错误码 1_004_001_000 ==========
    ErrorCode SUBSCRIPTION_LIST_EMPTY = new ErrorCode(1_004_008_001, "账户订阅列表为空");
    ErrorCode SERVICE_SUBSCRIPTION_NOT_FOUND = new ErrorCode(1_004_008_002, "未找到匹配的服务订阅: {}");
    ErrorCode SERVICE_CONFIG_NOT_FOUND = new ErrorCode(1_004_008_003, "未找到服务配置: {}");
    ErrorCode BILLING_CALCULATION_FAILED = new ErrorCode(1_004_008_004, "计费计算失败: {}");
    ErrorCode BILL_SAVE_FAILED = new ErrorCode(1_004_008_005, "账单保存失败");
    ErrorCode DELAY_MQ_SEND_FAILED = new ErrorCode(1_004_008_006, "延迟重试MQ发送失败");
    ErrorCode WALLET_MQ_SEND_FAILED = new ErrorCode(1_004_008_007, "钱包扣款MQ发送失败");
    ErrorCode LOCAL_FILE_WRITE_FAILED = new ErrorCode(1_004_008_008, "本地文件写入失败");
    ErrorCode PREPAID_BILL_PROCESS_FAILED = new ErrorCode(1_004_008_009, "预付费账单处理失败: {}");
    ErrorCode POSTPAID_SERVICE_BILL_PROCESS_FAILED = new ErrorCode(1_004_008_010, "后付费服务账单处理失败: {}");
    ErrorCode POSTPAID_PRODUCT_BILL_PROCESS_FAILED = new ErrorCode(1_004_008_011, "后付费产品账单处理失败: {}");
    ErrorCode COST_BILL_PROCESS_FAILED = new ErrorCode(1_004_008_012, "成本账单处理失败: {}");
    ErrorCode RATE_CONFIG_NOT_FOUND = new ErrorCode(1_004_008_013, "费率配置不存在");
    ErrorCode RATE_CONFIG_CURRENCY_NOT_FOUND = new ErrorCode(1_004_008_014, "未找到匹配币种 {} 的费率配置");
    ErrorCode INCOME_BILL_CALCULATION_FAILED = new ErrorCode(1_004_008_015, "收入出账计算原价失败: {}");
    ErrorCode PRODUCT_SERVICE_NOT_FOUND = new ErrorCode(1_004_008_016, "收入出账计算失败,未找到产品或服务: {}");
    ;
    ErrorCode CUSTOMER_ACCOUNT_NOT_FOUND = new ErrorCode(1_004_008_017, "收入出账计算失败,未找到客户账户: {}");
    ;
    ErrorCode COMPUTE_PRODUCT_SERVICE_FEE_REQ_BILL_START_TIME_OR_END_TIME_IS_EMPTY = new ErrorCode(1_004_008_018, "计算产品服务费用请求,账单开始时间或结束时间为空");
    ;
    ErrorCode COMPUTE_PRODUCT_SERVICE_FEE_REQ_CUSTOMER_ID_IS_EMPTY = new ErrorCode(1_004_008_019, "计算产品服务费用请求,账单客户ID为空");
    ;
    ErrorCode COMPUTE_PRODUCT_SERVICE_FEE_REQ_ACCOUNT_ID_IS_EMPTY = new ErrorCode(1_004_008_020, "计算产品服务费用请求,账单账户ID为空");
    ;
    ErrorCode COMPUTE_PRODUCT_SERVICE_FEE_REQ_PRODUCT_BILL_ID_IS_EMPTY = new ErrorCode(1_004_008_021, "计算产品服务费用请求,产品账单id为空");
    ;
    ErrorCode COMPUTE_PRODUCT_SERVICE_FEE_REQ_SUBSCRIPTION_ID_EMPTY = new ErrorCode(1_004_008_022, "计算产品服务费用请求,订阅id为空");
    ErrorCode CREATE_BILL_INFO_IS_NULL = new ErrorCode(1_004_008_023, "创建账单时,账单信息为空");
    ;
    ErrorCode UPDATE_BILL_INFO_IS_NULL = new ErrorCode(1_004_008_024, "确认账单时,账单信息为空");
    ;
    ErrorCode COMPUTE_BILL_FEE_REQ_BILL_INFO_EMPTY = new ErrorCode(1_004_008_025, "计算产品服务费用请求,账单信息为空");
    ;
    ErrorCode COMPUTE_BILL_FEE_REQ_BILL_INFO_ERROR = new ErrorCode(1_004_008_026, "计算产品服务费用请求,账单信息异常");
    ;
    ErrorCode BILL_INVOICE_ID_GENERATE_ERROR_NOT_FOUND_RULE = new ErrorCode(1_004_008_027, "账单发票号生成失败,未找到规则: {}");
    ErrorCode BILL_ID_GENERATE_ERROR_NOT_FOUND_RULE = new ErrorCode(1_004_008_028, "账单号生成失败,未找到规则: {}");
    ErrorCode GENERATOR_ID_TYPE_NOT_SUPPORT = new ErrorCode(1_004_008_029, "序号生成失败,不支持的规则类型: {}");
}
