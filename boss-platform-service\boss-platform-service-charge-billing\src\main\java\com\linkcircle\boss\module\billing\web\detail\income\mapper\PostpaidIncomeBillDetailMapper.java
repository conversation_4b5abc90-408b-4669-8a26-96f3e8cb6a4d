package com.linkcircle.boss.module.billing.web.detail.income.mapper;

import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PostpaidIncomeBillDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025-06-16 17:06
 * @description 后付费收入账单明细数据访问层
 */
@Mapper
public interface PostpaidIncomeBillDetailMapper extends BaseMapperX<PostpaidIncomeBillDetailDO> {

    /**
     * 根据订阅ID、服务Code和时间范围统计使用量（使用SQL SUM函数）
     *
     * @param subscribeId      订阅ID
     * @param serviceId        服务编码
     * @param billingStartTime 计费开始时间
     * @param billingEndTime   计费结束时间
     * @return 总使用量
     */
    PostpaidIncomeBillDetailDO sumUsageByServiceAndTime(@Param("subscribeId") Long subscribeId,
                                                        @Param("serviceId") Long serviceId,
                                                        @Param("billingStartTime") Long billingStartTime,
                                                        @Param("billingEndTime") Long billingEndTime);
}
