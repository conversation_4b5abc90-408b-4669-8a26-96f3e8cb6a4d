package com.linkcircle.boss.module.charge.crm.web.purchase.controller;


import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageParam;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.excel.core.util.ExcelUtils;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseAddDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseUpdateDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.vo.ChargePurchaseQueryVO;
import com.linkcircle.boss.module.charge.crm.web.purchase.service.IChargePurchaseService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 供应商资源采购信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Tag(name = "采购单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/charge-purchase")
public class ChargePurchaseController {

    private final IChargePurchaseService chargePurchaseService;

    @PostMapping(value = "/page")
    @Operation(summary = "采购分页查询")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:page')")
    public CommonResult<PageResult<ChargePurchaseQueryVO>> list(@Valid @RequestBody ChargePurchaseQueryDTO queryDTO) {
        PageResult<ChargePurchaseQueryVO> pages = chargePurchaseService.pageQuery(queryDTO);
        return CommonResult.success(pages);
    }

    @GetMapping(value = "/getResourceServiceBySupplierId")
    @Operation(summary = "供应商下的资源服务(支持查询指定货币)")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:getResourceServiceBySupplierId')")
    public CommonResult<List<ChargeResourceServiceVersionInfoVO>> getResourceServiceBySupplierId(@RequestParam("supplierId") Long supplierId, @RequestParam(value = "CurrencyCode", required = false) String CurrencyCode) {
        return CommonResult.success(chargePurchaseService.getResourceServiceBySupplierId(supplierId, CurrencyCode));
    }

    @GetMapping(value = "/getPurchaseInfoByAccountId")
    @Operation(summary = "获取当前账户id下的采购单信息")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:getPurchaseInfoByAccountId')")
    public CommonResult<?> getPurchaseInfoByAccountId(@RequestParam("accountId") Long accountId) {
        return CommonResult.success(chargePurchaseService.getAccountSubscriptionsList(accountId));
    }

    @GetMapping(value = "/getPurchaseInfoByPurchaseId")
    @Operation(summary = "获取当前PurchaseId下的采购单信息")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:getPurchaseInfoByAccountId')")
    public CommonResult<?> getPurchaseInfoByPurchaseId(@RequestParam("purchaseId") Long purchaseId) {
        return CommonResult.success(chargePurchaseService.getPurchaseDetail(purchaseId));
    }

    @GetMapping(value = "/getAllPurchaseAccountIds")
    @Operation(summary = "获取当前付费类型下的所有账户id")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:getPurchaseInfoByAccountId')")
    public CommonResult<?> getPurchaseInfoByAccountId(@RequestParam("paymentType") Integer paymentType) {
        return CommonResult.success(chargePurchaseService.getAllPurchaseAccountIds(paymentType));
    }

    @GetMapping(value = "/getPurchasesListByChargeType")
    @Operation(summary = "获取当前付费类型下的所有采购单")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:getPurchaseInfoByAccountId')")
    public CommonResult<?> getPurchasesListByChargeType(@RequestParam("chargeType") Integer chargeType) {
        return CommonResult.success(chargePurchaseService.getPurchasesListByBillingType(chargeType));
    }


    @PostMapping(value = "/add")
    @Operation(summary = "新增采购单")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:add')")
    public CommonResult<?> add(@Valid @RequestBody ChargePurchaseAddDTO AddDTO) {
        return CommonResult.success(chargePurchaseService.add(AddDTO));
    }

    @PostMapping(value = "/update")
    @Operation(summary = "更新采购单")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:update')")
    public CommonResult<?> update(@Valid @RequestBody ChargePurchaseUpdateDTO updateDTO) {
        return CommonResult.success(chargePurchaseService.edit(updateDTO));
    }

    @GetMapping(value = "/cancel")
    @Operation(summary = "取消采购单")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:cancel')")
    public CommonResult<?> cancel(@RequestParam("id") Long id) {
        return CommonResult.success(chargePurchaseService.cancel(id));
    }

    @PostMapping(value = "/export")
    @Operation(summary = "采购导出")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:export')")
    public void export(HttpServletResponse response, @RequestBody ChargePurchaseQueryDTO queryDTO) throws IOException {
        queryDTO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ChargePurchaseQueryVO> list = chargePurchaseService.pageQuery(queryDTO).getList();
        ExcelUtils.write(response, "采购.xls", "采购列表", ChargePurchaseQueryVO.class,
                list);
    }
}
