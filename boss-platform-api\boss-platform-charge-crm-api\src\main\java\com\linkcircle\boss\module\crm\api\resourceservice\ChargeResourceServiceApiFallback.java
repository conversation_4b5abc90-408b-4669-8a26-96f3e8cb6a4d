package com.linkcircle.boss.module.crm.api.resourceservice;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 13:56
 */
@Slf4j
@Component
public class ChargeResourceServiceApiFallback implements FallbackFactory<ChargeResourceServiceApi> {
    @Override
    public ChargeResourceServiceApi create(Throwable cause) {
        return new ChargeResourceServiceApi() {
            @Override
            public CommonResult<List<CommonVO>> findNameByIds(CommonDTO commonDTO) {
                log.error("调用资源服务中心API失败API失败，accountId: {}, 异常信息: ", commonDTO, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
