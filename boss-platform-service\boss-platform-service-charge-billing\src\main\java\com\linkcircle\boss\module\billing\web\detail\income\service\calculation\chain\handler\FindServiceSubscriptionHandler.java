package com.linkcircle.boss.module.billing.web.detail.income.service.calculation.chain.handler;

import cn.hutool.core.collection.CollUtil;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.ResponsibilityChainGroupConstant;
import com.linkcircle.boss.module.billing.exception.ServiceConfigNotFoundException;
import com.linkcircle.boss.module.billing.web.data.service.CustomerAccountDataService;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.IncomeBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ReceiveIncomeBillMqDTO;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ServiceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.chain.context.IncomeDetailBillErrorEnum;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.chain.context.IncomeDetailBillRequestContext;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import io.github.kk01001.design.pattern.responsibility.ResponsibilityChain;
import io.github.kk01001.design.pattern.responsibility.ResponsibilityChainContext;
import io.github.kk01001.design.pattern.responsibility.ResponsibilityChainHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-06-18 17:01
 * @description 查询当前要处理的订阅服务新
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ResponsibilityChain(value = ResponsibilityChainGroupConstant.INCOME_DETAIL_BILL, order = 1)
public class FindServiceSubscriptionHandler implements ResponsibilityChainHandler<IncomeDetailBillRequestContext, IncomeDetailBillErrorEnum> {

    private final SubscriptionDataService subscriptionDataService;
    private final CustomerAccountDataService customerAccountDataService;

    @Override
    public IncomeDetailBillErrorEnum handle(ResponsibilityChainContext<IncomeDetailBillRequestContext, IncomeDetailBillErrorEnum> context) {
        IncomeDetailBillRequestContext requestContext = context.getData();
        ReceiveIncomeBillMqDTO incomeBillMqDTO = requestContext.getIncomeBillMqDTO();
        IncomeBillDetailRequestDTO requestParams = incomeBillMqDTO.getRequestParams();
        // 1. 查询账户订阅信息
        Long accountId = requestParams.getAccountId();
        Optional<List<AccountSubscriptionsVO>> accountSubscriptionsOpt = subscriptionDataService.getAccountSubscriptions(accountId, PaymentTypeEnum.PREPAID);
        if (accountSubscriptionsOpt.isEmpty()) {
            context.terminate();
            log.warn("[IncomeDetailBill] 未找到订阅信息, 终止流程.");
            throw new ServiceConfigNotFoundException(ErrorCodeConstants.NOT_FOUND_SUBSCRIPTION, accountId);
        }
        List<AccountSubscriptionsVO> subscriptionList = accountSubscriptionsOpt.get();

        // 2. 查找匹配的服务订阅信息
        Long serviceId = requestParams.getServiceId();
        Long businessTime = requestParams.getBusinessTime();
        Optional<ServiceSubscriptionInfoDTO> serviceSubscriptionInfoOptional = findServiceSubscriptionInfo(subscriptionList, serviceId, businessTime);
        if (serviceSubscriptionInfoOptional.isEmpty()) {
            context.terminate();
            log.warn("[IncomeDetailBill] 未找到匹配的服务订阅信息, 终止流程.");
            throw new ServiceConfigNotFoundException(ErrorCodeConstants.NOT_FOUND_MATCHING_SERVICE_SUBSCRIPTION, serviceId);
        }
        ServiceSubscriptionInfoDTO serviceSubscriptionInfoDTO = serviceSubscriptionInfoOptional.get();
        requestContext.setServiceSubscriptionInfoDTO(serviceSubscriptionInfoDTO);
        requestContext.setPaymentType(serviceSubscriptionInfoDTO.getSubscriptionsVO().getPaymentType());

        // 3. 查询客户账户信息
        Optional<CustomerAccountVO> accountInfoOptional = customerAccountDataService.getCustomerAccountInfo(accountId);
        if (accountInfoOptional.isEmpty()) {
            context.terminate();
            log.warn("[IncomeDetailBill] 未找到客户账户信息, 终止流程. accountId: {}", accountId);
            throw new ServiceConfigNotFoundException(ErrorCodeConstants.NOT_FOUND_CUSTOMER_ACCOUNT, accountId);
        }
        CustomerAccountVO customerAccountVO = accountInfoOptional.get();
        requestContext.setCustomerAccountVO(customerAccountVO);

        log.info("[IncomeDetailBill] 查询订阅和账户信息完成, accountId: {}, serviceId: {}, currency: {}",
                accountId, serviceId, customerAccountVO.getCurrency());
        return IncomeDetailBillErrorEnum.OK;
    }

    /**
     * 查找服务配置和产品信息
     */
    private Optional<ServiceSubscriptionInfoDTO> findServiceSubscriptionInfo(List<AccountSubscriptionsVO> subscriptionList, Long serviceId, Long businessTime) {
        if (CollUtil.isEmpty(subscriptionList)) {
            return Optional.empty();
        }

        for (AccountSubscriptionsVO subscription : subscriptionList) {

            // 订阅状态未开启
            if (!subscription.hasValidSubscription()) {
                continue;
            }

            for (AccountSubscriptionsVO.Detail detail : subscription.getDetails()) {
                if (CollUtil.isEmpty(detail.getProducts())) {
                    continue;
                }

                // 不在时间范围内
                if (detail.getStartTime() >= businessTime || detail.getEndTime() <= businessTime) {
                    log.info("[IncomeDetailBill] 时间范围不符合要求, 忽略该订阅信息. subscriptionId: {}, startTime: {}, endTime: {}, businessTime: {}",
                            subscription.getId(), detail.getStartTime(), detail.getEndTime(), businessTime);
                    continue;
                }

                for (AccountSubscriptionsVO.Product product : detail.getProducts()) {
                    if (CollUtil.isEmpty(product.getServices())) {
                        continue;
                    }

                    for (AccountSubscriptionsVO.Service service : product.getServices()) {
                        if (serviceId.equals(service.getServiceId())) {
                            return Optional.of(new ServiceSubscriptionInfoDTO(subscription, detail, service, product.getProductId()));
                        }
                    }
                }
            }
        }
        return Optional.empty();
    }

}
