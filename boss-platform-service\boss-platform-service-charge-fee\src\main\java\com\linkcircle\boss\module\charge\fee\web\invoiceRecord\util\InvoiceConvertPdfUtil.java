package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.util;

import com.google.common.collect.Lists;
import com.linkcircle.boss.framework.common.util.pdf.Maps;
import com.linkcircle.boss.framework.common.util.pdf.PdfRenderer;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceDetailsResVO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发票转换 pdf工具类
 *
 * <AUTHOR> zyuan
 * @data : 2025-07-01
 */
@Slf4j
public class InvoiceConvertPdfUtil {

    /**
     * 生成pdf
     *
     * @param invoiceInfo      发票属性
     * @param cssPath          样式模版
     * @param templateFileName pdf生成模版名称
     * @return 文件字节
     */
    public static byte[] createPdf(InvoiceDetailsResVO invoiceInfo,
                                   String cssPath,
                                   String templateFileName) {
        try {

            // 转换为Map
//            Map<String, Object> data = BeanUtil.beanToMap(invoiceInfo);
            Map<String, Object> data = new HashMap<>();

            Map<String, Object> entityInfo = new HashMap<>();
            entityInfo.put("brandLogoUrl", "https://iknow-pic.cdn.bcebos.com/503d269759ee3d6d087d775651166d224e4adef0");
            entityInfo.put("entityName", "变形金刚(M78星云)天外来物科技公司");
            entityInfo.put("address", "不知星云系M78星云3572外太轨道098-123星");
            entityInfo.put("postalCode", "108901");
            entityInfo.put("taxNumber", "91350100MA2YCQYN20");
            entityInfo.put("city", "大马哈鱼");
            entityInfo.put("email", "<EMAIL>");

            Map<String, Object> customerInfo = new HashMap<>();
            customerInfo.put("customerName", "汽车人");
            customerInfo.put("address", "火星");

            Map<String, Object> invoiceConfig = new HashMap<>();
            invoiceConfig.put("startSequence", "**********-0001");
            invoiceConfig.put("legalInformation", "发票已经开出，所有解释权归擎天柱所有！");
            invoiceConfig.put("additionalInformation", "力霸天也想要开发票。。。");
            invoiceConfig.put("color", "red");

            Map<String, Object> entity = new HashMap<>();
            entity.put("date", "2025-01-01 20:00:00");
            entity.put("entityInfo", entityInfo);
            entity.put("customerInfo", customerInfo);
            entity.put("invoiceConfig", invoiceConfig);

            List<Map<String, Object>> items = Lists.newArrayList();
            items.
                    add(new Maps<String, Object>()
                            .
                            with("serviceName", "电视机")
                                    .
                            with("number", "5")
                                    .
                            with("price", "500")
                                    .
                            with("amount", "2500")
                                    .
                            toMap());
            items.
                    add(new Maps<String, Object>()
                            .
                            with("serviceName", "洗衣机")
                                    .
                            with("number", "10")
                                    .
                            with("price", "300")
                                    .
                            with("amount", "3000")
                                    .
                            toMap());
            items.
                    add(new Maps<String, Object>()
                            .
                            with("serviceName", "冰箱")
                                    .
                            with("number", "2")
                                    .
                            with("price", "1000")
                                    .
                            with("amount", "2000")
                                    .
                            toMap());
            entity.put("items", items);
            data.put("entity", entity);
            data.put("title", "变形金刚发票公司");
            return new PdfRenderer.Builder()
                    .withTemplate(templateFileName)
                    .withCssFileName(cssPath)
                    .withData(data)
                    .render();
        } catch (Exception e) {
            log.error("转换发票pdf文件异常：", e);
            return null;
        }
    }
}
