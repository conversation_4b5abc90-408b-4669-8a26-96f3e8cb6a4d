package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.util;

import com.google.common.collect.Lists;
import com.linkcircle.boss.framework.common.util.pdf.Maps;
import com.linkcircle.boss.framework.common.util.pdf.PdfRenderer;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceDetailsResVO;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发票转换 pdf工具类
 *
 * <AUTHOR> zyuan
 * @data : 2025-07-01
 */
@Slf4j
public class InvoiceConvertPdfUtil {

    /**
     * 生成pdf
     *
     * @param invoiceInfo      发票属性
     * @param cssPath          样式模版
     * @param templateFileName pdf生成模版名称
     * @return 文件字节
     */
    public static byte[] createPdf(InvoiceDetailsResVO invoiceInfo,
                                   String cssPath,
                                   String templateFileName) {
        try {
            Map<String, Object> data = new HashMap<>();
            EntityDetailsVO entityDetailsVO = invoiceInfo.getEntityInfo();
            Map<String, Object> entityInfo = new HashMap<>();
            entityInfo.put("brandLogoUrl", entityDetailsVO.getBrandLogoUrl());
            entityInfo.put("entityName", entityDetailsVO.getEntityName());
            entityInfo.put("address", entityDetailsVO.getAddress());
            entityInfo.put("postalCode", entityDetailsVO.getPostalCode());
            entityInfo.put("taxNumber", entityDetailsVO.getTaxNumber());
            entityInfo.put("city", entityDetailsVO.getCity());
            entityInfo.put("email", entityDetailsVO.getEmail());

            ChargeCustomerInfoVO customerInfoVO = invoiceInfo.getCustomerInfo();
            Map<String, Object> customerInfo = new HashMap<>();
            customerInfo.put("customerName", customerInfoVO.getCustomerName());
            customerInfo.put("address", customerInfoVO.getAddress());

            InvoiceDetailsVO config = invoiceInfo.getInvoiceConfig();
            Map<String, Object> invoiceConfig = new HashMap<>();
            invoiceConfig.put("startSequence", invoiceInfo.getInvoiceId());
            invoiceConfig.put("legalInformation", config.getLegalInformation());
            invoiceConfig.put("additionalInformation", config.getAdditionalInformation());
            invoiceConfig.put("color", config.getBrandColor());
            Map<String, Object> entity = new HashMap<>();
            entity.put("date", invoiceInfo.getInvoiceDate());
            entity.put("entityInfo", entityInfo);
            entity.put("customerInfo", customerInfo);
            entity.put("invoiceConfig", invoiceConfig);

            List<Map<String, Object>> items = Lists.newArrayList();
            items.
                    add(new Maps<String, Object>()
                            .
                            with("serviceName", "电视机")
                                    .
                            with("number", "5")
                                    .
                            with("price", "500")
                                    .
                            with("amount", "2500")
                                    .
                            toMap());
            items.
                    add(new Maps<String, Object>()
                            .
                            with("serviceName", "洗衣机")
                                    .
                            with("number", "10")
                                    .
                            with("price", "300")
                                    .
                            with("amount", "3000")
                                    .
                            toMap());
            items.
                    add(new Maps<String, Object>()
                            .
                            with("serviceName", "冰箱")
                                    .
                            with("number", "2")
                                    .
                            with("price", "1000")
                                    .
                            with("amount", "2000")
                                    .
                            toMap());
            entity.put("items", items);
            data.put("entity", entity);
            data.put("title", "变形金刚发票公司");
            return new PdfRenderer.Builder()
                    .withTemplate(templateFileName)
                    .withCssFileName(cssPath)
                    .withData(data)
                    .render();
        } catch (Exception e) {
            log.error("转换发票pdf文件异常：", e);
            return null;
        }
    }
}
