CREATE TABLE `charge_purchase`
(
    `id`                bigint(20) NOT NULL COMMENT '主键id',
    `suppliers_id`      bigint(20) NOT NULL COMMENT '供应商id',
    `account_id`        bigint(20) NOT NULL COMMENT '账户ID',
    `currency_code`     char(3) NOT NULL COMMENT '货币代码（ISO 4217 标准，如 CNY 表示人民币）',
    `entity_id`         bigint(20) NOT NULL COMMENT '主体ID',
    `payment_type`      tinyint(1) DEFAULT NULL COMMENT '支付类型，0-预付费，1-后付费',
    `start_time`        bigint(20) DEFAULT NULL COMMENT '采购开始时间',
    `end_time`          bigint(20) DEFAULT NULL COMMENT '采购结束时间',
    `free_trial_days`   bigint(20) DEFAULT NULL COMMENT '免费试用天数',
    `by_proportion`     tinyint(2) DEFAULT NULL COMMENT '是否按比例计算，0：否，1：是',
    `billing_entity_id` bigint(20) NOT NULL COMMENT '主体ID（开单主体）',
    `is_tax_inclusive`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否含税，0：不含税，1：含税',
    `rate`              int(10) NOT NULL COMMENT '税率百分比（示例：9.00）',
    `cycle_type`        tinyint(1) DEFAULT NULL COMMENT '出账周期类型（数据字典）',
    `days`              int(3) DEFAULT NULL COMMENT '天数',
    `status`            tinyint(1) DEFAULT NULL COMMENT '状态，0：已结束，1：已生效，2：试用中，3：已取消',
    `create_time`       bigint(20) DEFAULT NULL COMMENT '数据添加时间戳',
    `update_time`       bigint(20) DEFAULT NULL COMMENT '数据变更时间戳',
    `tenant_id`         bigint(20) DEFAULT NULL COMMENT '多租户编号',
    `creator`           varchar(32)      DEFAULT NULL COMMENT '创建人',
    `updater`           varchar(32)      DEFAULT NULL COMMENT '修改人',
    `deleted`           bit(1)  NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY                 `idx_suppliers_id` (`suppliers_id`),
    KEY                 `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='供应商资源采购信息表';



CREATE TABLE `charge_purchase_service_price`
(
    `id`                  bigint(20) NOT NULL COMMENT '主键id',
    `suppliers_id`        bigint(20) NOT NULL COMMENT '供应商id',
    `account_id`          bigint(20) NOT NULL COMMENT '账户ID',
    `purchase_id`         bigint(20) NOT NULL COMMENT '供应商资源采购信息表id',
    `resource_service_id` bigint(20) NOT NULL COMMENT '资源服务id',
    `service_name`        varchar(64) NOT NULL COMMENT '服务名称',
    `scale_id`            bigint(20) DEFAULT NULL COMMENT '量表表结构id',
    `event_name`          varchar(255)         DEFAULT NULL COMMENT '事件名称',
    `description`         varchar(255)         DEFAULT NULL COMMENT '服务描述',
    `charge_type`         tinyint(1) DEFAULT NULL COMMENT '0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费',
    `payment_options`     tinyint(1) DEFAULT NULL COMMENT '支付方式，0:现金，1：积分',
    `unit_label`          varchar(16)          DEFAULT NULL COMMENT '单位标签',
    `count`               int(20) DEFAULT NULL COMMENT '数量（固定费率，阶梯费率可填写）',
    `currency_price_json` longtext COMMENT '价格配置json',
    `in_package`          tinyint(1) DEFAULT NULL COMMENT '是否含套餐外,0:不包含，1：包含',
    `period`              int(11) NOT NULL COMMENT '间隔时长单位 0：一次性, 1:日，2：周，3：月，4：季度，5：年',
    `unit_period`         int(11) NOT NULL COMMENT '间隔时长（数据字典）',
    `create_time`         bigint(20) DEFAULT NULL COMMENT '数据添加时间戳',
    `update_time`         bigint(20) DEFAULT NULL COMMENT '数据变更时间戳',
    `creator`             varchar(32)          DEFAULT NULL COMMENT '创建人',
    `updater`             varchar(32)          DEFAULT NULL COMMENT '修改人',
    `tenant_id`           bigint(20) DEFAULT NULL COMMENT '多租户编号',
    `deleted`             bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                   `idx_subs_id` (`suppliers_id`),
    KEY                   `idx_account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='采购服务价格管理';
