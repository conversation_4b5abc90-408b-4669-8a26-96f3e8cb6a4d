<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.charge.crm.web.purchase.mapper.ChargePurchaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.linkcircle.boss.module.charge.crm.web.purchase.model.ChargePurchase">
        <id column="id" property="id"/>
        <result column="suppliers_id" property="suppliersId"/>
        <result column="account_id" property="accountId"/>
        <result column="currency_code" property="currencyCode"/>
        <result column="entity_id" property="entityId"/>
        <result column="payment_type" property="paymentType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="free_trial_days" property="freeTrialDays"/>
        <result column="by_proportion" property="byProportion"/>
        <result column="billing_entity_id" property="billingEntityId"/>
        <result column="is_tax_inclusive" property="isTaxInclusive"/>
        <result column="rate" property="rate"/>
        <result column="cycle_type" property="cycleType"/>
        <result column="days" property="days"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        cp
        .
        id
        , cp.suppliers_id, cp.account_id, cp.currency_code, cp.entity_id, cp.payment_type, cp.start_time, cp.end_time,cp.free_trial_days, cp.by_proportion, cp.billing_entity_id, cp.is_tax_inclusive, cp.rate, cp.cycle_type, cp.days, cp.`status`, cp.create_time, cp.update_time, cp.creator, cp.updater
    </sql>
    <select id="pageQuery"
            resultType="com.linkcircle.boss.module.charge.crm.web.purchase.model.vo.ChargePurchaseQueryVO">
        select
        <include refid="Base_Column_List"></include>,
        ceb.entity_name as entityName,
        csi.supplier_name as suppliersName,
        GROUP_CONCAT(cpsp.service_name SEPARATOR ',') AS resourceServiceNames
        from charge_purchase cp
        left join charge_entity_business ceb on cp.entity_id=ceb.id
        left join charge_supplier_info csi on cp.suppliers_id=csi.id
        left join charge_purchase_service_price cpsp on cp.id=cpsp.purchase_id
        <where>
            <if test="query.purchaseId != null and query.purchaseId != ''">
                AND cp.id like CONCAT('%',#{query.purchaseId},'%')
            </if>
            <if test="query.accountId != null and query.accountId != ''">
                AND cp.account_id=#{query.accountId}
            </if>

            <if test="query.resourceServiceId != null and query.resourceServiceId != ''">
                AND #{query.resourceServiceId} in (select resource_service_id from charge_purchase_service_price where
                purchase_id=cp.id)
            </if>

            <if test="query.accountId != null and query.accountId != ''">
                AND cp.account_id=#{query.accountId}
            </if>
            <if test="query.status != null">
                AND cp.status =#{query.status}
            </if>
            <if test="query.suppliersId != null and query.suppliersId != ''">
                AND cp.suppliers_id =#{query.suppliersId}
            </if>
            <if test="query.entityId != null and query.entityId != ''">
                AND cp.entity_id =#{query.entityId}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND cp.create_time &gt;=#{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND cp.create_time &lt;=#{query.endTime}
            </if>

        </where>
        group by cp.id
        order by cp.create_time desc
    </select>
    <select id="newPageQuery"
            resultType="com.linkcircle.boss.module.charge.crm.web.supplier.model.vo.SupplierAccountPurchaseRespVO">
        select
        <include refid="Base_Column_List"></include>,
        ceb.entity_name as entityName,
        csi.supplier_name as supplierName
        from charge_purchase cp
        left join charge_entity_business ceb on cp.entity_id=ceb.entity_id
        left join charge_supplier_info csi on cp.suppliers_id=csi.id
        <where>
            <if test="req.purchaseStatus != null ">
                AND cp.status =#{req.purchaseStatus}
            </if>
            <if test="req.supplierId != null">
                AND cp.suppliers_id =#{req.supplierId}
            </if>
            <if test="req.purchaseId != null ">
                AND cp.id =#{req.purchaseId}
            </if>
            <if test="req.accountId != null ">
                AND cp.account_id=#{accountId}
            </if>
            <if test="req.resourceId != null ">
                AND cp.resource_id=#{req.resourceId}
            </if>
        </where>

    </select>

    <select id="countPurchaseBySupplierId"
            resultType="com.linkcircle.boss.module.charge.crm.web.supplier.model.vo.SupplierPurchaseVO">
        select suppliers_id as supplierId,count(1) as purchaseCount
        from charge_purchase t
        <where>
            t.suppliers_id = #{supplierId}
            <if test="status != null">
                and t.status = #{status}
            </if>
        </where>
        group by t.suppliers_id
    </select>

</mapper>
