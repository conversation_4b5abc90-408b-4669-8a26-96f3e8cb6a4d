package com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeFixedRateCycleStatusMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomePackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeTierRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeFixedRateCycleStatusDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomePackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeTierRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.IncomeRateUsageManageService;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-23 09:02
 * @description 收入-费率用量管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomeRateUsageManageServiceImpl implements IncomeRateUsageManageService {

    private final IncomeFixedRateCycleStatusMapper incomeFixedRateCycleStatusMapper;
    private final IncomeTierRateUsageMapper incomeTierRateUsageMapper;
    private final IncomePackageRateUsageMapper incomePackageRateUsageMapper;
    private final IncomeUsageRateUsageMapper incomeUsageRateUsageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                   BigDecimal usage,
                                   String usageUnit,
                                   Boolean inTrail) {

        // 试用期
        if (Boolean.TRUE.equals(inTrail)) {
            return true;
        }

        if (!cyclePeriodResult.isSuccess()) {
            log.warn("周期计算失败，无法更新费率用量: {}", cyclePeriodResult.getErrorMessage());
            return false;
        }

        ChargeRateTypeEnum rateTypeEnum = cyclePeriodResult.getRateTypeEnum();
        if (rateTypeEnum == null) {
            log.error("不支持的费率类型: {}", rateTypeEnum);
            return false;
        }
        return switch (rateTypeEnum) {
            case FIXED -> updateFixedRateStatus(cyclePeriodResult, usage);
            case TIERED -> updateTierRateUsage(cyclePeriodResult, usage, usageUnit);
            case PACKAGE -> updatePackageRateUsage(cyclePeriodResult, usage, usageUnit);
            case USAGE -> updateUsageRateUsage(cyclePeriodResult, usage, usageUnit);
        };
    }

    @Override
    public BigDecimal getRateUsage(Integer rateType,
                                   Long subscriptionId,
                                   Long accountId,
                                   Long serviceId,
                                   String billingCycle,
                                   String currency) {

        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(rateType);
        if (rateTypeEnum == null) {
            log.error("不支持的费率类型: {}", rateType);
            return BigDecimal.ZERO;
        }

        return switch (rateTypeEnum) {
            case FIXED -> getFixedRateUsage(subscriptionId, serviceId, billingCycle);
            case TIERED -> getTierRateUsage(subscriptionId, serviceId, billingCycle);
            case PACKAGE -> getPackageRateUsage(subscriptionId, serviceId, billingCycle);
            case USAGE -> getUsageRateUsage(subscriptionId, serviceId, billingCycle);
        };
    }

    @Override
    public boolean isFixedRateBilled(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeFixedRateCycleStatusDO::getId);
        queryWrapper.eq(IncomeFixedRateCycleStatusDO::getServiceId, serviceId)
                .eq(IncomeFixedRateCycleStatusDO::getSubscriptionId, subscriptionId)
                .eq(IncomeFixedRateCycleStatusDO::getBillingCycle, billingCycle);

        IncomeFixedRateCycleStatusDO statusDO = incomeFixedRateCycleStatusMapper.selectOne(queryWrapper);
        return statusDO != null;
    }

    @Override
    public boolean isTieredRateBilled(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeTierRateUsageDO::getId);
        queryWrapper.eq(IncomeTierRateUsageDO::getServiceId, serviceId)
                .eq(IncomeTierRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeTierRateUsageDO::getBillingCycle, billingCycle);

        IncomeTierRateUsageDO statusDO = incomeTierRateUsageMapper.selectOne(queryWrapper);
        return statusDO != null;
    }

    /**
     * 更新固定费率状态
     */
    private boolean updateFixedRateStatus(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        String currency = cyclePeriodResult.getCurrency();
        // 检查是否已存在记录
        LambdaQueryWrapper<IncomeFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomeFixedRateCycleStatusDO::getServiceId, cyclePeriodResult.getServiceId())
                .eq(IncomeFixedRateCycleStatusDO::getSubscriptionId, subscriptionId)
                .eq(IncomeFixedRateCycleStatusDO::getBillingCycle, billingCycle);

        IncomeFixedRateCycleStatusDO existingRecord = incomeFixedRateCycleStatusMapper.selectOne(queryWrapper);

        if (existingRecord == null) {
            // 创建新记录
            IncomeFixedRateCycleStatusDO statusDO = IncomeFixedRateCycleStatusDO.builder()
                    .subscriptionId(subscriptionId)
                    .accountId(cyclePeriodResult.getAccountId())
                    .serviceId(cyclePeriodResult.getServiceId())
                    .billingCycle(billingCycle)
                    .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                    .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                    .totalUsage(usage)
                    .currency(currency)
                    .billingTime(System.currentTimeMillis())
                    .createTime(System.currentTimeMillis())
                    .build();

            int insertCount = incomeFixedRateCycleStatusMapper.insert(statusDO);
            log.info("创建固定费率周期状态记录, subscriptionId: {}, billingCycle: {}, result: {}",
                    subscriptionId, billingCycle, insertCount > 0 ? "成功" : "失败");
            return insertCount > 0;
        }

        log.debug("固定费率周期状态记录已存在, subscriptionId: {}, billingCycle: {}", subscriptionId, billingCycle);
        return true;
    }

    /**
     * 更新阶梯费率用量
     */
    private boolean updateTierRateUsage(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage, String usageUnit) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        String currency = cyclePeriodResult.getCurrency();
        // 检查是否已存在记录
        LambdaQueryWrapper<IncomeTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomeTierRateUsageDO::getServiceId, cyclePeriodResult.getServiceId())
                .eq(IncomeTierRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeTierRateUsageDO::getBillingCycle, billingCycle);

        IncomeTierRateUsageDO existingRecord = incomeTierRateUsageMapper.selectOne(queryWrapper);

        if (existingRecord == null) {
            // 创建新记录
            IncomeTierRateUsageDO usageDO = IncomeTierRateUsageDO.builder()
                    .subscriptionId(subscriptionId)
                    .accountId(cyclePeriodResult.getAccountId())
                    .serviceId(cyclePeriodResult.getServiceId())
                    .billingCycle(billingCycle)
                    .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                    .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                    .totalUsage(usage)
                    .usageUnit(usageUnit)
                    .currency(currency)
                    .billingTime(System.currentTimeMillis())
                    .createTime(System.currentTimeMillis())
                    .build();

            int insertCount = incomeTierRateUsageMapper.insert(usageDO);
            log.info("创建阶梯费率用量记录, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}",
                    subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
            return insertCount > 0;
        }

        // 更新累计用量
        BigDecimal newTotalUsage = existingRecord.getTotalUsage().add(usage);
        LambdaUpdateWrapper<IncomeTierRateUsageDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IncomeTierRateUsageDO::getId, existingRecord.getId())
                .set(IncomeTierRateUsageDO::getTotalUsage, newTotalUsage)
                .set(IncomeTierRateUsageDO::getBillingTime, System.currentTimeMillis());

        int updateCount = incomeTierRateUsageMapper.update(null, updateWrapper);
        log.info("更新阶梯费率用量, subscriptionId: {}, billingCycle: {}, 原用量: {}, 新增用量: {}, 总用量: {}, result: {}",
                subscriptionId, billingCycle, existingRecord.getTotalUsage(), usage, newTotalUsage,
                updateCount > 0 ? "成功" : "失败");
        return updateCount > 0;
    }

    /**
     * 更新套餐费率用量
     */
    private boolean updatePackageRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                           BigDecimal usage,
                                           String usageUnit) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        String currency = cyclePeriodResult.getCurrency();
        // 检查是否已存在记录
        LambdaQueryWrapper<IncomePackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomePackageRateUsageDO::getServiceId, cyclePeriodResult.getServiceId())
                .eq(IncomePackageRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomePackageRateUsageDO::getBillingCycle, billingCycle);

        IncomePackageRateUsageDO existingRecord = incomePackageRateUsageMapper.selectOne(queryWrapper);

        if (existingRecord == null) {
            // 创建新记录
            IncomePackageRateUsageDO usageDO = IncomePackageRateUsageDO.builder()
                    .subscriptionId(subscriptionId)
                    .accountId(cyclePeriodResult.getAccountId())
                    .serviceId(cyclePeriodResult.getServiceId())
                    .billingCycle(billingCycle)
                    .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                    .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                    .totalUsage(usage)
                    .usageUnit(usageUnit)
                    .currency(currency)
                    .billingTime(System.currentTimeMillis())
                    .createTime(System.currentTimeMillis())
                    .build();

            int insertCount = incomePackageRateUsageMapper.insert(usageDO);
            log.info("创建套餐费率用量记录, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}",
                    subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
            return insertCount > 0;
        }

        // 更新累计用量
        BigDecimal newTotalUsage = existingRecord.getTotalUsage().add(usage);
        LambdaUpdateWrapper<IncomePackageRateUsageDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IncomePackageRateUsageDO::getId, existingRecord.getId())
                .set(IncomePackageRateUsageDO::getTotalUsage, newTotalUsage)
                .set(IncomePackageRateUsageDO::getBillingTime, System.currentTimeMillis());

        int updateCount = incomePackageRateUsageMapper.update(null, updateWrapper);
        log.info("更新套餐费率用量, subscriptionId: {}, billingCycle: {}, 原用量: {}, 新增用量: {}, 总用量: {}, result: {}",
                subscriptionId, billingCycle, existingRecord.getTotalUsage(), usage, newTotalUsage,
                updateCount > 0 ? "成功" : "失败");
        return updateCount > 0;
    }

    /**
     * 更新按量费率用量
     */
    private boolean updateUsageRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                         BigDecimal usage,
                                         String usageUnit) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long accountId = cyclePeriodResult.getAccountId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String currency = cyclePeriodResult.getCurrency();
        // 检查是否已存在记录
        LambdaQueryWrapper<IncomeUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomeUsageRateUsageDO::getServiceId, serviceId)
                .eq(IncomeUsageRateUsageDO::getSubscriptionId, cyclePeriodResult.getSubscriptionId())
                .eq(IncomeUsageRateUsageDO::getBillingCycle, billingCycle);

        IncomeUsageRateUsageDO existingRecord = incomeUsageRateUsageMapper.selectOne(queryWrapper);

        if (existingRecord == null) {
            // 创建新记录
            IncomeUsageRateUsageDO usageDO = IncomeUsageRateUsageDO.builder()
                    .subscriptionId(cyclePeriodResult.getSubscriptionId())
                    .accountId(accountId)
                    .serviceId(serviceId)
                    .billingCycle(billingCycle)
                    .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                    .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                    .totalUsage(usage)
                    .usageUnit(usageUnit)
                    .currency(currency)
                    .billingTime(System.currentTimeMillis())
                    .createTime(System.currentTimeMillis())
                    .build();

            int insertCount = incomeUsageRateUsageMapper.insert(usageDO);
            log.info("创建按量费率用量记录, accountId: {}, serviceId: {}, billingCycle: {}, usage: {}, result: {}",
                    accountId, serviceId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
            return insertCount > 0;
        }

        // 更新累计用量
        BigDecimal newTotalUsage = existingRecord.getTotalUsage().add(usage);
        LambdaUpdateWrapper<IncomeUsageRateUsageDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IncomeUsageRateUsageDO::getId, existingRecord.getId())
                .set(IncomeUsageRateUsageDO::getTotalUsage, newTotalUsage)
                .set(IncomeUsageRateUsageDO::getBillingTime, System.currentTimeMillis());

        int updateCount = incomeUsageRateUsageMapper.update(null, updateWrapper);
        log.info("更新按量费率用量, accountId: {}, serviceId: {}, billingCycle: {}, 原用量: {}, 新增用量: {}, 总用量: {}, result: {}",
                accountId, serviceId, billingCycle, existingRecord.getTotalUsage(), usage, newTotalUsage,
                updateCount > 0 ? "成功" : "失败");
        return updateCount > 0;
    }

    private BigDecimal getFixedRateUsage(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeFixedRateCycleStatusDO::getTotalUsage);
        queryWrapper.eq(IncomeFixedRateCycleStatusDO::getServiceId, serviceId)
                .eq(IncomeFixedRateCycleStatusDO::getSubscriptionId, subscriptionId)
                .eq(IncomeFixedRateCycleStatusDO::getBillingCycle, billingCycle);

        IncomeFixedRateCycleStatusDO usageDO = incomeFixedRateCycleStatusMapper.selectOne(queryWrapper);
        return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
    }

    /**
     * 获取阶梯费率用量
     */
    private BigDecimal getTierRateUsage(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeTierRateUsageDO::getTotalUsage);
        queryWrapper.eq(IncomeTierRateUsageDO::getServiceId, serviceId)
                .eq(IncomeTierRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeTierRateUsageDO::getBillingCycle, billingCycle);

        IncomeTierRateUsageDO usageDO = incomeTierRateUsageMapper.selectOne(queryWrapper);
        return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
    }

    /**
     * 获取套餐费率用量
     */
    private BigDecimal getPackageRateUsage(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomePackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomePackageRateUsageDO::getTotalUsage);
        queryWrapper.eq(IncomePackageRateUsageDO::getServiceId, serviceId)
                .eq(IncomePackageRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomePackageRateUsageDO::getBillingCycle, billingCycle);

        IncomePackageRateUsageDO usageDO = incomePackageRateUsageMapper.selectOne(queryWrapper);
        return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
    }

    /**
     * 获取按量费率用量
     */
    private BigDecimal getUsageRateUsage(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeUsageRateUsageDO::getTotalUsage);
        queryWrapper.eq(IncomeUsageRateUsageDO::getServiceId, serviceId)
                .eq(IncomeUsageRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeUsageRateUsageDO::getBillingCycle, billingCycle);

        IncomeUsageRateUsageDO usageDO = incomeUsageRateUsageMapper.selectOne(queryWrapper);
        return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
    }

}
