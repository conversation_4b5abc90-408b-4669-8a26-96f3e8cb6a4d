package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linkcircle.boss.framework.excel.core.convert.TimestampConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 发票分页信息 VO
 *
 * <AUTHOR> zyuan
 * @data : 2025-06-30
 */
@Schema(description = "发票 - 发票分页信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceRecordResVO implements java.io.Serializable {

    @Schema(description = "发票主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1209901901")
    private Long id;

    @ExcelProperty(value = "状态")
    @Schema(description = "发票状态 0-待审核 1-审核不通过 2-已开票", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    /**
     * 发票编号(如CN-1, LA20240530-0001)
     */
    @ExcelProperty(value = "ID")
    @Schema(description = "发票编号(如CN-1, LA20240530-0001)", requiredMode = Schema.RequiredMode.REQUIRED, example = "LA20240530-0001")
    private String invoiceId;

    /**
     * 发票类型(信用票据、国外发票、增值税专票、红冲发票等)
     */
    @ExcelProperty(value = "类型")
    @Schema(description = "票据类型 0-信用票据 1-普票 2-国外发票 3-增值税专票 4-红冲发票", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer invoiceType;

    /**
     * 主体id
     */
    @Schema(description = "发票关联的主体id", requiredMode = Schema.RequiredMode.REQUIRED, example = "*********")
    private Long entityId;

    @Schema(description = "发票关联的客户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "*********")
    private Long customerId;

    @Schema(description = "发票关联的账户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "*********")
    private Long accountId;

    @ExcelProperty(value = "账单ID")
    @Schema(description = "开票账单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "bill202502030182012")
    private String invoiceBillingId;

    @Schema(description = "币种符号：$", requiredMode = Schema.RequiredMode.REQUIRED, example = "$")
    private String currencySymbol;

    @ExcelProperty(value = "金额")
    @Schema(description = "开票金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "2.00")
    private BigDecimal invoiceAmount;

    /**
     * 账单类型
     */
    @Schema(description = "账单类型 1-预付费账单，2-后付费账单,3-手工账单")
    private Integer billType;

    /**
     * 主体名称
     */
    @ExcelProperty(value = "开票主体")
    @Schema(description = "主体名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    private String entityName;

    /**
     * 创建者，目前使用 SysUser 的 id 编号
     */
    @ExcelProperty(value = "开票人员")
    @Schema(description = "开票人员", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    private String creator;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", converter = TimestampConvert.class)
    @Schema(description = "创建时间(时间戳)", requiredMode = Schema.RequiredMode.REQUIRED, example = "10001090201")
    private Long createTime;

    /**
     * 主体信息字符串
     */
    @JsonIgnore
    private String entityJsonStr;

    /**
     * 客户信息JSON字符串
     */
    @JsonIgnore
    private String customerJsonStr;
}
