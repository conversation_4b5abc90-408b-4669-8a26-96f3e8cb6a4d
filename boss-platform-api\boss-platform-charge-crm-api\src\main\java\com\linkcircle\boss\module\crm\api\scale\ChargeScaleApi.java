package com.linkcircle.boss.module.crm.api.scale;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.scale.vo.ChargeScaleColumnInfoRespVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-12 13:35
 * @description
 */
@FeignClient(name = ApiConstants.NAME,
        path = ApiConstants.PREFIX + "/scale",
        fallbackFactory = ChargeScaleApiFallback.class)
@Tag(name = "RPC 服务 - 量表")
public interface ChargeScaleApi {

    @GetMapping("/business-column/list")
    @Operation(summary = "获取量表字段配置列表")
    @Parameter(name = "tableId", description = "量表编号", required = true, example = "1024")
    CommonResult<List<ChargeScaleColumnInfoRespVO>> getScaleBusinessColumnInfoList(@RequestParam("tableId") Long tableId);
}
