package com.linkcircle.boss.module.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-06-17 11:47
 * @description 账单状态枚举
 */
@Getter
@AllArgsConstructor
public enum BillStatusEnum {

    WAIT_BILL(-1, "待出账"),
    DRAFT(0, "草稿"),
    PENDING(1, "待支付"),
    PAID(2, "已支付"),
    UNPAID(3, "未结清");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举对象
     */
    public static BillStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (BillStatusEnum billStatus : values()) {
            if (billStatus.getStatus().equals(status)) {
                return billStatus;
            }
        }
        return null;
    }
}
