package com.linkcircle.boss.module.billing.web.data.service;

import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;

/**
 * <AUTHOR>
 * @date 2025-06-20 17:22
 * @description 周期服务 时间间隔计算
 */
public interface CyclePeriodCalculateService {

    /**
     * 计算周期信息
     *
     * @param periodUnit   周期单位
     * @param timezone     时区
     * @param businessTime 业务时间
     * @param startTime    开始时间
     * @param period       周期
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateCyclePeriod(Integer periodUnit, String timezone, long businessTime, long startTime, int period);

    /**
     * 计算周期信息（支持获取上一周期）
     *
     * @param periodUnit       周期单位
     * @param timezone         时区
     * @param businessTime     业务时间
     * @param startTime        开始时间
     * @param period           周期
     * @param getPreviousCycle true-获取上一周期，false-获取当前周期
     * @return 周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateCyclePeriod(Integer periodUnit, String timezone, long businessTime, long startTime, int period, boolean getPreviousCycle);

    /**
     * 计算基于自然周的间隔时长的当前周期信息
     * 自然周：周一到周日为一个完整的自然周
     *
     * @param timezone      时区
     * @param businessTime  业务时间（毫秒时间戳）
     * @param startTime     开始时间（毫秒时间戳）
     * @param intervalWeeks 间隔时长（周数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateWeekCyclePeriod(String timezone, long businessTime, long startTime, int intervalWeeks);

    /**
     * 计算基于自然周的间隔时长的周期信息（支持获取上一周期）
     * 自然周：周一到周日为一个完整的自然周
     *
     * @param timezone         时区
     * @param businessTime     业务时间（毫秒时间戳）
     * @param startTime        开始时间（毫秒时间戳）
     * @param intervalWeeks    间隔时长（周数）
     * @param getPreviousCycle true-获取上一周期，false-获取当前周期
     * @return 周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateWeekCyclePeriod(String timezone, long businessTime, long startTime, int intervalWeeks, boolean getPreviousCycle);

    /**
     * 计算基于自然日的间隔时长的当前周期信息
     * 自然日：00:00:00 到 23:59:59 为一个完整的自然日
     *
     * @param timezone     时区
     * @param businessTime 业务时间（毫秒时间戳）
     * @param startTime    开始时间（毫秒时间戳）
     * @param intervalDays 间隔时长（天数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateDayCyclePeriod(String timezone, long businessTime, long startTime, int intervalDays);

    /**
     * 计算基于自然日的间隔时长的周期信息（支持获取上一周期）
     * 自然日：00:00:00 到 23:59:59 为一个完整的自然日
     *
     * @param timezone         时区
     * @param businessTime     业务时间（毫秒时间戳）
     * @param startTime        开始时间（毫秒时间戳）
     * @param intervalDays     间隔时长（天数）
     * @param getPreviousCycle true-获取上一周期，false-获取当前周期
     * @return 周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateDayCyclePeriod(String timezone, long businessTime, long startTime, int intervalDays, boolean getPreviousCycle);

    /**
     * 计算基于自然月的间隔时长的当前周期信息
     * 自然月：1号 00:00:00 到月末 23:59:59 为一个完整的自然月
     *
     * @param timezone       时区
     * @param businessTime   业务时间（毫秒时间戳）
     * @param startTime      开始时间（毫秒时间戳）
     * @param intervalMonths 间隔时长（月数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateMonthCyclePeriod(String timezone, long businessTime, long startTime, int intervalMonths);

    /**
     * 计算基于自然月的间隔时长的周期信息（支持获取上一周期）
     * 自然月：1号 00:00:00 到月末 23:59:59 为一个完整的自然月
     *
     * @param timezone         时区
     * @param businessTime     业务时间（毫秒时间戳）
     * @param startTime        开始时间（毫秒时间戳）
     * @param intervalMonths   间隔时长（月数）
     * @param getPreviousCycle true-获取上一周期，false-获取当前周期
     * @return 周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateMonthCyclePeriod(String timezone, long businessTime, long startTime, int intervalMonths, boolean getPreviousCycle);

    /**
     * 计算基于自然季度的间隔时长的当前周期信息
     * 自然季度：Q1(1-3月)、Q2(4-6月)、Q3(7-9月)、Q4(10-12月)
     *
     * @param timezone         时区
     * @param businessTime     业务时间（毫秒时间戳）
     * @param startTime        开始时间（毫秒时间戳）
     * @param intervalQuarters 间隔时长（季度数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateQuarterCyclePeriod(String timezone, long businessTime, long startTime, int intervalQuarters);

    /**
     * 计算基于自然季度的间隔时长的周期信息（支持获取上一周期）
     * 自然季度：Q1(1-3月)、Q2(4-6月)、Q3(7-9月)、Q4(10-12月)
     *
     * @param timezone           时区
     * @param businessTime       业务时间（毫秒时间戳）
     * @param startTime          开始时间（毫秒时间戳）
     * @param intervalQuarters   间隔时长（季度数）
     * @param getPreviousCycle   true-获取上一周期，false-获取当前周期
     * @return 周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateQuarterCyclePeriod(String timezone, long businessTime, long startTime, int intervalQuarters, boolean getPreviousCycle);

    /**
     * 计算基于自然年的间隔时长的当前周期信息
     * 自然年：1月1日 00:00:00 到 12月31日 23:59:59 为一个完整的自然年
     *
     * @param timezone      时区
     * @param businessTime  业务时间（毫秒时间戳）
     * @param startTime     开始时间（毫秒时间戳）
     * @param intervalYears 间隔时长（年数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateYearCyclePeriod(String timezone, long businessTime, long startTime, int intervalYears);

    /**
     * 计算基于自然年的间隔时长的周期信息（支持获取上一周期）
     * 自然年：1月1日 00:00:00 到 12月31日 23:59:59 为一个完整的自然年
     *
     * @param timezone         时区
     * @param businessTime     业务时间（毫秒时间戳）
     * @param startTime        开始时间（毫秒时间戳）
     * @param intervalYears    间隔时长（年数）
     * @param getPreviousCycle true-获取上一周期，false-获取当前周期
     * @return 周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateYearCyclePeriod(String timezone, long businessTime, long startTime, int intervalYears, boolean getPreviousCycle);
}
