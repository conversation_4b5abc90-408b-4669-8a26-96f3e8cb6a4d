package com.linkcircle.boss.module.charge.crm.manager;

import cn.hutool.extra.spring.SpringUtil;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.transation.TransactionUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-07-07 14:34
 * @description 计费 缓存管理器
 */
@Slf4j
public abstract class ChargeCacheManager {

    private static RedissonUtil getRedissonUtil() {
        return SpringUtil.getBean(RedissonUtil.class);
    }

    // ==================== 业务缓存删除方法 ====================

    /**
     * 删除账户订阅信息缓存
     *
     * @param accountId 账户ID
     * @return 是否删除成功
     */
    public static boolean deleteAccountSubscriptionsCache(Long accountId) {
        String key = ChargeCacheUtils.getAccountSubscriptionsKey(accountId);
        return deleteCache(key);
    }

    /**
     * 删除订阅信息详情缓存
     *
     * @param subscriptionId 订阅ID
     * @return 是否删除成功
     */
    public static boolean deleteSubscriptionsDetailCache(Long subscriptionId) {
        String key = ChargeCacheUtils.getSubscriptionsDetailKey(subscriptionId);
        return deleteCache(key);
    }

    /**
     * 删除供应商资源采购信息缓存
     *
     * @param accountId 供应商账户ID
     * @return 是否删除成功
     */
    public static boolean deleteAccountResourcePurchasesCache(Long accountId) {
        String key = ChargeCacheUtils.getAccountResourcePurchasesKey(accountId);
        return deleteCache(key);
    }

    /**
     * 删除资源采购信息详情缓存
     *
     * @param purchaseId 供应商资源采购信息ID
     * @return 是否删除成功
     */
    public static boolean deleteResourcePurchaseDetailCache(Long purchaseId) {
        String key = ChargeCacheUtils.getResourcePurchaseDetailKey(purchaseId);
        return deleteCache(key);
    }

    /**
     * 删除客户账户信息缓存
     *
     * @param accountId 账户ID
     * @return 是否删除成功
     */
    public static boolean deleteCustomerAccountCache(Long accountId) {
        String key = ChargeCacheUtils.getCustomerAccountKey(accountId);
        return deleteCache(key);
    }

    /**
     * 删除供应商账户信息缓存
     *
     * @param accountId 账户ID
     * @return 是否删除成功
     */
    public static boolean deleteSupplierAccountCache(Long accountId) {
        String key = ChargeCacheUtils.getSupplierAccountKey(accountId);
        return deleteCache(key);
    }

    /**
     * 删除量表字段配置信息缓存
     *
     * @param scaleId 量表ID
     * @return 是否删除成功
     */
    public static boolean deleteScaleColumnsCache(Long scaleId) {
        String key = ChargeCacheUtils.getScaleColumnsKey(scaleId);
        return deleteCache(key);
    }

    /**
     * 删除指标单位配置简单列表缓存
     *
     * @return 是否删除成功
     */
    public static boolean deleteMetricUnitConfigSimpleListCache() {
        String key = ChargeCacheUtils.getMetricUnitConfigSimpleListKey();
        return deleteCache(key);
    }

    /**
     * 删除单个缓存
     *
     * @param key 缓存key
     * @return 是否删除成功
     */
    public static boolean deleteCache(String key) {
        try {
            TransactionUtils.doAfterTransaction(() -> {
                RedissonUtil redissonUtil = getRedissonUtil();
                redissonUtil.delete(key);
            });
            return true;
        } catch (Exception e) {
            log.error("删除缓存失败, key: {}", key, e);
            return false;
        }
    }

}
