package org.github.cloud.framework.common.constant;

/**
 * <AUTHOR>
 * @date 2025-07-29 16:30
 * @description 时间常量定义
 */
public final class TimeConstants {

    /**
     * 基础时间单位常量
     */
    public static final long MILLIS_PER_SECOND = 1000L;
    public static final long SECONDS_PER_MINUTE = 60L;
    public static final long MINUTES_PER_HOUR = 60L;
    public static final long HOURS_PER_DAY = 24L;
    public static final long DAYS_PER_WEEK = 7L;
    // 平均天数
    public static final long DAYS_PER_MONTH_AVG = 30L;
    public static final long MONTHS_PER_QUARTER = 3L;
    public static final long MONTHS_PER_YEAR = 12L;
    public static final long QUARTERS_PER_YEAR = 4L;

    /**
     * 毫秒时间常量
     */
    public static final long MILLIS_PER_MINUTE = SECONDS_PER_MINUTE * MILLIS_PER_SECOND;
    public static final long MILLIS_PER_HOUR = MINUTES_PER_HOUR * MILLIS_PER_MINUTE;
    public static final long MILLIS_PER_DAY = HOURS_PER_DAY * MILLIS_PER_HOUR;
    public static final long MILLIS_PER_WEEK = DAYS_PER_WEEK * MILLIS_PER_DAY;

    /**
     * 秒时间常量
     */
    public static final long SECONDS_PER_HOUR = MINUTES_PER_HOUR * SECONDS_PER_MINUTE;
    public static final long SECONDS_PER_DAY = HOURS_PER_DAY * SECONDS_PER_HOUR;
    public static final long SECONDS_PER_WEEK = DAYS_PER_WEEK * SECONDS_PER_DAY;

    /**
     * 分钟时间常量
     */
    public static final long MINUTES_PER_DAY = HOURS_PER_DAY * MINUTES_PER_HOUR;
    public static final long MINUTES_PER_WEEK = DAYS_PER_WEEK * MINUTES_PER_DAY;

    /**
     * 常用时间间隔（毫秒）
     */
    public static final long ONE_SECOND_MILLIS = MILLIS_PER_SECOND;
    public static final long ONE_MINUTE_MILLIS = MILLIS_PER_MINUTE;
    public static final long ONE_HOUR_MILLIS = MILLIS_PER_HOUR;
    public static final long ONE_DAY_MILLIS = MILLIS_PER_DAY;
    public static final long ONE_WEEK_MILLIS = MILLIS_PER_WEEK;

    /**
     * 常用时间间隔（秒）
     */
    public static final long ONE_MINUTE_SECONDS = SECONDS_PER_MINUTE;
    public static final long ONE_HOUR_SECONDS = SECONDS_PER_HOUR;
    public static final long ONE_DAY_SECONDS = SECONDS_PER_DAY;
    public static final long ONE_WEEK_SECONDS = SECONDS_PER_WEEK;

    /**
     * 缓存过期时间常量（秒）
     */
    public static final long CACHE_EXPIRE_1_MINUTE = ONE_MINUTE_SECONDS;
    public static final long CACHE_EXPIRE_5_MINUTES = 5 * ONE_MINUTE_SECONDS;
    public static final long CACHE_EXPIRE_10_MINUTES = 10 * ONE_MINUTE_SECONDS;
    public static final long CACHE_EXPIRE_30_MINUTES = 30 * ONE_MINUTE_SECONDS;
    public static final long CACHE_EXPIRE_1_HOUR = ONE_HOUR_SECONDS;
    public static final long CACHE_EXPIRE_6_HOURS = 6 * ONE_HOUR_SECONDS;
    public static final long CACHE_EXPIRE_12_HOURS = 12 * ONE_HOUR_SECONDS;
    public static final long CACHE_EXPIRE_1_DAY = ONE_DAY_SECONDS;
    public static final long CACHE_EXPIRE_7_DAYS = ONE_WEEK_SECONDS;

    /**
     * 时间格式常量
     */
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
    public static final String DATETIME_FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String DATETIME_FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static final String TIME_FORMAT_HH_MM_SS = "HH:mm:ss";
    public static final String TIME_FORMAT_HHMMSS = "HHmmss";

    /**
     * 时区常量
     */
    public static final String TIMEZONE_UTC = "UTC";
    public static final String TIMEZONE_SHANGHAI = "Asia/Shanghai";
    public static final String TIMEZONE_TOKYO = "Asia/Tokyo";
    public static final String TIMEZONE_NEW_YORK = "America/New_York";
    public static final String TIMEZONE_LONDON = "Europe/London";

    /**
     * 私有构造函数，防止实例化
     */
    private TimeConstants() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 将毫秒转换为秒
     *
     * @param millis 毫秒
     * @return 秒
     */
    public static long millisToSeconds(long millis) {
        return millis / MILLIS_PER_SECOND;
    }

    /**
     * 将秒转换为毫秒
     *
     * @param seconds 秒
     * @return 毫秒
     */
    public static long secondsToMillis(long seconds) {
        return seconds * MILLIS_PER_SECOND;
    }

    /**
     * 将分钟转换为毫秒
     *
     * @param minutes 分钟
     * @return 毫秒
     */
    public static long minutesToMillis(long minutes) {
        return minutes * MILLIS_PER_MINUTE;
    }

    /**
     * 将小时转换为毫秒
     *
     * @param hours 小时
     * @return 毫秒
     */
    public static long hoursToMillis(long hours) {
        return hours * MILLIS_PER_HOUR;
    }

    /**
     * 将天数转换为毫秒
     *
     * @param days 天数
     * @return 毫秒
     */
    public static long daysToMillis(long days) {
        return days * MILLIS_PER_DAY;
    }

    /**
     * 将周数转换为毫秒
     *
     * @param weeks 周数
     * @return 毫秒
     */
    public static long weeksToMillis(long weeks) {
        return weeks * MILLIS_PER_WEEK;
    }
}
