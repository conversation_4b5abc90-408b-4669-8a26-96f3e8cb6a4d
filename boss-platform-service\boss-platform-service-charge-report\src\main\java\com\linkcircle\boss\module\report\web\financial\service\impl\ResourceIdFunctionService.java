package com.linkcircle.boss.module.report.web.financial.service.impl;

import com.linkcircle.boss.module.crm.api.common.CommonApi;
import com.linkcircle.boss.module.crm.api.resource.ChargeResourceApi;
import com.linkcircle.boss.module.crm.api.resourceservice.ChargeResourceServiceApi;
import com.linkcircle.boss.module.report.enums.FinancialEnum;
import com.linkcircle.boss.module.report.web.financial.service.FunctionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/29 19:31
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ResourceIdFunctionService implements FunctionService, SmartInitializingSingleton {


    private final ChargeResourceApi chargeResourceApi;

    @Override
    public FinancialEnum.BillColumn column() {
        return FinancialEnum.BillColumn.RESOURCE;
    }

    @Override
    public CommonApi getApi() {
        return chargeResourceApi;
    }

    @Override
    public Logger getLogger() {
        return log;
    }
    @Override
    public void afterSingletonsInstantiated() {
        register(column(), this);
    }
}
