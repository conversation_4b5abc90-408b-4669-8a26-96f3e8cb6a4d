package com.linkcircle.boss.module.charge.crm.web.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageParam;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.excel.core.util.ExcelUtils;
import com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.crm.web.customer.convert.ChargeCustomerInfoConvert;
import com.linkcircle.boss.module.charge.crm.web.customer.mapper.ChargeForOtherMapper;
import com.linkcircle.boss.module.charge.crm.web.customer.mapper.CustomerAccountMapper;
import com.linkcircle.boss.module.charge.crm.web.customer.mapper.CustomerMapper;
import com.linkcircle.boss.module.charge.crm.web.customer.model.dto.AccountNameDTO;
import com.linkcircle.boss.module.charge.crm.web.customer.model.dto.ChargeForOtherQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.customer.model.dto.CustomerNameDTO;
import com.linkcircle.boss.module.charge.crm.web.customer.model.dto.CustomerQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.customer.model.entity.ChargeCustomerAccountsInfo;
import com.linkcircle.boss.module.charge.crm.web.customer.model.entity.ChargeCustomerInfo;
import com.linkcircle.boss.module.charge.crm.web.customer.model.enums.CustomerStatus;
import com.linkcircle.boss.module.charge.crm.web.customer.model.vo.CustomerAccountTreeVO;
import com.linkcircle.boss.module.charge.crm.web.customer.model.vo.CustomerAccountWalletTreeVO;
import com.linkcircle.boss.module.charge.crm.web.customer.model.vo.CustomerPageVO;
import com.linkcircle.boss.module.charge.crm.web.customer.service.CustomerService;
import com.linkcircle.boss.module.charge.crm.web.subscribe.mapper.ChargeSubscriptionsMapper;
import com.linkcircle.boss.module.charge.crm.web.subscribe.mapper.ChargeSubscriptionsProductMapper;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.entity.ChargeSubscriptionsDO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.entity.ChargeSubscriptionsProductDO;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.fee.api.bill.BillFeeApi;
import com.linkcircle.boss.module.fee.api.wallets.CustomerWalletApi;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.ChargeForOther;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletNameDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  2025/6/11 9:30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, ChargeCustomerInfo> implements CustomerService {

    private final CustomerAccountMapper customerAccountMapper;

    private final CustomerMapper customerMapper;

    private final ChargeForOtherMapper chargeForOtherMapper;

    private final ChargeSubscriptionsMapper chargeSubscriptionsMapper;

    private final ChargeSubscriptionsProductMapper chargeSubscriptionsProductMapper;

    @Resource
    private CustomerWalletApi customerAccountWalletApi;

    private final BillFeeApi billFeeApi;


    @Override
    public CommonResult<?> createCustomer(ChargeCustomerInfo dto) {
        long count = count(Wrappers.<ChargeCustomerInfo>lambdaQuery()
                .eq(ChargeCustomerInfo::getCustomerName, dto.getCustomerName()));
        if (count > 0) {
            return CommonResult.error(500, "客户名已存在");
        }
        String customerId = generateId("cus");
        dto.setId(IdUtil.getSnowflakeNextId());
        dto.setCustomerCode(customerId);
        dto.setStatus(2);
        createAccount(dto);
        save(dto);
        return CommonResult.success();
    }

    @Override
    public CommonResult<PageResult<CustomerPageVO>> pageQuery(CustomerQueryDTO dto) {
        List<ChargeCustomerInfo> list1 = list();
        for (ChargeCustomerInfo chargeCustomerInfo : list1) {
            if (!chargeCustomerInfo.getStatus().equals(CustomerStatus.ARCHIVED.getStatus())) {
                //查询客户下的订阅，判断是否为活跃用户
                Long count = chargeSubscriptionsMapper.selectCount(Wrappers.<ChargeSubscriptionsDO>lambdaQuery()
                        .eq(ChargeSubscriptionsDO::getCustomerId, chargeCustomerInfo.getId()));
                if (count > 0) {
                    chargeCustomerInfo.setStatus(1);
                    updateById(chargeCustomerInfo);
                }
            }
        }
        PageResult<ChargeCustomerInfo> chargeCustomerInfoPageResult = customerMapper.selectPage(dto);
        List<ChargeCustomerInfo> list = chargeCustomerInfoPageResult.getList();
        List<CustomerPageVO> customerPageList = new ArrayList<>();
        for (ChargeCustomerInfo chargeCustomerInfo : list) {
            CustomerPageVO customerPageVO = new CustomerPageVO();
            BeanUtil.copyProperties(chargeCustomerInfo, customerPageVO);
            Long l = customerAccountMapper.selectCount(Wrappers.<ChargeCustomerAccountsInfo>lambdaQuery()
                    .eq(ChargeCustomerAccountsInfo::getCountryCode, dto.getCustomerCode()));
            customerPageVO.setAccountNum(Math.toIntExact(l));
            // 产品的数量
            List<ChargeSubscriptionsDO> chargeSubscriptionsDOS = chargeSubscriptionsMapper.selectList(Wrappers.<ChargeSubscriptionsDO>lambdaQuery()
                    .eq(ChargeSubscriptionsDO::getCustomerId, chargeCustomerInfo.getId()));
            int count1 = 0;
            for (ChargeSubscriptionsDO chargeSubscriptionsDO : chargeSubscriptionsDOS) {
                Long l1 = chargeSubscriptionsProductMapper.selectCount(Wrappers.<ChargeSubscriptionsProductDO>lambdaQuery()
                        .eq(ChargeSubscriptionsProductDO::getSubsId, chargeSubscriptionsDO.getId()));
                count1 = (int) (count1 + l1);
            }
            customerPageVO.setProductNum(count1);

            customerPageList.add(customerPageVO);
        }
        PageResult<CustomerPageVO> customerPageVOPageResult = new PageResult<>();
        customerPageVOPageResult.setList(customerPageList);
        customerPageVOPageResult.setTotal(chargeCustomerInfoPageResult.getTotal());
        return CommonResult.success(customerPageVOPageResult);
    }

    @Override
    public CommonResult<?> queryCustomer(Long id) {
        ChargeCustomerInfo byId = getById(id);
        return CommonResult.success(byId);
    }

    @Override
    public CommonResult<?> updateCus(ChargeCustomerInfo dto) {
        updateById(dto);
        return CommonResult.success();
    }

    @Override
    public CommonResult<PageResult<ChargeForOther>> chargePage(ChargeForOtherQueryDTO dto) {
        // 1. 执行分页查询
        PageResult<ChargeForOther> pageResult = chargeForOtherMapper.selectPage(dto);

        // 2. 批量获取关联信息
        if (!CollectionUtils.isEmpty(pageResult.getList())) {
            // 提取所有ID
            Set<Long> customerIds = pageResult.getList().stream()
                    .map(ChargeForOther::getCustomerId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Set<String> accountIds = pageResult.getList().stream()
                    .map(ChargeForOther::getAccountId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Set<Long> walletIds = pageResult.getList().stream()
                    .map(ChargeForOther::getWalletId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 批量查询客户信息
            Map<Long, CustomerNameDTO> customerInfoMap = customerIds.isEmpty() ?
                    Collections.emptyMap() : customerMapper.batchGetCustomerNames(customerIds);

            // 批量查询账户信息
            Map<String, AccountNameDTO> accountInfoMap = accountIds.isEmpty() ?
                    Collections.emptyMap() : customerAccountMapper.batchGetAccountNames(accountIds);

            // 批量查询钱包信息
            Map<Long, WalletNameDTO> walletInfoMap = walletIds.isEmpty() ?
                    Collections.emptyMap() : customerAccountWalletApi.batchGetWalletNames(walletIds);

            // 设置关联信息
            pageResult.getList().forEach(item -> {
                // 设置客户名称
                if (item.getCustomerId() != null) {
                    CustomerNameDTO customerInfo = customerInfoMap.get(item.getCustomerId());
                    item.setCustomerName(customerInfo != null ? customerInfo.getCustomerName() : null);
                }

                // 设置账户名称
                if (item.getAccountId() != null) {
                    AccountNameDTO accountInfo = accountInfoMap.get(item.getAccountId());
                    item.setAccountName(accountInfo != null ? accountInfo.getAccountName() : null);
                }

                // 设置钱包名称
                if (item.getWalletId() != null) {
                    WalletNameDTO walletInfo = walletInfoMap.get(item.getWalletId());
                    item.setWalletName(walletInfo != null ? walletInfo.getWalletName() : null);
                }
            });
        }

        return CommonResult.success(pageResult);
    }

    @Override
    public CommonResult<?> chargeForOtherDetail(Long id) {
        ChargeForOther chargeForOther = chargeForOtherMapper.selectById(id);

        if (ObjectUtil.isEmpty(chargeForOther)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "数据为空");

        }
        CommonResult<ChargeCustomerAccountWalletsDO> walletInfo = customerAccountWalletApi.getWalletInfo(chargeForOther.getWalletId());
        ChargeCustomerAccountWalletsDO chargeCustomerAccountWallets = walletInfo.getData();
        if (ObjectUtil.isEmpty(chargeCustomerAccountWallets)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "钱包信息为空");

        }
        chargeForOther.setCustomerName(customerMapper.getName(chargeForOther.getCustomerId()));
        chargeForOther.setCurrencyCode(chargeCustomerAccountWallets.getCurrencyCode());
        return CommonResult.success(chargeForOther);
    }

    @Override
    public CommonResult<?> createCharge(ChargeForOther charge) {
        ChargeCustomerInfo one = getOne(Wrappers.<ChargeCustomerInfo>lambdaQuery()
                .eq(ChargeCustomerInfo::getCustomerCode, charge.getCustomerCode()).last("limit 1"));

        if (ObjectUtil.isNotEmpty(one)) {
            charge.setCustomerId(one.getId());
            charge.setCustomerName(one.getCustomerName());
            charge.setCurrencyCode(one.getCurrencyCode());
        }
        chargeForOtherMapper.insert(charge);
        return CommonResult.success();
//        if (charge == null || CollectionUtils.isEmpty(charge.getFile())) {
//            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST, "附件不能为空");
//        }
//
//        List<String> successUrls = new ArrayList<>();
//        List<String> failedFiles = new ArrayList<>();
//
//        // 2. 处理文件上传
//        for (MultipartFile file : charge.getFile()) {
//            try {
//                // 2.1 校验文件
//                if (!isValidFile(file)) {
//                    failedFiles.add(file.getOriginalFilename());
//                    continue;
//                }
//
//                // 2.2 上传文件
//                FileCreateReqDTO dto = new FileCreateReqDTO();
//                dto.setName(file.getOriginalFilename());
//                dto.setPath(file.getOriginalFilename());
//                dto.setContent(file.getBytes());
//
//                CommonResult<String> uploadResult = fileApi.createFile(dto);
//
//                if (uploadResult.isSuccess()) {
//                    successUrls.add(uploadResult.getData());
//                } else {
//                    failedFiles.add(file.getOriginalFilename());
//                }
//            } catch (Exception e) {
//                log.error("文件[{}]上传异常", file.getOriginalFilename(), e);
//                failedFiles.add(file.getOriginalFilename());
//            }
//        }
//
//        // 3. 保存数据（只要有成功上传的文件）
//        if (!successUrls.isEmpty()) {
//            try {
//                ChargeCustomerInfo one = getOne(Wrappers.<ChargeCustomerInfo>lambdaQuery()
//                        .eq(ChargeCustomerInfo::getCustomerCode, charge.getCustomerCode()).last("LIMIT 1"));
//                charge.setCustomerId(one.getId());
//                charge.setFileUrl(String.join(",", successUrls));
//                chargeForOtherMapper.insert(charge);
//            } catch (Exception e) {
//                log.error("保存数据失败", e);
//                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "系统保存数据失败");
//            }
//        } else {
//            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST, "所有文件上传失败");
//        }
//
//        // 4. 返回结果
//        if (failedFiles.isEmpty()) {
//            return CommonResult.success();
//        } else {
//            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST, "部分文件上传失败: " + String.join(",", failedFiles));
//        }
    }

    @Override
    public CommonResult<?> getCustomerAccountTree() {
        // 1. 查询所有客户基础信息
        List<ChargeCustomerInfo> customers = customerMapper.selectList(null);
        // 2. 转换为树形结构
        List<CustomerAccountTreeVO> collect = customers.stream().map(customer -> {
            CustomerAccountTreeVO treeVO = new CustomerAccountTreeVO();
            treeVO.setCustomerId(customer.getId());
            treeVO.setCustomerCode(customer.getCustomerCode());
            treeVO.setCustomerName(customer.getCustomerName());

            // 3. 查询该客户下的所有账户
            List<ChargeCustomerAccountsInfo> accounts = customerAccountMapper.selectList(
                    new LambdaQueryWrapper<ChargeCustomerAccountsInfo>()
                            .eq(ChargeCustomerAccountsInfo::getCustomerId, customer.getId())
            );

            // 4. 转换账户数据
            List<CustomerAccountTreeVO.AccountWallet> accountWallets = accounts.stream().map(account -> {
                CustomerAccountTreeVO.AccountWallet accountWallet = new CustomerAccountTreeVO.AccountWallet();
                accountWallet.setAccountId(account.getId());
                accountWallet.setAccountCode(account.getAccountCode());
                accountWallet.setAccountName(account.getAccountName());

                return accountWallet;
            }).collect(Collectors.toList());
            treeVO.setAccountWalletList(accountWallets);
            return treeVO;
        }).collect(Collectors.toList());
        return CommonResult.success(collect);
    }

    @Override
    public CommonResult<?> getCustomerTree() {
        // 1. 查询所有客户基础信息
        List<ChargeCustomerInfo> customers = customerMapper.selectList(null);

        // 2. 转换为树形结构
        List<CustomerAccountWalletTreeVO> collect = customers.stream().map(customer -> {
            CustomerAccountWalletTreeVO treeVO = new CustomerAccountWalletTreeVO();
            treeVO.setCustomerId(customer.getId());
            treeVO.setCustomerCode(customer.getCustomerCode());
            treeVO.setCustomerName(customer.getCustomerName());

            // 3. 查询该客户下的所有账户
            List<ChargeCustomerAccountsInfo> accounts = customerAccountMapper.selectList(
                    new LambdaQueryWrapper<ChargeCustomerAccountsInfo>()
                            .eq(ChargeCustomerAccountsInfo::getCustomerId, customer.getId())
            );

            // 4. 转换账户数据
            List<CustomerAccountWalletTreeVO.AccountWallet> accountWallets = accounts.stream().map(account -> {
                CustomerAccountWalletTreeVO.AccountWallet accountWallet = new CustomerAccountWalletTreeVO.AccountWallet();
                accountWallet.setAccountId(account.getId());
                accountWallet.setAccountCode(account.getAccountCode());
                accountWallet.setAccountName(account.getAccountName());

                // 5. 查询该账户下的所有钱包


                List<Long> longs = Collections.singletonList(account.getId());
                CommonResult<List<ChargeCustomerAccountWalletsDO>> listCommonResult = customerAccountWalletApi.queryWallet(longs);
                List<ChargeCustomerAccountWalletsDO> wallets = listCommonResult.getData();
                // 6. 转换钱包数据
                List<CustomerAccountWalletTreeVO.Wallet> walletList = wallets.stream().map(wallet -> {
                    CustomerAccountWalletTreeVO.Wallet w = new CustomerAccountWalletTreeVO.Wallet();
                    w.setWalletId(wallet.getId());
                    w.setWalletCode(wallet.getWalletsCode());
                    w.setWalletName(wallet.getWalletsName());
                    w.setWalletsType(wallet.getWalletsType());
                    w.setCurrencyCode(wallet.getCurrencyCode());
                    w.setBalance(wallet.getWalletsType() == 0 ? wallet.getCashBalance() : wallet.getPointsBalance());
                    return w;
                }).collect(Collectors.toList());

                accountWallet.setWalletList(walletList);
                return accountWallet;
            }).collect(Collectors.toList());

            treeVO.setAccountWalletList(accountWallets);
            return treeVO;
        }).collect(Collectors.toList());
        return CommonResult.success(collect);
    }

    @Override
    public CommonResult<?> chargeForOtherCancel(Long id) {
        chargeForOtherMapper.deleteById(id);
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> archive(Long id) {
        ChargeCustomerInfo byId = getById(id);
        // 有订阅在进行中，结算没完成的客户都无法存档
        ChargeSubscriptionsDO chargeSubscriptionsDO = chargeSubscriptionsMapper.selectOne(Wrappers.<ChargeSubscriptionsDO>lambdaQuery()
                .eq(ChargeSubscriptionsDO::getCustomerId, id).last("limit 1"));
        if (ObjectUtil.isNotEmpty(chargeSubscriptionsDO)) {
            if (chargeSubscriptionsDO.getStatus() == 1 || chargeSubscriptionsDO.getStatus() == 2) {
                return CommonResult.error(500, "有订阅在进行中，无法存档");
            }
        }
        CommonResult<Long> longCommonResult = billFeeApi.computeCustomerUnPaidBillNum(id);
        if (longCommonResult.getData() > 0) {
            return CommonResult.error(500, "有未结清账单,无法存档");
        }
        if (byId.getStatus() == 1) {
            return CommonResult.error(500, "活跃用户无法存档");
        }
        byId.setStatus(0);
        updateById(byId);
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> delete(Long id) {
        ChargeCustomerInfo byId = getById(id);
        if (byId.getStatus() != 0) {
            return CommonResult.error(500, "非已存档客户无法删除");
        }
        // 若已存档客户有过订阅，则无法删除
        Long l = chargeSubscriptionsMapper.selectCount(Wrappers.<ChargeSubscriptionsDO>lambdaQuery()
                .eq(ChargeSubscriptionsDO::getCustomerId, id)
                .apply(null));
        if (l > 0) {
            return CommonResult.error(500, "已存档客户有过订阅，无法删除");
        }
        removeById(id);
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> queryByCode(String customerCode) {
        ChargeCustomerInfo one = getOne(Wrappers.<ChargeCustomerInfo>lambdaQuery().eq(ChargeCustomerInfo::getCustomerCode, customerCode)
                .last("limit 1"));
        if (ObjectUtil.isEmpty(one)) {
            return CommonResult.error(500, "客户编码不存在");
        }
        List<ChargeCustomerAccountsInfo> chargeCustomerAccountsInfos = customerAccountMapper.selectList(Wrappers.<ChargeCustomerAccountsInfo>lambdaQuery()
                .eq(ChargeCustomerAccountsInfo::getCustomerId, one.getId()));
        return CommonResult.success(chargeCustomerAccountsInfos);
    }

    @Override
    public CommonResult<?> queryNameByCode(String customerCode) {
        ChargeCustomerInfo one = getOne(Wrappers.<ChargeCustomerInfo>lambdaQuery().eq(ChargeCustomerInfo::getCustomerCode, customerCode)
                .last("limit 1"));
        return CommonResult.success(one);
    }

    @Override
    public CommonResult<?> queryWallet(Long id) {
        List<ChargeCustomerAccountWalletsDO> walletCashOnly = customerAccountWalletApi.getWalletCashOnly(id);
        return CommonResult.success(walletCashOnly);
    }

    @Override
    public CommonResult<?> chargeForOtherAllocation(Long id) {
        ChargeForOther chargeForOther = chargeForOtherMapper.selectById(id);
        chargeForOther.setStatus(1);
        chargeForOtherMapper.updateById(chargeForOther);
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> queryAll() {
        List<ChargeCustomerInfo> list = list();
        return CommonResult.success(list);
    }

    @Override
    public List<CommonVO> findNameByIds(CommonDTO commonDTO) {
        List<ChargeCustomerInfo> customerInfos = customerMapper.findNameByIds(commonDTO);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(customerInfos)) {
            return customerInfos.stream().map(t -> CommonVO.builder().id(t.getId()).name(t.getCustomerName()).build()).toList();
        }
        return List.of();
    }

    @Override
    public ChargeCustomerInfoVO findById(Long id) {
        CommonResult<?> commonResult = queryCustomer(id);
        if (commonResult.isSuccess()) {
            Object data = commonResult.getData();
            if (data instanceof ChargeCustomerInfo) {
                return ChargeCustomerInfoConvert.INSTANCE.convert((ChargeCustomerInfo) data);
            }
        }
        return null;
    }

    @Override
    public void exportCustomer(CustomerQueryDTO dto, HttpServletResponse response) {
        // ① 设置分页大小为无限制
        dto.setPageSize(PageParam.PAGE_SIZE_NONE);
        CommonResult<PageResult<CustomerPageVO>> pageResultCommonResult = this.pageQuery(dto);
        PageResult<CustomerPageVO> page = pageResultCommonResult.getData();
        // ② 导出 Excel
        try {

            ExcelUtils.write(response, "客户列表.xls", "客户", CustomerPageVO.class, page.getList());
        } catch (Exception e) {
            log.error("导出客户信息异常", e);
            // ⑤ 如果发生IO异常，则抛出ServiceException异常
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUPPLIER_ACCOUNT_EXPORT_ERROR);
        }
    }


    /**
     * 文件校验方法
     */
    private boolean isValidFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        // 校验文件大小 (5MB)
        if (file.getSize() > 5 * 1024 * 1024) {
            return false;
        }

        // 校验文件类型
        String contentType = file.getContentType();
        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename != null ?
                originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase() : "";

        return (contentType != null &&
                (contentType.equals("application/pdf") ||
                        contentType.equals("image/jpeg") ||
                        contentType.equals("image/png"))) ||
                (Arrays.asList("pdf", "jpg", "jpeg", "png").contains(fileExtension));
    }

    /**
     * 创建客户时，默认创建账户和钱包
     *
     * @param dto 客户
     */
    private void createAccount(ChargeCustomerInfo dto) {
        ChargeCustomerAccountsInfo accountsInfo = getChargeCustomerAccountsInfo(dto);
        customerAccountMapper.insert(accountsInfo);
        ChargeCustomerAccountWalletsDO accountWallets = new ChargeCustomerAccountWalletsDO();
        accountWallets.setAccountId(accountsInfo.getId());
        accountWallets.setWalletsName(dto.getCustomerName() + "的钱包");
        accountWallets.setWalletsCode(generateId("wa"));
        accountWallets.setWalletsType(0);
        accountWallets.setCurrencyCode(dto.getCurrencyCode());
        customerAccountWalletApi.createWallet(accountWallets);
    }

    @NotNull
    private static ChargeCustomerAccountsInfo getChargeCustomerAccountsInfo(ChargeCustomerInfo dto) {
        String accountId = generateId("ac");
        ChargeCustomerAccountsInfo accountsInfo = new ChargeCustomerAccountsInfo();
        BeanUtil.copyProperties(dto, accountsInfo);
        accountsInfo.setId(IdUtil.getSnowflakeNextId());
        accountsInfo.setAccountCode(accountId);
        accountsInfo.setInvoiceTaxNumber(dto.getInvoiceTaxNumber());
        accountsInfo.setAccountName(dto.getCustomerName() + "账户");
        accountsInfo.setInvoiceEntityId(dto.getEntityId());
        accountsInfo.setCountryCode(dto.getCountryCode());
        accountsInfo.setCustomerId(dto.getId());
        accountsInfo.setAccountType(0);
        accountsInfo.setWalletEnabled(1);
        accountsInfo.setIsSettlementAccount(1);
        accountsInfo.setIsDefault(1);
        accountsInfo.setParentId(dto.getId());
        accountsInfo.setType(dto.getCustomerType());
        accountsInfo.setBillingCycle(0);
        accountsInfo.setBillingDay(1);
        accountsInfo.setPaymentPeriod(7);
        accountsInfo.setCurrencyCode(dto.getCurrencyCode());
        return accountsInfo;
    }

    public static String generateId(String prefix) {
        long timestamp = DateUtil.current();
        int randomNum = RandomUtil.randomInt(100000, 999999);
        return prefix + timestamp + randomNum;
    }
}
