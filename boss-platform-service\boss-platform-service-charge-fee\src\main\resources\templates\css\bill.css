/* 修改后 */
/* 基础样式 */
/* 基础重置与全局设置 */
/** {*/
/*    box-sizing: border-box;*/
/*    margin: 0;*/
/*    padding: 0;*/
/*    -fs-pdf-font-embed: embed; !* 强制字体嵌入PDF *!*/
/*}*/

/*html {*/
/*    background-color: aliceblue;*/
/*}*/

/* 楷体字体声明 */
@font-face {
    font-family: '楷体';
    src: local('SimKai'), local('KaiTi'); /* 兼容不同系统 */
}

body {
    font-family: Arial, '楷体', sans-serif;
    font-size: 10.5pt; /* 统一基准字号 */
    line-height: 1.5;
    color: #333;
    margin: 0;
    padding: 15pt;
    /*-fs-pdf-word-wrap: break-word; !* 强制换行 *!*/
}

/*!* 主容器 - 固定尺寸防止溢出 *!*/
/*.invoice-container {*/
/*    width: 600pt !important; !* 强制宽度 *!*/
/*    max-width: 600pt !important; !* 双重保护 *!*/
/*    margin: 0 auto;*/
/*    padding: 0 22.5pt;*/
/*    background-color: #fff;*/
/*    overflow: hidden; !* 隐藏溢出内容 *!*/
/*}*/

/*!* 头部布局 - 表格布局更稳定 *!*/
/*.invoice-header {*/
/*    display: table;*/
/*    width: 100%;*/
/*    margin-top: 37.5pt;*/
/*    margin-bottom: 15pt;*/
/*    !* table-layout: fixed; *! !* 固定表格布局 *!*/
/*}*/

/*.header-row {*/
/*    display: table-row;*/
/*}*/

/*.header-cell {*/
/*    display: table-cell;*/
/*    vertical-align: middle;*/
/*}*/

/*.main-logo {*/
/*    width: 90pt;*/
/*    height: 90pt;*/
/*    max-width: 90pt; !* 防止图片拉伸 *!*/
/*    padding-left: 75pt;*/
/*}*/

/*.invoice-meta {*/
/*    text-align: right;*/
/*    font-size: 10.5pt;*/
/*    color: #999999;*/
/*    padding-top: 5pt;*/
/*}*/

/*!* 买卖方信息 - 表格布局 *!*/
/*.parties {*/
/*    display: table;*/
/*    width: 100%;*/
/*    margin-top: 15pt;*/
/*    font-size: 10.5pt;*/
/*    color: #999999;*/
/*    !* table-layout: fixed *!;*/
/*}*/

/*.party-row {*/
/*    display: table-row;*/
/*}*/

/*.party-cell {*/
/*    display: table-cell;*/
/*    vertical-align: top;*/
/*    width: 50%;*/
/*    padding-right: 10pt;*/
/*}*/

/*.party-cell:last-child {*/
/*    padding-right: 0;*/
/*}*/

/*.textAlignRight {*/
/*    text-align: right;*/
/*}*/

/*!* 表格样式 - 严格限制尺寸 *!*/
/*.invoice-items {*/
/*    width: 100%;*/
/*    margin-top: 12pt;*/
/*    border-collapse: collapse;*/
/*    !* table-layout: fixed; *! !* 固定列宽 *!*/
/*}*/

/*.invoice-items th,*/
/*.invoice-items td {*/
/*    padding: 6pt 3pt;*/
/*    overflow: hidden;*/
/*    !*text-overflow: ellipsis; !* 文字过长显示省略号 *!*!*/
/*}*/

/*.tableTr {*/
/*    background-color: #F0F0F0;*/
/*    height: 37.5pt;*/
/*    font-size: 10.5pt;*/
/*    text-align: left;*/
/*    font-weight: normal;*/
/*    color: #666666;*/
/*}*/

/*!* 精确列宽控制 *!*/
/*.width25 {*/
/*    width: 25%;*/
/*    max-width: 150pt; !* 600pt × 25% *!*/
/*}*/

/*.width15 {*/
/*    width: 15%;*/
/*    max-width: 90pt; !* 600pt × 15% *!*/
/*}*/

/*!* 总计区域 - 表格布局 *!*/
/*.total-area {*/
/*    display: table;*/
/*    width: 100%;*/
/*    margin-top: 18pt;*/
/*}*/

/*.total-row {*/
/*    display: table-row;*/
/*}*/

/*.total-cell {*/
/*    display: table-cell;*/
/*    padding: 3pt 0;*/
/*}*/

/*.total-left {*/
/*    width: 135pt;*/
/*    text-align: left;*/
/*}*/

/*.total-right {*/
/*    text-align: right;*/
/*}*/

/*!* 法律声明 *!*/
/*.law {*/
/*    margin-top: 18pt;*/
/*    font-size: 10.5pt;*/
/*    color: #666666;*/
/*}*/

/*.law span {*/
/*    display: block;*/
/*    margin-bottom: 3pt;*/
/*}*/

/*.title {*/
/*    font-weight: bold;*/
/*    margin-bottom: 6pt;*/
/*}*/

/*!* 银行信息 *!*/
/*.bank {*/
/*    margin-top: 12pt;*/
/*    font-size: 10.5pt;*/
/*    color: #333333;*/
/*    margin-bottom: 12pt;*/
/*}*/

/*.bankItem {*/
/*    margin-bottom: 6pt;*/
/*}*/

/*.width200 {*/
/*    width: 150pt;*/
/*    max-width: 150pt;*/
/*}*/

/*!* 底部背景条 *!*/
/*.bg {*/
/*    height: 15pt;*/
/*    width: 600pt;*/
/*    margin: 0 auto;*/
/*    padding: 0 22.5pt;*/
/*    background-color: antiquewhite;*/
/*}*/

/*!* 分页控制 *!*/
/*.page-break {*/
/*    page-break-after: always;*/
/*    height: 0;*/
/*    visibility: hidden;*/
/*}*/

/*!* 强制换行 *!*/
/*.force-wrap {*/
/*    word-wrap: break-word;*/
/*    white-space: pre-wrap;*/
/*}*/

html {
    background-color: aliceblue;
}

.invoice-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 800px;
    margin: 0 auto;
    padding: 0 30px;
    background-color: #fff;
    /* box-sizing: border-box; */
}

.invoice-header {
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
    margin-top: 50px;
    margin-bottom: 20px;
}

.main-logo {
    width: 120px;
    height: 120px;
    margin-left: 100px;
}

.invoice-meta {
    display: flex;
    flex-direction: column;
    text-align: right;
    font-size: 14px;
    color: #999999;
}

.parties {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 14px;
    color: #999999;
}

.seller {
    display: flex;
    flex-direction: column;
}

.textAlginRight {
    text-align: right;
}

.invoice-items {
    margin-top: 16px;
}

.invoice-items {
    border-collapse: collapse; /* 这一行是可选的，用于去除单元格间的默认间距 */
}

.bordered-table th, .bordered-table td {
    border: none;
}

.tableTr {
    background-color: #F0F0F0;
    height: 50px;
    font-size: 14px;
    text-align: left;
    font-weight: normal;
    color: #666666;
}

.width25 {
    width: 25%;
}

.width15 {
    width: 25%;
}

.padding0 {
    padding: 0;
    margin: 0;
}

.total {
    display: table; /* 替代 flex */
    width: 100%; /* 确保宽度占满 */
    margin-top: 18pt; /* 改用物理单位 (24px ≈ 18pt) */
    margin-bottom: 0;
    font-size: 12pt; /* 14px ≈ 12pt */
    color: #333333;
    text-align: right; /* 整体右对齐 */
}

.total {
    width: 100%;
    margin-top: 18pt;
    font-size: 12pt;
    color: #333333;
    text-align: right; /* 整体右对齐 */
}

.total-row {
    display: block;
    text-align: right;
}

.total-item {
    display: inline-block;
    text-align: left;
    width: 135pt; /* 左侧标签固定宽度 */
    vertical-align: top;
}

.total-value {
    display: inline-block;
    text-align: right;
    min-width: 90pt; /* 右侧数值最小宽度 */
    vertical-align: top;
}

.total-row > span {
    display: block;
    white-space: nowrap; /* 防止内容换行 */
    line-height: 1.2; /* 紧凑行距 */
    padding: 0;
    margin: 0;
}

.law {
    display: flex;
    flex-direction: column;
    margin-top: 24px;
    font-size: 14px;
    color: #666666;
}

.title {
    margin-bottom: 8px;
}

.law span {
    width: 400px;
}

.bank {
    display: table; /* 替代 flex */
    width: 100%; /* 确保宽度 */
    margin-top: 12pt; /* 改用物理单位 */
    margin-bottom: 12pt;
    font-size: 12pt;
    color: #333333;
    border-collapse: collapse; /* 消除单元格间隙 */
}

.bankItem {
    display: table-cell; /* 替代 flex 子项 */
    vertical-align: top; /* 顶部对齐 */
    padding-right: 10pt; /* 单元格间距 */
}

.width200 {
    width: 150pt; /* 固定左侧列宽 */
}

.bankItem span {
    display: block; /* 让每个<span>独占一行 */
    margin-bottom: 3pt; /* 行间距 */
}

.width200 {
    width: 200px;
}

.bg {
    /* margin-top: 16px; */
    height: 20px;
    width: 800px;
    margin: 0 auto;
    padding: 0 30px;
    background-color: antiquewhite;
}
