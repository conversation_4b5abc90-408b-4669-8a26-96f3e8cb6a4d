package com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup;

import com.baomidou.mybatisplus.annotation.TableField;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.ChargeCustomerInvoiceDTO;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16 16:40
 */
@Schema(description = "手工账单-账单详情对象 Response DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MakeupIncomeBillDetailVo {
    /**
     * 账单id
     */
    @Schema(description = "账单id")
    private Long billId;

    /**
     * 客户id
     */
    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerName;

    /**
     * 账户id
     */
    @Schema(description = "账户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

        /**
     * 账户名称
     */
    @Schema(description = "账户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String  accountName;

    /**
     * 主体id
     */
    @Schema(description = "主体id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long entityId;

    /**
     * 主体名称
     */
    @Schema(description = "主体名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String entityName;

    /**
     * 合同id
     */
    @Schema(description = "合同id")
    private Long contractId;

    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String contractName;

    /**
     * 钱包id
     */
    @Schema(description = "钱包id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long walletId;

    /**
     * 钱包名称
     */
    @Schema(description = "钱包名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String walletName;

    /**
     * 账单状态	 0-草稿, 1-待支付, 2-已支付, 3-未结清
     */
    @Schema(description = "账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer billStatus;

    /**
     * 税率
     */
    @Schema(description = "税率")
    private BigDecimal taxRate;


    /**
     * 税率
     */
    @TableField("tax_amount")
    @Schema(description = "税率金额总计")
    private BigDecimal taxAmount;


    // 各个服务总价  = 各个服务原价  - 折扣  + 税率
    @TableField("sub_total_amount")
    @Schema(description = "总价=各个服务总价之和")
    private BigDecimal subTotalAmount;


    /**
     * 优惠的金额
     */
    @Schema(description = "账单优惠的金额")
    private BigDecimal discountAmount;


    /**
     * 优惠的金额
     */
    @Schema(description = "总计优惠的金额 = 产品优惠金额 + 服务优惠金额 + 账单优惠金额")
    private BigDecimal subDiscountAmount;

    /**
     * 已开票金额
     */
    @Schema(description = "已开票金额")
    private BigDecimal invoicedAmount;

    /**
     * 可开票金额(=优惠价-已开票金额)
     */
    @Schema(description = "可开票金额(=优惠价-已开票金额)")
    private BigDecimal availableInvoiceAmount;

    /**
     * 含税总金额->发票金额[可开发票金额的最大值]
     */
    @Schema(description = "含税总金额->发票金额[可开发票金额的最大值]")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */
    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 实际支付时间戳
     */
    @Schema(description = "实际支付时间戳")
    private Long paymentTime;

    /**
     * 出账时间戳
     */
    @Schema(description = "出账时间戳")
    private Long billingTime;

    @Schema(description = "出账开始时间戳（毫秒）")
    private Long billingStartTime;

    @Schema(description = "出账结束时间戳（毫秒）")
    private Long billingEndTime;

    /**
     * 数据创建时间戳
     */
    @Schema(description = "数据创建时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long createTime;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>")
    @TableField("discount_details")
    private String discountDetails;

    @Schema(description = "优惠配置")
    private List<BillDiscountConfigDTO> coupons;

    @Schema(description = "客户详细信息")
    private ChargeCustomerInfoVO customer;

    @Schema(description = "发票客户账户详细信息")
    private ChargeCustomerInvoiceDTO customerInvoice;

    @Schema(description = "主体详细信息")
    private EntityDetailsVO entity;


    @Schema(description = "发票客户账户详细信息")
    private EntityDetailsVO entityInvoice;


    @Schema(description = "产品信息")
    private List<MakeupIncomeProductBilDetailVo> products;
    @Schema(description = "产品名称信息")
    private List<String> productNames;
    @Schema(description = "产品服务名称信息")
    private List<String> serviceNames;

    /**
     * 已支付金额
     */
    @TableField("paid_amount")
    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    /**
     * 未支付金额
     */
    @TableField("unpaid_amount")
    @Schema(description = "未支付金额")
    private BigDecimal unpaidAmount;



    @TableField("bill_no")
    @Schema(description = "账单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billNo;

    /**
     * 开票退款金额
     */
    @TableField("refund_invoice_amount")
    @Schema(description = "开票退款金额")
    private BigDecimal refundInvoiceAmount;


    @Schema(description = "账单所有内容")
    private List<BillContent> showContents;

    @Schema(description = "账单所有优惠")
    private List<BillCoupon> showCoupons;
}
