package com.linkcircle.boss.framework.config;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-07 10:12
 * @description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MvcWarmUpHttp implements CommandLineRunner {

    private final ServerProperties serverProperties;

    @Override
    public void run(String... args) throws Exception {
        String contextPath = serverProperties.getServlet().getContextPath();
        Integer port = serverProperties.getPort();
        String url = String.format("http://127.0.0.1:%s%s/actuator/health", port, contextPath);
        try (HttpResponse response = HttpRequest.get(url)
                .timeout(5000)
                .execute()) {
            log.info("MvcWarmUpHttp: {}", response.body());
        } catch (Exception ex) {
            log.error("MvcWarmUpHttp error: {}", ex.getMessage());
        }
    }
}