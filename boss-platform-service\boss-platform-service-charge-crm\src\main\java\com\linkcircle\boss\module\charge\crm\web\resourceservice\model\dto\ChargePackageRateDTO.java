package com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 套餐类型费率
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChargePackageRateDTO {
    @Schema(description = "套餐包含量")
    @NotNull(message = "套餐包含量 不允许为空")
    private BigDecimal packageInclude;

    @Schema(description = "套餐包含单位")
    @NotBlank(message = "套餐包含单位 不允许为空")
    private String packageIncludeUnit;

    @Schema(description = "用量")
    @NotNull(message = "用量 不允许为空")
    private BigDecimal measure;

    @Schema(description = "计量单位")
    @NotBlank(message = "计量单位 不允许为空")
    private String measureUnit;

    @Schema(description = "计量单位是否向上取整 0-不向上取整, 1-向上取整")
    @NotNull(message = "计量单位是否向上取整 不允许为空")
    private Integer measureCeil;

    @Schema(description = "套餐配置列表")
    @Valid
    private List<PackageConfigDTO> packages;

    /**
     * 套餐配置
     */
    @Data
    @Schema(description = "套餐配置")
    public static class PackageConfigDTO {

        @Schema(description = "币种")
        @NotBlank(message = "币种 不允许为空")
        private String currency;

        @Schema(description = "套餐内费率")
        @Valid
        private InPackageRateDTO inPackage;

        @Schema(description = "套餐外费率")
        @Valid
        private OutPackageRateDTO outPackage;
    }

    /**
     * 套餐内费率
     */
    @Data
    @Schema(description = "套餐内费率")
    public static class InPackageRateDTO {

        @Schema(description = "固定金额")
        @NotNull(message = "固定金额 不允许为空")
        private BigDecimal fixCharge;

        @Schema(description = "积分")
        @NotNull(message = "积分 不允许为空")
        private Integer integralCharge;
    }

    /**
     * 套餐外费率
     */
    @Data
    @Schema(description = "套餐外费率")
    public static class OutPackageRateDTO {

        @Schema(description = "套餐外价格模型 fixed-固定, level-阶梯")
        @NotBlank(message = "固定金额 不允许为空")
        private String outPackagePriceModel;

        @Schema(description = "固定费率（套餐外）")
        @Valid
        private FixedOutPackageRateDTO fixRatePricesOutPackage;

        @Schema(description = "阶梯费率列表（套餐外）")
        @Valid
        private List<StairOutPackageRateDTO> stairRatePricesOutPackage;
    }

    /**
     * 固定费率（套餐外）
     */
    @Data
    @Schema(description = "固定费率（套餐外）")
    public static class FixedOutPackageRateDTO {

        @Schema(description = "固定金额")
        @NotNull(message = "固定金额 不允许为空")
        private BigDecimal fixCharge;

        @Schema(description = "积分")
        @NotNull(message = "固定金额 不允许为空")
        private Integer integralCharge;

        @Schema(description = "币种")
        @NotBlank(message = "币种 不允许为空")
        private String currency;
    }

    /**
     * 阶梯费率（套餐外）
     */
    @Data
    @Schema(description = "阶梯费率（套餐外）")
    public static class StairOutPackageRateDTO {

        @Schema(description = "阶梯级别")
        @NotNull(message = "阶梯级别 不允许为空")
        private Integer tierLevel;

        @Schema(description = "从金额")
        @NotNull(message = "从金额 不允许为空")
        private BigDecimal min;

        @Schema(description = "到")
        @NotNull(message = "到金额 不允许为空")
        private BigDecimal max;

        @Schema(description = "价格")
        @NotNull(message = " 不允许为空")
        private BigDecimal fixCharge;

        @Schema(description = "积分")
        @NotNull(message = "积分 不允许为空")
        private Integer integralCharge;

        @Schema(description = "每单位")
        @NotNull(message = "每单位 不允许为空")
        private Integer payUnit;

        @Schema(description = "是否全额支付 0-否")
        @NotNull(message = "是否全额支付 不允许为空")
        private Integer isAllPay;
    }

}
