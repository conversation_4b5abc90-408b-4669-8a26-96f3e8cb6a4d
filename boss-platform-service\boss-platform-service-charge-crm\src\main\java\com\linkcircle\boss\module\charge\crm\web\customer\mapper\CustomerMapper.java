package com.linkcircle.boss.module.charge.crm.web.customer.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.linkcircle.boss.module.charge.crm.web.customer.model.dto.CustomerNameDTO;
import com.linkcircle.boss.module.charge.crm.web.customer.model.dto.CustomerQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.customer.model.entity.ChargeCustomerInfo;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * date  2025/6/11 9:33
 */
@Mapper
@InterceptorIgnore(tenantLine = "true", blockAttack = "true", illegalSql = "true")
public interface CustomerMapper extends BaseMapperX<ChargeCustomerInfo> {

    default PageResult<ChargeCustomerInfo> selectPage(CustomerQueryDTO dto) {
        LambdaQueryWrapperX<ChargeCustomerInfo> query = new LambdaQueryWrapperX<ChargeCustomerInfo>()
                .eqIfPresent(ChargeCustomerInfo::getCustomerCode, dto.getCustomerCode())
                .eqIfPresent(ChargeCustomerInfo::getEntityId, dto.getEntityId())
                .eqIfPresent(ChargeCustomerInfo::getStatus, dto.getStatus())
                .likeIfPresent(ChargeCustomerInfo::getCustomerName, dto.getCustomerName())
                .orderByDesc(ChargeCustomerInfo::getCreateTime);
        if (dto.getCustomerId() != null && !dto.getCustomerId().isEmpty()) {
            query.in(ChargeCustomerInfo::getId, dto.getCustomerId());
        }
        return selectPage(dto, query);
    }

    @Select({
            "<script>",
            "SELECT id as customerId, customer_name as customerName",
            "FROM charge_customer_info",
            "WHERE id IN",
            "<foreach collection='customerIds' item='id' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            "</script>"
    })
    @MapKey("customerId")
    Map<Long, CustomerNameDTO> batchGetCustomerNames(@Param("customerIds") Collection<Long> customerIds);


    @Select("SELECT customer_name FROM charge_customer_info where id = #{customerId}")
    String getName(Long customerId);

    default List<ChargeCustomerInfo> findNameByIds(CommonDTO commonDTO) {
        QueryWrapper<ChargeCustomerInfo> wrapper = new QueryWrapper<>();
        wrapper.select("id", "customer_name");
        wrapper.in("id", commonDTO.getIds());
        return selectList(wrapper);
    }
}
