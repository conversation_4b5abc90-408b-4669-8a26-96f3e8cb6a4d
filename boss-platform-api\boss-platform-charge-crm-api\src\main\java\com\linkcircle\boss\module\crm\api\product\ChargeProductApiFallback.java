package com.linkcircle.boss.module.crm.api.product;

import com.linkcircle.boss.framework.common.model.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:02
 */
@Component
@Slf4j
public class ChargeProductApiFallback implements FallbackFactory<ChargeProductApi> {
    @Override
    public ChargeProductApi create(Throwable cause) {
        return commonDTO -> {
            log.error("调用产品中心API失败API失败，accountId: {}, 异常信息: ", commonDTO, cause);
            return CommonResult.error(500, "调用失败: " + cause.getMessage());
        };
    }
}
