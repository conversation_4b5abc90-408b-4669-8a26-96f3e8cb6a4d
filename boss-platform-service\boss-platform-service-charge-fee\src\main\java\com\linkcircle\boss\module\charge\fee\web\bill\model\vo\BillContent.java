package com.linkcircle.boss.module.charge.fee.web.bill.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Schema(description = "账单内容")
@Data
public class BillContent {
    @Schema(description = "描述")
    private String description;
    @Schema(description = "折扣单价")
    private String discountUnitPrice;
    @Schema(description = "用量")
    private String usageCount;
    @Schema(description = "总价")
    private String discountPrice;
    @Schema(description = "税率")
    private String taxRate;
    @Schema(description = "总金额")
    private String totalAmount;

    @Schema(description = "货币单位")
    private String currency;
    @Schema(description = "子内容")
    private List<BillContent> children;

    public BillContent() {
        this.children = new ArrayList<>();
        this.discountUnitPrice = "";
        this.usageCount = "";
        this.totalAmount = "";
        this.taxRate = "";
        this.discountPrice = "";
        this.description = "";
        this.currency = "";
    }
}

