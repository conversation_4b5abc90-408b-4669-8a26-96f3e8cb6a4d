package com.linkcircle.boss.module.crm.api.coupon;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:44
 */
@org.springframework.stereotype.Component
@lombok.extern.slf4j.Slf4j
public class CouponApiFallback implements FallbackFactory<CouponApi> {
    @Override
    public CouponApi create(Throwable cause) {
        return new CouponApi() {
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<java.util.List<com.linkcircle.boss.module.crm.api.common.vo.CommonVO>> findNameByIds(CommonDTO commonDTO) {
                log.error("调用优惠中心API失败，commonDTO: {} 异常信息: ", commonDTO, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
