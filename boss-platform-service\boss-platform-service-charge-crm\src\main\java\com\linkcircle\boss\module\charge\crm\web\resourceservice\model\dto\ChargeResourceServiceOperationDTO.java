package com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 资源服务基本信息通用参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@Schema(description = "资源服务基本信息通用参数")
public abstract class ChargeResourceServiceOperationDTO implements ChargeResourceServiceOperationBaseDTO {


    @Schema(description = "服务名称")
    @NotBlank(message = "服务名称不允许为空")
    private String serviceName;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "发票显示名称")
    private String invoiceShows;

    @Schema(description = "费率类型: 0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费")
    @NotNull(message = "费率类型不允许为空")
    private Integer chargeType;

    @Schema(description = "单位标签")
    private String unitLabel;

    @Schema(description = "支付方式，0:现金，1：积分")
    @NotNull(message = "支付方式不允许为空")
    private Integer paymentOptions;

    @Schema(description = "是否含套餐外,0:不包含，1：包含")
    private Integer inPackage;

    @Schema(description = "间隔时长单位 0：一次性, 1:日，2：周，3：月，4：季度，5：年")
    @NotNull(message = "间隔时长单位不允许为空")
    private Integer period;

    @Schema(description = "间隔时长")
    @NotNull(message = "间隔时长不允许为空")
    private Integer unitPeriod;

    @Schema(description = "货币id集合,当前服务选的所有货币id,用,分隔")
    @NotNull(message = "货币id集合不允许为空")
    private String currencyCollection;

    @Schema(description = "固定费率")
    private List<ChargeFixRateDTO> chargeFixRateDTOS;


    @Schema(description = "阶梯费率")
    private List<ChargeGradientRateDTO> chargeGradientRateDTOS;

    @Schema(description = "套餐费率")
    private ChargePackageRateDTO chargePackageRateDTO;


    @Schema(description = "计量费率")
    private ChargePostpaidRateDTO chargePostpaidRateDTOS;
}
