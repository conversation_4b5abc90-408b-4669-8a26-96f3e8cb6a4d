package com.linkcircle.boss.module.billing.web.bill.transaction.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-07-29 10:50
 * @description 事务消息类型枚举
 */
@Getter
@AllArgsConstructor
public enum TransactionMessageTypeEnum {

    /**
     * 收入服务计费事务
     */
    INCOME_SERVICE_BILLING("INCOME_SERVICE_BILLING", "收入服务计费事务"),

    /**
     * 成本资源计费事务
     */
    COST_RESOURCE_BILLING("COST_RESOURCE_BILLING", "成本资源计费事务");

    /**
     * 事务类型编码
     */
    private final String code;

    /**
     * 事务类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static TransactionMessageTypeEnum getByCode(String code) {
        for (TransactionMessageTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
