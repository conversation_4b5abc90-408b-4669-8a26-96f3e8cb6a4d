-- 主体基本信息表
CREATE TABLE charge_entity_business
(
    `id`             bigint(20) NOT NULL COMMENT 'ID',
    `entity_name`    varchar(64) NOT NULL COMMENT '主体名称',
    `entity_id`      varchar(64) NOT NULL COMMENT '主体id',
    `brand_logo_url` varchar(255)         DEFAULT NULL COMMENT '品牌Logo存储路径/URL',
    `legal_name`     varchar(128)         DEFAULT NULL COMMENT '法定名称',
    `country_code`   char(8)     NOT NULL COMMENT '国家代码（ISO 3166-1 alpha-2 标准，如 CN 表示中国）',
    `postal_code`    varchar(8)           DEFAULT NULL COMMENT '邮政编码',
    `city`           varchar(64)          DEFAULT NULL COMMENT '城市',
    `address`        varchar(64)          DEFAULT NULL COMMENT '地址',
    `tax_number`     varchar(32)          DEFAULT NULL COMMENT '税号',
    `email`          varchar(64)          DEFAULT NULL COMMENT '邮箱',
    `currency_code`  char(8)     NOT NULL COMMENT '货币代码（ISO 4217 标准，如 CNY 表示人民币）',
    `timezone`       varchar(32) NOT NULL COMMENT '时区标识',
    `language_code`  char(4)     NOT NULL COMMENT '官方语言代码（ISO 639-1 标准，如 zh 表示中文）',
    `deleted`        bit(1)      NOT NULL DEFAULT b'0' COMMENT '删除标志:0-否 1-是',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '数据添加时间戳',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '数据变更时间戳',
    `creator`        varchar(32)          DEFAULT NULL COMMENT '创建人',
    `updater`        varchar(32)          DEFAULT NULL COMMENT '修改人',
    `tenant_id`      bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`),
    KEY              `idx_entity_id` (`entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主体基本信息表';

-- 开票记录信息表
CREATE TABLE charge_invoice_record
(
    `id`                   bigint(20) NOT NULL COMMENT '主体ID',
    `invoice_code`         VARCHAR(64)    NOT NULL COMMENT '发票号码',
    `type`                 tinyint(1) NOT NULL COMMENT '发票类型 （0-个人 1-企业）',
    `invoice_type`         tinyint(1)  NOT NULL COMMENT '票据类型 （0-信用票据 1-普票 2-国外发票 3-增值税专票 4-红冲发票）',
    `customer_id`          bigint(20)  NOT NULL COMMENT '关联客户id',
    `account_id`           bigint(20)  NOT NULL COMMENT '关联账户id',
    `entity_id`            bigint(20)  NOT NULL COMMENT '关联主体id',
    `entity_json_str`      text           NOT NULL COMMENT '主体信息JSON字符串',
    `customer_json_str`    text           NOT NULL COMMENT '客户信息JSON字符串',
    `currency_symbol`      varchar(10)    NOT NULL COMMENT '币种符号：$',
    `invoice_amount`       decimal(12, 2) NOT NULL COMMENT '开票金额',
    `invoice_billing_id`   varchar(32)    NOT NULL COMMENT '开票账单id',
    `invoice_billing_no`   varchar(50)    NOT NULL COMMENT '开票账单号码',
    `credit_invoice_id`    bigint(20)    DEFAULT NULL COMMENT '信用票据发票关联id',
    `bill_service_code`    varchar(32)    NOT NULL COMMENT '开票账单服务编码',
    `billTime`             bigint(20)   DEFAULT NULL COMMENT '开票账单时间戳',
    `billType`             tinyint(1)   DEFAULT NULL COMMENT '开票账单类型  1-预付费账单，2-后付费账单,3-手工账单',
    `status`               tinyint(1) NOT NULL COMMENT '发票状态 (0-待审核 1-审核不通过 2-已开票 3-已作废）',
    `invoice_resource_url` varchar(64)             DEFAULT NULL COMMENT '发票资源地址',
    `deleted`              bit(1)         NOT NULL DEFAULT b'0' COMMENT '删除标志:0-否 1-是',
    `create_time`          bigint(20)   DEFAULT NULL COMMENT '数据添加时间戳',
    `update_time`          bigint(20)   DEFAULT NULL COMMENT '数据变更时间戳',
    `creator`              varchar(32)             DEFAULT NULL COMMENT '创建人',
    `updater`              varchar(32)             DEFAULT NULL COMMENT '修改人',
    `tenant_id`            bigint(20)   NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主体基本信息表';

-- 账单、发票配置信息表
CREATE TABLE charge_billing_invoice
(
    `id`                     bigint(20) NOT NULL COMMENT 'id',
    `type`                   tinyint(1) NOT NULL COMMENT '配置类型 （0-发票 1-账单)',
    `entity_id`              bigint(20) NOT NULL COMMENT '关联主体id',
    `number_format`          varchar(64)          DEFAULT NULL COMMENT '号码格式',
    `start_sequence`         varchar(16) NOT NULL COMMENT '开始序列',
    `number_init`            tinyint(1) DEFAULT NULL COMMENT '0-年 1-月 2-日',
    `brand_color`            varchar(64)          DEFAULT NULL COMMENT '品牌色',
    `legal_information`      text                 DEFAULT NULL COMMENT '法律信息',
    `additional_information` text                 DEFAULT NULL COMMENT '附加信息',
    `deleted`                bit(1)      NOT NULL DEFAULT b'0' COMMENT '删除标志:0-否 1-是',
    `create_time`            bigint(20) DEFAULT NULL COMMENT '数据添加时间戳',
    `update_time`            bigint(20) DEFAULT NULL COMMENT '数据变更时间戳',
    `creator`                varchar(32)          DEFAULT NULL COMMENT '创建人',
    `updater`                varchar(32)          DEFAULT NULL COMMENT '修改人',
    `tenant_id`              bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`),
    KEY                      `idx_entity_id` (`entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账单和发票配置信息表';