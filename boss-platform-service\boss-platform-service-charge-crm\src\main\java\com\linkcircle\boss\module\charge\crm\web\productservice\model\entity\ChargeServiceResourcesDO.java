package com.linkcircle.boss.module.charge.crm.web.productservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linkcircle.boss.framework.common.model.common.BaseDO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 服务和资源对应关系表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@TableName("charge_service_resources")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChargeServiceResourcesDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 关联产品服务ID
     */
    @Schema(description = "关联产品服务ID")
    private Long serviceId;

    /**
     * 资源id
     */
    @Schema(description = "资源id")
    private Long resourcesId;

    /**
     * 租户编号
     */
    @Schema(description = "租户编号")
    private Long tenantId;

    /**
     * 资源版本信息
     */
    @TableField(exist = false)
    @Schema(description = "资源版本信息")
    private ChargeResourceServiceVersionInfoVO versionInfoVO;
}
