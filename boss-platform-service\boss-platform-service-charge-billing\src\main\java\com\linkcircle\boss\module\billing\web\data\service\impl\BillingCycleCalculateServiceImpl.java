package com.linkcircle.boss.module.billing.web.data.service.impl;

import com.linkcircle.boss.module.billing.web.data.model.vo.BillingCycleResultVO;
import com.linkcircle.boss.module.billing.web.data.service.BillingCycleCalculateService;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.enums.BillingCycleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.github.cloud.framework.common.constant.TimeConstants;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2025-07-08 17:00
 * @description 出账周期计算服务实现
 */
@Slf4j
@Service
public class BillingCycleCalculateServiceImpl implements BillingCycleCalculateService {

    /**
     * 默认时区
     */
    private static final String DEFAULT_TIMEZONE = TimeConstants.TIMEZONE_SHANGHAI;

    /**
     * 出账周期格式
     */
    private static final DateTimeFormatter BILLING_CYCLE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo) {
        // 调用新的重载方法，默认计算当前周期
        return calculateBillingCycle(accountInfo, false);
    }

    @Override
    public BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo, boolean calculatePreviousCycle) {
        if (accountInfo == null) {
            return BillingCycleResultVO.notAllowed(null, "账户信息为空");
        }

        Long accountId = accountInfo.getAccountId();
        Integer billingCycleType = accountInfo.getBillingCycle();
        Integer billingDay = accountInfo.getBillingDay();
        String timezone = accountInfo.getTimezone();

        // 参数验证
        if (billingCycleType == null) {
            return BillingCycleResultVO.notAllowed(accountId, "出账周期类型为空");
        }

        if (billingDay == null) {
            return BillingCycleResultVO.notAllowed(accountId, "出账日配置为空");
        }

        // 获取出账周期类型枚举
        BillingCycleTypeEnum cycleTypeEnum = BillingCycleTypeEnum.getByType(billingCycleType);
        if (cycleTypeEnum == null) {
            return BillingCycleResultVO.notAllowed(accountId, "不支持的出账周期类型: " + billingCycleType);
        }

        // 设置默认时区
        if (timezone == null || timezone.trim().isEmpty()) {
            timezone = DEFAULT_TIMEZONE;
        }

        try {
            ZoneId zoneId = ZoneId.of(timezone);
            LocalDate currentDate = LocalDate.now(zoneId);

            log.debug("计算出账周期信息 - accountId: {}, 周期类型: {}, 出账日: {}, 时区: {}, 计算上一周期: {}",
                    accountId, cycleTypeEnum, billingDay, timezone, calculatePreviousCycle);

            // 根据出账周期类型计算出账信息
            BillingCycleResultVO resultVO = switch (cycleTypeEnum) {
                case MONTHLY -> calculateMonthlyBilling(accountId, currentDate, billingDay, timezone, calculatePreviousCycle);
                case WEEKLY -> calculateWeeklyBilling(accountId, currentDate, billingDay, timezone, calculatePreviousCycle);
                case QUARTERLY -> calculateQuarterlyBilling(accountId, currentDate, billingDay, timezone, calculatePreviousCycle);
                case YEARLY -> calculateYearlyBilling(accountId, currentDate, billingDay, timezone, calculatePreviousCycle);
            };
            resultVO.setCurrency(accountInfo.getCurrency());
            return resultVO;
        } catch (Exception e) {
            log.error("计算出账周期异常, accountId: {}, timezone: {}, billingDay: {}, calculatePreviousCycle: {}",
                    accountId, timezone, billingDay, calculatePreviousCycle, e);
            return BillingCycleResultVO.notAllowed(accountId, "计算出账周期异常: " + e.getMessage());
        }
    }

    /**
     * 计算月结出账信息（按自然月）
     * 例如：每月1号出账，出账上个自然月（1号到月末）的账单
     */
    private BillingCycleResultVO calculateMonthlyBilling(Long accountId, LocalDate currentDate,
                                                         Integer billingDay, String timezone) {
        return calculateMonthlyBilling(accountId, currentDate, billingDay, timezone, false);
    }

    /**
     * 计算月结出账信息（按自然月，支持上一周期）
     * 例如：每月1号出账，出账上个自然月（1号到月末）的账单
     */
    private BillingCycleResultVO calculateMonthlyBilling(Long accountId, LocalDate currentDate,
                                                         Integer billingDay, String timezone, boolean calculatePreviousCycle) {
        // 验证出账日配置
        if (billingDay < 1 || billingDay > 28) {
            return BillingCycleResultVO.notAllowed(accountId, "月结出账日配置无效，应为1-28: " + billingDay);
        }

        int currentDay = currentDate.getDayOfMonth();

        // 判断是否为出账日（仅在计算当前周期时检查）
        if (!calculatePreviousCycle && currentDay != billingDay) {
            return BillingCycleResultVO.notAllowed(accountId,
                    String.format("当前不是出账日，配置出账日: %d, 当前日期: %d", billingDay, currentDay));
        }

        // 计算目标出账月份
        LocalDate targetMonth;
        String cycleDescription;

        if (calculatePreviousCycle) {
            // 计算上一周期：如果当前是出账日，则计算上上个月；否则计算上个月
            if (currentDay == billingDay) {
                targetMonth = currentDate.minusMonths(2);
                cycleDescription = "上一周期月结出账（自然月）";
            } else {
                targetMonth = currentDate.minusMonths(1);
                cycleDescription = "上一周期月结出账（自然月）";
            }
        } else {
            // 计算当前周期：出账上个自然月的账单
            targetMonth = currentDate.minusMonths(1);
            cycleDescription = "月结出账（自然月）";
        }

        // 按自然月计算：出账目标月份的账单（1号到月末）
        LocalDate billingStartDate = targetMonth.withDayOfMonth(1);
        LocalDate billingEndDate = targetMonth.withDayOfMonth(targetMonth.lengthOfMonth());

        // 转换为时间戳（开始时间为00:00:00，结束时间为23:59:59）
        LocalDateTime startDateTime = billingStartDate.atStartOfDay();
        LocalDateTime endDateTime = billingEndDate.atTime(LocalTime.MAX);

        ZoneId zoneId = ZoneId.of(timezone);
        long startTimestamp = startDateTime.atZone(zoneId).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(zoneId).toInstant().toEpochMilli();

        String billingCycle = billingStartDate.format(BILLING_CYCLE_FORMATTER) + "-" + billingEndDate.format(BILLING_CYCLE_FORMATTER);
        String description = String.format("%s：%s 00:00:00 至 %s 23:59:59",
                cycleDescription, billingStartDate, billingEndDate);

        log.info("月结出账计算完成, accountId: {}, 出账范围: {}, 计算上一周期: {}",
                accountId, description, calculatePreviousCycle);

        return BillingCycleResultVO.allowed(accountId, startTimestamp, endTimestamp,
                BillingCycleTypeEnum.MONTHLY.getType(), billingDay, billingCycle, timezone, description);
    }

    /**
     * 计算周结出账信息（按自然周）
     * 例如：每周一出账，出账上个自然周（周一到周日）的账单
     */
    private BillingCycleResultVO calculateWeeklyBilling(Long accountId, LocalDate currentDate,
                                                        Integer billingDay, String timezone) {
        return calculateWeeklyBilling(accountId, currentDate, billingDay, timezone, false);
    }

    /**
     * 计算周结出账信息（按自然周，支持上一周期）
     * 例如：每周一出账，出账上个自然周（周一到周日）的账单
     */
    private BillingCycleResultVO calculateWeeklyBilling(Long accountId, LocalDate currentDate,
                                                        Integer billingDay, String timezone, boolean calculatePreviousCycle) {
        // 验证出账星期配置
        if (billingDay < 1 || billingDay > 7) {
            return BillingCycleResultVO.notAllowed(accountId, "周结出账星期配置无效，应为1-7: " + billingDay);
        }
        // 1=周一, 7=周日
        int currentDayOfWeek = currentDate.getDayOfWeek().getValue();

        // 判断是否为出账星期（仅在计算当前周期时检查）
        if (!calculatePreviousCycle && currentDayOfWeek != billingDay) {
            return BillingCycleResultVO.notAllowed(accountId,
                    String.format("当前不是出账星期，配置出账星期: %d, 当前星期: %d", billingDay, currentDayOfWeek));
        }

        // 计算目标出账周
        LocalDate targetWeekMonday;
        String cycleDescription;

        if (calculatePreviousCycle) {
            // 计算上一周期：如果当前是出账日，则计算上上周；否则计算上周
            if (currentDayOfWeek == billingDay) {
                targetWeekMonday = currentDate.minusWeeks(2).with(java.time.DayOfWeek.MONDAY);
                cycleDescription = "上一周期周结出账（自然周）";
            } else {
                targetWeekMonday = currentDate.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
                cycleDescription = "上一周期周结出账（自然周）";
            }
        } else {
            // 计算当前周期：出账上个自然周的账单
            targetWeekMonday = currentDate.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
            cycleDescription = "周结出账（自然周）";
        }

        // 按自然周计算：出账目标周的账单（周一到周日）
        LocalDate lastWeekMonday = targetWeekMonday;
        // 找到目标周的周日（自然周结束）
        LocalDate lastWeekSunday = lastWeekMonday.plusDays(6);

        // 转换为时间戳
        LocalDateTime startDateTime = lastWeekMonday.atStartOfDay();
        LocalDateTime endDateTime = lastWeekSunday.atTime(LocalTime.MAX);

        ZoneId zoneId = ZoneId.of(timezone);
        long startTimestamp = startDateTime.atZone(zoneId).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(zoneId).toInstant().toEpochMilli();

        String billingCycle = lastWeekMonday.format(BILLING_CYCLE_FORMATTER) + "-" + lastWeekSunday.format(BILLING_CYCLE_FORMATTER);
        String description = String.format("%s：%s 00:00:00 至 %s 23:59:59",
                cycleDescription, lastWeekMonday, lastWeekSunday);

        log.info("周结出账计算完成, accountId: {}, 出账范围: {}, 计算上一周期: {}",
                accountId, description, calculatePreviousCycle);

        return BillingCycleResultVO.allowed(accountId, startTimestamp, endTimestamp,
                BillingCycleTypeEnum.WEEKLY.getType(), billingDay, billingCycle, timezone, description);
    }

    /**
     * 计算季结出账信息（按自然季度）
     * 例如：每季度第1天出账，出账上个自然季度（季度第一天到季度最后一天）的账单
     */
    private BillingCycleResultVO calculateQuarterlyBilling(Long accountId, LocalDate currentDate,
                                                           Integer billingDay, String timezone) {
        return calculateQuarterlyBilling(accountId, currentDate, billingDay, timezone, false);
    }

    /**
     * 计算季结出账信息（按自然季度，支持上一周期）
     * 例如：每季度第1天出账，出账上个自然季度（季度第一天到季度最后一天）的账单
     */
    private BillingCycleResultVO calculateQuarterlyBilling(Long accountId, LocalDate currentDate,
                                                           Integer billingDay, String timezone, boolean calculatePreviousCycle) {
        // 验证出账日配置
        if (billingDay < 1 || billingDay > 90) {
            return BillingCycleResultVO.notAllowed(accountId, "季结出账日配置无效，应为1-90: " + billingDay);
        }

        // 获取当前季度的第一天
        int currentMonth = currentDate.getMonthValue();
        // 1, 4, 7, 10
        int quarterStartMonth = ((currentMonth - 1) / 3) * 3 + 1;
        LocalDate quarterStart = LocalDate.of(currentDate.getYear(), quarterStartMonth, 1);

        // 计算当前日期是季度中的第几天
        long dayOfQuarter = ChronoUnit.DAYS.between(quarterStart, currentDate) + 1;

        // 判断是否为出账日（仅在计算当前周期时检查）
        if (!calculatePreviousCycle && dayOfQuarter != billingDay) {
            return BillingCycleResultVO.notAllowed(accountId,
                    String.format("当前不是出账日，配置出账日: %d, 当前季度第%d天", billingDay, dayOfQuarter));
        }

        // 计算目标出账季度
        LocalDate targetQuarterStart;
        String cycleDescription;

        if (calculatePreviousCycle) {
            // 计算上一周期：如果当前是出账日，则计算上上季度；否则计算上季度
            if (dayOfQuarter == billingDay) {
                targetQuarterStart = quarterStart.minusMonths(6);
                cycleDescription = "上一周期季结出账（自然季度）";
            } else {
                targetQuarterStart = quarterStart.minusMonths(3);
                cycleDescription = "上一周期季结出账（自然季度）";
            }
        } else {
            // 计算当前周期：出账上个自然季度的账单
            targetQuarterStart = quarterStart.minusMonths(3);
            cycleDescription = "季结出账（自然季度）";
        }

        // 按自然季度计算：出账目标季度的账单（季度第一天到季度最后一天）
        LocalDate lastQuarterStart = targetQuarterStart;
        // 计算目标季度的最后一天
        LocalDate lastQuarterEnd = targetQuarterStart.plusMonths(3).minusDays(1);

        // 转换为时间戳
        LocalDateTime startDateTime = lastQuarterStart.atStartOfDay();
        LocalDateTime endDateTime = lastQuarterEnd.atTime(LocalTime.MAX);

        ZoneId zoneId = ZoneId.of(timezone);
        long startTimestamp = startDateTime.atZone(zoneId).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(zoneId).toInstant().toEpochMilli();

        String billingCycle = lastQuarterStart.format(BILLING_CYCLE_FORMATTER) + "-" + lastQuarterEnd.format(BILLING_CYCLE_FORMATTER);
        String description = String.format("%s：%s 00:00:00 至 %s 23:59:59",
                cycleDescription, lastQuarterStart, lastQuarterEnd);

        log.info("季结出账计算完成, accountId: {}, 出账范围: {}, 计算上一周期: {}",
                accountId, description, calculatePreviousCycle);

        return BillingCycleResultVO.allowed(accountId, startTimestamp, endTimestamp,
                BillingCycleTypeEnum.QUARTERLY.getType(), billingDay, billingCycle, timezone, description);
    }

    /**
     * 计算年结出账信息（按自然年）
     * 例如：每年1月1号出账，出账上个自然年（1月1日到12月31日）的账单
     */
    private BillingCycleResultVO calculateYearlyBilling(Long accountId, LocalDate currentDate,
                                                        Integer billingDay, String timezone) {
        return calculateYearlyBilling(accountId, currentDate, billingDay, timezone, false);
    }

    /**
     * 计算年结出账信息（按自然年，支持上一周期）
     * 例如：每年1月1号出账，出账上个自然年（1月1日到12月31日）的账单
     */
    private BillingCycleResultVO calculateYearlyBilling(Long accountId, LocalDate currentDate,
                                                        Integer billingDay, String timezone, boolean calculatePreviousCycle) {
        // 年结出账日配置：1表示1月1日，32表示2月1日，以此类推
        // 验证出账日配置（1-366，考虑闰年）
        if (billingDay < 1 || billingDay > 366) {
            return BillingCycleResultVO.notAllowed(accountId, "年结出账日配置无效，应为1-366: " + billingDay);
        }

        // 获取当前年份的第一天
        LocalDate yearStart = LocalDate.of(currentDate.getYear(), 1, 1);

        // 计算当前日期是年中的第几天
        long dayOfYear = ChronoUnit.DAYS.between(yearStart, currentDate) + 1;

        // 判断是否为出账日（仅在计算当前周期时检查）
        if (!calculatePreviousCycle && dayOfYear != billingDay) {
            return BillingCycleResultVO.notAllowed(accountId,
                    String.format("当前不是出账日，配置出账日: %d, 当前年第%d天", billingDay, dayOfYear));
        }

        // 检查闰年情况（仅在计算当前周期时检查）
        if (!calculatePreviousCycle) {
            boolean isLeapYear = currentDate.isLeapYear();
            if (!isLeapYear && billingDay > 365) {
                return BillingCycleResultVO.notAllowed(accountId, "非闰年，跳过第" + billingDay + "天的年结出账");
            }
        }

        // 计算目标出账年份
        int targetYear;
        String cycleDescription;

        if (calculatePreviousCycle) {
            // 计算上一周期：如果当前是出账日，则计算上上年；否则计算上年
            if (dayOfYear == billingDay) {
                targetYear = currentDate.getYear() - 2;
                cycleDescription = "上一周期年结出账（自然年）";
            } else {
                targetYear = currentDate.getYear() - 1;
                cycleDescription = "上一周期年结出账（自然年）";
            }
        } else {
            // 计算当前周期：出账上个自然年的账单
            targetYear = currentDate.getYear() - 1;
            cycleDescription = "年结出账（自然年）";
        }

        // 按自然年计算：出账目标年份的账单（1月1日到12月31日）
        LocalDate billingStartDate = LocalDate.of(targetYear, 1, 1);
        LocalDate billingEndDate = LocalDate.of(targetYear, 12, 31);

        // 转换为时间戳
        LocalDateTime startDateTime = billingStartDate.atStartOfDay();
        LocalDateTime endDateTime = billingEndDate.atTime(LocalTime.MAX);

        ZoneId zoneId = ZoneId.of(timezone);
        long startTimestamp = startDateTime.atZone(zoneId).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(zoneId).toInstant().toEpochMilli();

        String billingCycle = billingStartDate.format(BILLING_CYCLE_FORMATTER) + "-" + billingEndDate.format(BILLING_CYCLE_FORMATTER);
        String description = String.format("年结出账（自然年）：%s 00:00:00 至 %s 23:59:59",
                billingStartDate, billingEndDate);

        log.info("年结出账计算完成, accountId: {}, 出账范围: {}", accountId, description);

        return BillingCycleResultVO.allowed(accountId, startTimestamp, endTimestamp,
                BillingCycleTypeEnum.YEARLY.getType(), billingDay, billingCycle, timezone, description);
    }
}