<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.billing.web.bill.product.mapper.PostpaidProductServiceIncomeBillMapper">
    <select id="selectByServiceIds"
            resultType="com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO">
        SELECT
        t1.product_service_bill_id,
        t1.service_id,
        t1.service_code,
        t1.currency,
        t1.original_price,
        t1.discounted_price,
        t1.discount_amount,
        t1.amount_without_tax,
        t1.amount_with_tax,
        t1.billing_status
        FROM postpaid_product_service_income_bill t1
        WHERE EXISTS (
        SELECT 1
        FROM (
        SELECT UNNEST(ARRAY[
        <foreach collection="serviceIds" item="id" separator=",">
            #{id}
        </foreach>
        ]) AS sid
        ) t2
        WHERE t2.sid = t1.service_id
        )
        AND t1.subscribe_id = #{subscriptionId}
        AND t1.product_id = #{productId}
        AND t1.billing_start_time = #{startTime}
        AND t1.billing_end_time = #{endTime}
        AND t1.deleted = false
    </select>
</mapper>