package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.enums.BillingProcessStatusEnum;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.IncomePostpaidBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidBillingService;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidProductServiceBillService;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CustomerAccountDataService;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.billing.web.manager.BillIdManager;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.BillStatusEnum;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-07 16:12
 * @description 后付费出账服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomePostpaidBillingServiceImpl implements IncomePostpaidBillingService {

    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final SubscriptionDataService subscriptionDataService;
    private final CustomerAccountDataService customerAccountDataService;
    private final IncomePostpaidProductServiceBillService incomePostpaidProductServiceBillService;
    private final BillIdManager billIdManager;
    private final RedissonUtil redissonUtil;
    private final RocketMQTemplate rocketMQTemplate;

    @Override
    public void processSingleAccount(Long accountId, PaymentTypeEnum paymentType) {
        log.info("开始处理单个账户出账, accountId: {}, paymentType: {}",
                accountId, paymentType);

        // 1. 查询账户信息，获取该账户的出账周期配置
        Optional<CustomerAccountVO> accountInfoOpt = customerAccountDataService.getCustomerAccountInfo(accountId);
        if (accountInfoOpt.isEmpty()) {
            log.warn("未找到账户信息，跳过出账, accountId: {}", accountId);
            return;
        }

        CustomerAccountVO accountInfo = accountInfoOpt.get();

        // 2. 查询账户的订阅详情列表
        Optional<List<AccountSubscriptionsVO>> subscriptionsOpt = subscriptionDataService.getAccountSubscriptions(accountId, PaymentTypeEnum.POSTPAID);

        if (subscriptionsOpt.isEmpty()) {
            log.info("账户无有效订阅，跳过出账, accountId: {}", accountId);
            return;
        }

        List<AccountSubscriptionsVO> subscriptions = subscriptionsOpt.get();

        if (CollUtil.isEmpty(subscriptions)) {
            log.info("账户无符合条件的订阅，跳过出账, accountId: {}, paymentType: {}", accountId, paymentType);
            return;
        }

        log.info("账户符合条件的订阅数量: {}, accountId: {}, paymentType: {}", subscriptions.size(), accountId, paymentType);

        // 2. 遍历订阅，处理每个订阅的产品和服务
        for (AccountSubscriptionsVO subscription : subscriptions) {
            try {
                if (!PaymentTypeEnum.POSTPAID.getMethod().equals(subscription.getPaymentType())) {
                    continue;
                }

                // 先判断订阅是否生效
                if (!isSubscriptionActive(subscription)) {
                    log.info("订阅未生效，跳过出账, subscriptionId: {}, accountId: {}", subscription.getId(), accountId);
                    continue;
                }

                processSubscriptionBilling(subscription, accountInfo);
            } catch (Exception e) {
                log.error("处理订阅出账异常, subscriptionId: {}, accountId: {}", subscription.getId(), accountId, e);
            }
        }

        log.info("账户出账处理完成, accountId: {}, 处理订阅数: {}", accountId, subscriptions.size());
    }

    /**
     * 处理订阅出账
     *
     * @param subscription 订阅信息
     * @param accountInfo  账户信息
     */
    public void processSubscriptionBilling(AccountSubscriptionsVO subscription, CustomerAccountVO accountInfo) {
        Long subscriptionId = subscription.getId();
        Long accountId = subscription.getAccountId();

        try {
            log.debug("订阅出账信息: {}", JsonUtils.toJsonString(subscription));

            // 1. 检查订阅是否有详情数据
            if (CollUtil.isEmpty(subscription.getDetails())) {
                log.warn("订阅无详情数据，跳过出账, subscriptionId: {}", subscriptionId);
                return;
            }

            // 2. 遍历订阅详情，处理每个时间段的产品和服务
            for (AccountSubscriptionsVO.Detail detail : subscription.getDetails()) {
                if (CollUtil.isEmpty(detail.getProducts())) {
                    log.warn("订阅详情无产品数据，跳过, subscriptionId: {}, detailId: {}", subscriptionId, detail.getId());
                    continue;
                }

                // 3. 遍历产品，处理每个产品下的服务
                for (AccountSubscriptionsVO.Product product : detail.getProducts()) {
                    if (CollUtil.isEmpty(product.getServices())) {
                        log.warn("产品无服务数据，跳过, productId: {}, productName: {}", product.getProductId(), product.getProductName());
                        continue;
                    }

                    // 4. 遍历服务，为每个服务创建出账记录
                    for (AccountSubscriptionsVO.Service service : product.getServices()) {
                        processServiceBilling(subscription, detail, product, service, accountInfo);
                    }
                }
            }

            log.info("订阅出账处理完成, subscriptionId: {}", subscriptionId);
        } catch (Exception e) {
            log.error("处理订阅出账异常, subscriptionId: {}, accountId: {}", subscriptionId, accountId, e);
            throw e;
        }
    }

    /**
     * 处理服务出账
     *
     * @param subscription 订阅信息
     * @param detail       时间段信息
     * @param product      产品信息
     * @param service      服务信息
     * @param accountInfo  账户信息
     */
    @Override
    public void processServiceBilling(AccountSubscriptionsVO subscription,
                                      AccountSubscriptionsVO.Detail detail,
                                      AccountSubscriptionsVO.Product product,
                                      AccountSubscriptionsVO.Service service,
                                      CustomerAccountVO accountInfo) {
        Long subscriptionId = subscription.getId();
        Long accountId = subscription.getAccountId();
        Long serviceId = service.getServiceId();
        String serviceCode = service.getServiceCode();
        Integer period = service.getPeriod();
        Integer unitPeriod = service.getUnitPeriod();
        String timezone = accountInfo.getTimezone();
        Long startTime = detail.getStartTime();
        log.info("开始处理服务出账, subscriptionId: {}, accountId: {}, serviceId: {}, serviceCode: {}",
                subscriptionId, accountId, serviceId, serviceCode);

        // 按量计费 周期 使用账户配置
        if (ChargeRateTypeEnum.USAGE.getType().equals(service.getChargeType())) {
            period = 1;
            unitPeriod = accountInfo.getBillingCycle();
            Integer billingDay = accountInfo.getBillingDay();
            // 当前时间 日
            int nowDay = LocalDate.now(ZoneId.of(accountInfo.getTimezone())).getDayOfMonth();
            if (billingDay != nowDay) {
                log.info("未到达计费周期出账日，跳过创建, subscriptionId: {}, accountId: {}, serviceId: {}, serviceCode: {}, billingDay: {}, nowDay: {}",
                        subscriptionId, accountId, serviceId, serviceCode, billingDay, nowDay);
                return;
            }
        }

        CyclePeriodResultVO cyclePeriodResultVO = cyclePeriodCalculateService.calculateCyclePeriod(unitPeriod, timezone,
                System.currentTimeMillis(), startTime, period, true);
        cyclePeriodResultVO.setCurrency(accountInfo.getCurrency());
        cyclePeriodResultVO.setRateTypeEnum(ChargeRateTypeEnum.getByType(service.getChargeType()));
        if (!cyclePeriodResultVO.isSuccess()) {
            log.warn("周期开始时间计算失败，跳过创建, subscriptionId: {}, accountId: {}, serviceId: {}, serviceCode: {}, errorMessage: {}",
                    subscriptionId, accountId, serviceId, serviceCode, cyclePeriodResultVO.getErrorMessage());
            return;
        }

        try {
            // 1. 检查是否已经存在服务账单（先检查Redis缓存，再查数据库）
            if (checkBillExists(serviceId, subscriptionId,
                    cyclePeriodResultVO.getCycleStartTime(), cyclePeriodResultVO.getCycleEndTime())) {
                log.info("服务账单已存在（缓存或数据库），跳过创建, accountId: {}, serviceCode: {}, subscriptionId: {}, billingCycle: {}",
                        accountId, serviceCode, cyclePeriodResultVO.getBillingCycle(), subscriptionId);
                return;
            }

            // 2. 创建服务账单
            PostpaidProductServiceIncomeBillDO serviceIncomeBillDO = createServiceBill(subscription, product, service, cyclePeriodResultVO);

            boolean success = sendBillMessage(serviceIncomeBillDO,
                    subscription, detail, product, service, cyclePeriodResultVO);
            log.info("服务出账消息发送完成: {}, billId: {}, accountId: {}, serviceId: {}",
                    success, serviceIncomeBillDO.getProductServiceBillId(), accountId, serviceId);
        } catch (Exception e) {
            log.error("处理服务出账异常, subscriptionId: {}, serviceId: {}", subscriptionId, serviceId, e);
            throw e;
        }
    }

    private boolean sendBillMessage(PostpaidProductServiceIncomeBillDO serviceIncomeBillDO,
                                    AccountSubscriptionsVO subscription,
                                    AccountSubscriptionsVO.Detail detail,
                                    AccountSubscriptionsVO.Product product,
                                    AccountSubscriptionsVO.Service service,
                                    CyclePeriodResultVO cyclePeriodResultVO) {
        // 构建消息
        IncomePostpaidBillingMessageDTO message = new IncomePostpaidBillingMessageDTO();
        message.setServiceIncomeBillDO(serviceIncomeBillDO);
        message.setSubscription(subscription);
        message.setDetail(detail);
        message.setProduct(product);
        message.setService(service);
        message.setCyclePeriodResultVO(cyclePeriodResultVO);
        Message<IncomePostpaidBillingMessageDTO> msg = MessageBuilder
                .withPayload(message)
                .build();

        String incomeServiceBillingMessageNormalTopic = ChargeTopicUtils.getIncomeServiceBillingMessageNormalTopic();
        SendResult sendResult = rocketMQTemplate.syncSend(incomeServiceBillingMessageNormalTopic, msg);
        log.info("收入服务出账消息发送成功, billId: {}, sendResult: {}", serviceIncomeBillDO.getProductServiceBillId(), sendResult);
        return true;
    }

    /**
     * 创建服务账单
     *
     * @param subscription        订阅信息
     * @param product             产品信息
     * @param service             服务信息
     * @param cyclePeriodResultVO 出账周期
     * @return 服务账单
     */
    private PostpaidProductServiceIncomeBillDO createServiceBill(AccountSubscriptionsVO subscription,
                                                                 AccountSubscriptionsVO.Product product,
                                                                 AccountSubscriptionsVO.Service service,
                                                                 CyclePeriodResultVO cyclePeriodResultVO) {
        long currentTime = System.currentTimeMillis();
        return PostpaidProductServiceIncomeBillDO.builder()
                .productServiceBillId(billIdManager.createBillIdLong(BillTypeEnum.INCOME_SERVICE, cyclePeriodResultVO.getCycleEndTime()))
                .billingTime(currentTime)
                .serviceId(service.getServiceId())
                .serviceCode(service.getServiceCode())
                .billingType(service.getChargeType())
                .billStatus(BillStatusEnum.WAIT_BILL.getStatus())
                .billingStatus(BillingProcessStatusEnum.WAITING.getStatus())
                .billId(null)
                .entityId(subscription.getEntityId())
                .contractId(subscription.getContractId())
                .customerId(subscription.getCustomerId())
                .accountId(subscription.getAccountId())
                .walletId(subscription.getWalletsId())
                .planId(subscription.getPlanId())
                .productId(product.getProductId())
                .subscribeId(subscription.getId())
                .paymentMethod(service.getPaymentOptions())
                .usageCount(BigDecimal.ZERO)
                .taxRate(subscription.getRate())
                .originalPrice(BigDecimal.ZERO)
                .amountWithTax(BigDecimal.ZERO)
                .amountWithoutTax(BigDecimal.ZERO)
                .currency(cyclePeriodResultVO.getCurrency())
                .chargeUnitCount(BigDecimal.ZERO)
                .chargeUsageCount(BigDecimal.ZERO)
                .chargeMeasure(BigDecimal.ZERO)
                .chargeMeasureCeil(0)
                .createTime(currentTime)
                .deleted(false)
                .billingStartTime(cyclePeriodResultVO.getCycleStartTime())
                .billingEndTime(cyclePeriodResultVO.getCycleEndTime())
                .billingCycleType(cyclePeriodResultVO.getPeriodUnitEnum().getUnit())
                .billingCycle(cyclePeriodResultVO.getBillingCycle())
                .timezone(cyclePeriodResultVO.getTimezone())
                .build();
    }

    /**
     * 判断订阅是否生效 状态
     *
     * @param subscription 订阅信息
     * @return 是否生效
     */
    private boolean isSubscriptionActive(AccountSubscriptionsVO subscription) {
        if (subscription == null) {
            return false;
        }

        if (!subscription.hasValidSubscription()) {
            log.info("订阅未生效, subscriptionId: {}", subscription.getId());
            return false;
        }

        log.info("订阅在出账周期内生效, subscriptionId: {}", subscription.getId());
        return true;
    }

    /**
     * 检查服务账单是否已存在（先检查Redis缓存，再查数据库）
     *
     * @param serviceId      服务编码
     * @param subscriptionId 订阅ID
     * @param cycleStartTime 周期开始时间
     * @param cycleEndTime   周期结束时间
     * @return 是否已存在
     */
    protected boolean checkBillExists(Long serviceId, Long subscriptionId, Long cycleStartTime, Long cycleEndTime) {
        // 1. 先检查Redis缓存
        String cacheKey = ChargeCacheUtils.getServiceBillCacheKey(serviceId, subscriptionId, cycleStartTime, cycleEndTime);
        String exist = redissonUtil.get(cacheKey);
        if (Objects.nonNull(exist)) {
            log.info("Redis缓存中服务账单标记已存在, serviceId: {}, subscriptionId: {}, cycleStartTime: {}, cycleEndTime: {}",
                    serviceId, subscriptionId, cycleStartTime, cycleEndTime);
            return true;
        }

        // 2. 再查数据库
        PostpaidProductServiceIncomeBillDO incomeBillDO = incomePostpaidProductServiceBillService.getBillExists(serviceId,
                subscriptionId, cycleStartTime, cycleEndTime);

        if (Objects.isNull(incomeBillDO)) {
            return false;
        }

        if (BillingProcessStatusEnum.WAITING.getStatus().equals(incomeBillDO.getBillingStatus())) {
            return false;
        }

        // 3. 如果数据库中存在，则设置Redis缓存标记，防止重复查询数据库
        Duration duration = Duration.ofMillis(cycleEndTime - cycleStartTime);
        boolean setResult = redissonUtil.setNx(cacheKey, System.currentTimeMillis(), duration);
        log.info("数据库中服务账单已存在，设置Redis缓存标记: {}, serviceId: {}, subscriptionId: {}, cycleStartTime: {}, cycleEndTime: {}",
                setResult, serviceId, subscriptionId, cycleStartTime, cycleEndTime);
        return true;
    }

}
