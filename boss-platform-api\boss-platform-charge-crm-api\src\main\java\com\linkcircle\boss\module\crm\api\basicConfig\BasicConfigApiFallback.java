package com.linkcircle.boss.module.crm.api.basicConfig;

import com.linkcircle.boss.framework.common.model.CommonResult;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:47
 */
@org.springframework.stereotype.Component
@lombok.extern.slf4j.Slf4j
public class BasicConfigApiFallback  implements FallbackFactory<BasicConfigApi> {
    @Override
    public BasicConfigApi create(Throwable cause) {
        return new BasicConfigApi() {
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO> queryInvoice(Long id,Integer type){
                log.error("调用基本配置中API失败，id: {},type: {} 异常信息: ", id,type, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}