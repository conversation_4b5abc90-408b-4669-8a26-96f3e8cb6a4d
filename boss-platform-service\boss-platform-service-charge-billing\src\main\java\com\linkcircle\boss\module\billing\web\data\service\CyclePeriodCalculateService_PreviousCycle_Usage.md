# 周期计算服务上一周期功能使用指南

## 功能概述

`CyclePeriodCalculateService` 现已支持获取上一周期的功能。通过新增的 `boolean getPreviousCycle` 参数，您可以选择获取当前周期或上一周期的信息。

## 新增方法

### 1. 主入口方法
```java
/**
 * 计算周期信息（支持获取上一周期）
 * @param getPreviousCycle true-获取上一周期，false-获取当前周期
 */
CyclePeriodResultVO calculateCyclePeriod(Integer periodUnit, String timezone, 
                                       long businessTime, long startTime, int period, 
                                       boolean getPreviousCycle);
```

### 2. 各周期类型方法
```java
// 日周期
CyclePeriodResultVO calculateDayCyclePeriod(String timezone, long businessTime, 
                                          long startTime, int intervalDays, 
                                          boolean getPreviousCycle);

// 周周期  
CyclePeriodResultVO calculateWeekCyclePeriod(String timezone, long businessTime, 
                                           long startTime, int intervalWeeks, 
                                           boolean getPreviousCycle);

// 月周期
CyclePeriodResultVO calculateMonthCyclePeriod(String timezone, long businessTime, 
                                            long startTime, int intervalMonths, 
                                            boolean getPreviousCycle);

// 季度周期
CyclePeriodResultVO calculateQuarterCyclePeriod(String timezone, long businessTime, 
                                              long startTime, int intervalQuarters, 
                                              boolean getPreviousCycle);

// 年周期
CyclePeriodResultVO calculateYearCyclePeriod(String timezone, long businessTime, 
                                           long startTime, int intervalYears, 
                                           boolean getPreviousCycle);
```

## 使用示例

### 基础用法

```java
@Resource
private CyclePeriodCalculateService cyclePeriodCalculateService;

// 获取当前月周期
CyclePeriodResultVO currentCycle = cyclePeriodCalculateService.calculateMonthCyclePeriod(
    "Asia/Shanghai", 
    System.currentTimeMillis(), 
    startTime, 
    1,
    false  // 获取当前周期
);

// 获取上一月周期
CyclePeriodResultVO previousCycle = cyclePeriodCalculateService.calculateMonthCyclePeriod(
    "Asia/Shanghai", 
    System.currentTimeMillis(), 
    startTime, 
    1,
    true   // 获取上一周期
);
```

### 实际业务场景

```java
/**
 * 计费场景：需要获取上一周期进行补账
 */
public void processSupplementaryBilling(Long subscriptionId) {
    // 获取订阅信息
    SubscriptionInfo subscription = getSubscriptionInfo(subscriptionId);
    
    // 计算上一周期信息
    CyclePeriodResultVO previousCycle = cyclePeriodCalculateService.calculateMonthCyclePeriod(
        subscription.getTimezone(),
        System.currentTimeMillis(),
        subscription.getStartTime(),
        subscription.getIntervalMonths(),
        true  // 获取上一周期
    );
    
    if (previousCycle.isSuccess()) {
        // 使用上一周期信息进行补账
        processSupplementaryBilling(subscriptionId, previousCycle);
    } else {
        log.warn("无法获取上一周期信息: {}", previousCycle.getErrorMessage());
    }
}
```

## 边界情况处理

### 1. 第一个周期的上一周期
```java
// 如果当前是第一个周期，获取上一周期会返回失败
CyclePeriodResultVO result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
    timezone, startTime, startTime, 1, true);

if (!result.isSuccess()) {
    // 错误信息: "不存在上一周期，当前已是第一个周期"
    System.out.println(result.getErrorMessage());
}
```

### 2. 业务时间未到开始时间
```java
// 如果业务时间还未到达开始时间，获取上一周期会返回失败
long earlyBusinessTime = startTime - 24 * 60 * 60 * 1000L; // 开始时间前一天

CyclePeriodResultVO result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
    timezone, earlyBusinessTime, startTime, 1, true);

if (!result.isSuccess()) {
    // 错误信息: "业务时间未到达开始时间，无法获取上一周期"
    System.out.println(result.getErrorMessage());
}
```

### 3. 一次性周期
```java
// 一次性周期不支持获取上一周期
CyclePeriodResultVO result = cyclePeriodCalculateService.calculateCyclePeriod(
    6, timezone, businessTime, startTime, 1, true); // 6 = ONCE

if (!result.isSuccess()) {
    // 错误信息: "一次性周期不支持获取上一周期"
    System.out.println(result.getErrorMessage());
}
```

## 向后兼容性

所有现有的方法调用都保持不变，新功能通过方法重载实现：

```java
// 现有调用方式（获取当前周期）
CyclePeriodResultVO current1 = cyclePeriodCalculateService.calculateMonthCyclePeriod(
    timezone, businessTime, startTime, 1);

// 等价于新方式
CyclePeriodResultVO current2 = cyclePeriodCalculateService.calculateMonthCyclePeriod(
    timezone, businessTime, startTime, 1, false);

// current1 和 current2 返回完全相同的结果
```

## 数学正确性验证

上一周期和当前周期之间的关系：

```java
CyclePeriodResultVO current = cyclePeriodCalculateService.calculateMonthCyclePeriod(
    timezone, businessTime, startTime, interval, false);

CyclePeriodResultVO previous = cyclePeriodCalculateService.calculateMonthCyclePeriod(
    timezone, businessTime, startTime, interval, true);

// 验证关系
assert previous.getCycleIndex() == current.getCycleIndex() - 1;
assert previous.getCycleEndTime() == current.getCycleStartTime() - 1;
```

## 性能说明

- 新功能对现有性能无影响
- 上一周期计算复用现有算法，只是调整周期索引
- 时间复杂度保持 O(1)

## 注意事项

1. **边界检查**：获取上一周期时会自动检查边界条件
2. **错误处理**：所有边界情况都会返回明确的错误信息
3. **时区处理**：上一周期计算完全支持时区转换
4. **线程安全**：所有方法都是线程安全的

## 测试建议

建议在使用前进行充分测试，特别关注：
- 跨月、跨年的周期计算
- 不同时区的处理
- 边界情况的处理
- 与现有业务逻辑的兼容性

参考测试类：`CyclePeriodCalculateServicePreviousCycleTest`
