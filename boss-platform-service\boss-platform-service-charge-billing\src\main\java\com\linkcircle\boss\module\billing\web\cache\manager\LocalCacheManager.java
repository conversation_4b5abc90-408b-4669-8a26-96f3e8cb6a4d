package com.linkcircle.boss.module.billing.web.cache.manager;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.linkcircle.boss.module.billing.api.cache.dto.CacheUpdateMessageDTO;
import com.linkcircle.boss.module.billing.config.ChargeBillingProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025-07-23 16:45
 * @description 本地缓存管理器
 */
@Component
@Slf4j
public class LocalCacheManager {

    /**
     * 缓存容器映射 - key是缓存类型，value是对应的缓存实例
     */
    private final Map<String, Cache<String, Object>> cacheMap = new ConcurrentHashMap<>();

    private final ChargeBillingProperties chargeBillingProperties;

    /**
     * 初始化缓存
     */
    public LocalCacheManager(ChargeBillingProperties chargeBillingProperties) {
        this.chargeBillingProperties = chargeBillingProperties;
        initializeCaches();
    }

    /**
     * 初始化各种类型的缓存
     */
    private void initializeCaches() {
        // 客户账户缓存 - 1小时过期，最大1000个
        cacheMap.put(CacheUpdateMessageDTO.Type.CUSTOMER_ACCOUNT,
                createCache(Duration.ofHours(1), 500));

        // 供应商账户缓存 - 1小时过期，最大1000个
        cacheMap.put(CacheUpdateMessageDTO.Type.SUPPLIER_ACCOUNT,
                createCache(Duration.ofHours(1), 500));

        // 秘钥缓存 - 2小时过期，最大500个
        cacheMap.put(CacheUpdateMessageDTO.Type.SECRET_KEY,
                createCache(Duration.ofHours(1), 50));

        // 账户的订阅缓存 - 30分钟过期，最大2000个
        cacheMap.put(CacheUpdateMessageDTO.Type.ACCOUNT_SUBSCRIPTION,
                createCache(Duration.ofHours(1), 1000));

        // 账户的资源采购缓存 - 30分钟过期，最大2000个
        cacheMap.put(CacheUpdateMessageDTO.Type.ACCOUNT_RESOURCE_PURCHASE,
                createCache(Duration.ofHours(1), 1000));

        // 指标单位缓存 - 1小时过期，最大100个
        cacheMap.put(CacheUpdateMessageDTO.Type.METRIC_UNIT,
                createCache(Duration.ofHours(1), 1000));

        // 量表缓存 - 1小时过期，最大1000个
        cacheMap.put(CacheUpdateMessageDTO.Type.SCALE,
                createCache(Duration.ofHours(3), 200));

        log.info("本地缓存初始化完成，共初始化 {} 种缓存类型", cacheMap.size());
    }

    /**
     * 创建缓存实例
     */
    private Cache<String, Object> createCache(Duration expireAfterWrite, long maximumSize) {
        return Caffeine.newBuilder()
                .expireAfterWrite(expireAfterWrite)
                .expireAfterAccess(expireAfterWrite)
                .maximumSize(maximumSize)
                .recordStats()
                .build();
    }

    /**
     * 获取缓存值 - 支持对象类型
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> get(String cacheType, String id, Class<T> clazz) {
        if (!Boolean.TRUE.equals(chargeBillingProperties.getUseLocalCache())) {
            return Optional.empty();
        }
        Cache<String, Object> cache = cacheMap.get(cacheType);
        if (cache == null) {
            log.warn("未找到缓存类型: {}", cacheType);
            return Optional.empty();
        }

        Object value = cache.getIfPresent(id);
        if (value == null) {
            return Optional.empty();
        }

        try {
            return Optional.of((T) value);
        } catch (ClassCastException e) {
            log.warn("缓存值类型转换失败: type={}, id={}, expectedType={}, actualType={}",
                    cacheType, id, clazz.getSimpleName(), value.getClass().getSimpleName());
            return Optional.empty();
        }
    }

    /**
     * 获取List类型缓存值
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<List<T>> getList(String cacheType, String id) {
        if (!Boolean.TRUE.equals(chargeBillingProperties.getUseLocalCache())) {
            return Optional.empty();
        }
        Cache<String, Object> cache = cacheMap.get(cacheType);
        if (cache == null) {
            log.warn("未找到缓存类型: {}", cacheType);
            return Optional.empty();
        }

        Object value = cache.getIfPresent(id);
        if (value == null) {
            return Optional.empty();
        }

        try {
            if (value instanceof List) {
                return Optional.of((List<T>) value);
            } else {
                log.warn("缓存值不是List类型: type={}, id={}, actualType={}",
                        cacheType, id, value.getClass().getSimpleName());
                return Optional.empty();
            }
        } catch (ClassCastException e) {
            log.warn("List类型缓存值转换失败: type={}, id={}, actualType={}",
                    cacheType, id, value.getClass().getSimpleName());
            return Optional.empty();
        }
    }

    /**
     * 获取Map类型缓存值
     */
    @SuppressWarnings("unchecked")
    public <K, V> Optional<Map<K, V>> getMap(String cacheType, String id) {
        if (!Boolean.TRUE.equals(chargeBillingProperties.getUseLocalCache())) {
            return Optional.empty();
        }
        Cache<String, Object> cache = cacheMap.get(cacheType);
        if (cache == null) {
            log.warn("未找到缓存类型: {}", cacheType);
            return Optional.empty();
        }

        Object value = cache.getIfPresent(id);
        if (value == null) {
            return Optional.empty();
        }

        try {
            if (value instanceof Map) {
                return Optional.of((Map<K, V>) value);
            } else {
                log.warn("缓存值不是Map类型: type={}, id={}, actualType={}",
                        cacheType, id, value.getClass().getSimpleName());
                return Optional.empty();
            }
        } catch (ClassCastException e) {
            log.warn("Map类型缓存值转换失败: type={}, id={}, actualType={}",
                    cacheType, id, value.getClass().getSimpleName());
            return Optional.empty();
        }
    }

    /**
     * 设置缓存值
     */
    public void put(String cacheType, String id, Object value) {
        Cache<String, Object> cache = cacheMap.get(cacheType);
        if (cache == null) {
            log.warn("未找到缓存类型: {}", cacheType);
            return;
        }

        cache.put(id, value);
        log.debug("缓存设置成功: type={}, id={}", cacheType, id);
    }

    /**
     * 删除缓存值
     */
    public void evict(String cacheType, String id) {
        Cache<String, Object> cache = cacheMap.get(cacheType);
        if (cache == null) {
            log.warn("未找到缓存类型: {}", cacheType);
            return;
        }

        cache.invalidate(id);
        log.info("缓存删除成功: type={}, id={}", cacheType, id);
    }

}
