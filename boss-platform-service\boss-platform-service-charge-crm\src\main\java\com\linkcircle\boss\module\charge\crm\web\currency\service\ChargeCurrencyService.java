package com.linkcircle.boss.module.charge.crm.web.currency.service;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.crm.web.currency.model.dto.ChargeCurrencyPageQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.currency.model.dto.CurrencySaveReqDTO;
import com.linkcircle.boss.module.charge.crm.web.currency.model.vo.ChargeCurrencyRespVO;
import com.linkcircle.boss.module.charge.crm.web.currency.model.vo.CurrencySelectRespVO;
import com.linkcircle.boss.module.crm.api.currency.vo.CurrencyRespVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <AUTHOR> zyuan
 * @data : 2025-06-25
 */
public interface ChargeCurrencyService {
    PageResult<ChargeCurrencyRespVO> getChargeCurrencyPage(@Valid ChargeCurrencyPageQueryDTO queryDTO);

    CommonResult<?> create(@Valid CurrencySaveReqDTO queryDTO);

    CommonResult<?> deleteById(String currencyCode);

    CommonResult<?> edit(@Valid CurrencySaveReqDTO queryDTO);

    CommonResult<List<CurrencySelectRespVO>> currencySelect();

    CommonResult<List<CurrencyRespVO>> listCurrency();
}
