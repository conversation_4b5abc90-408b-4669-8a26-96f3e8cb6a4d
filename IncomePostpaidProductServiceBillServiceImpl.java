private String getServiceCode(Integer billingType, String serviceCode, Long serviceId, Long subscriptionId) {
        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(billingType);
        switch (rateTypeEnum) {
            case FIXED, TIERED -> {
                return rateTypeEnum.name().toLowerCase();
            }
            case USAGE, PACKAGE -> {
                if (StrUtil.isEmpty(serviceCode)) {
                    throw new ScaleTableNotBindException(ErrorCodeConstants.NOT_BIND_SCALE_TABLE, rateTypeEnum.name(), serviceId);
                }
                return serviceCode;
            }
        }
        return "";
    }