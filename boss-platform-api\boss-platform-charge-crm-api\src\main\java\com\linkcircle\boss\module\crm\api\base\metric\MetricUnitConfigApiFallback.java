package com.linkcircle.boss.module.crm.api.base.metric;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.base.metric.vo.MetricUnitConfigSimpleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:48
 */
@Slf4j
@Component
public class MetricUnitConfigApiFallback implements FallbackFactory<MetricUnitConfigApi> {
    @Override
    public MetricUnitConfigApi create(Throwable cause) {
        return new MetricUnitConfigApi() {
            @Override
            public CommonResult<List<MetricUnitConfigSimpleVO>> getSimpleMetricUnitConfigList() {
                log.error("调用服务-指标单位API失败， 异常信息: ", cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
