-- PostgreSQL版本的账单表建表SQL

-- 客户每日账单表
CREATE TABLE "customer_daily_usage_bill_${year}"
(
    id                   BIGINT      NOT NULL,        -- 主键 ID
    create_time          BIGINT      NOT NULL,        -- 创建时间戳
    bill_date            VARCHAR(16) NOT NULL,        -- 账单日期(yyyyMMddhh)
    timezone             VARCHAR(32) NOT NULL,        -- 时区
    customer_id          BIGINT      NOT NULL,        -- 客户 ID
    account_id           BIGINT      NOT NULL,        -- 账户 ID
    product_id           BIGINT      NOT NULL,        -- 产品 ID
    service_id           BIGINT      NOT NULL,        -- 服务 ID
    usage_count          DECIMAL(18, 2) DEFAULT 0,    -- 使用量
    usage_unit           VARCHAR(16)    DEFAULT NULL, -- 使用量单位
    cash_amount          DECIMAL(18, 6) DEFAULT 0,    -- 现金消费金额
    cash_amount_currency VARCHAR(3),                  -- 现金消费金额货币单位
    point_amount         DECIMAL(18, 0) DEFAULT 0,    -- 积分消费金额

    CONSTRAINT pk_customer_daily_usage_bill PRIMARY KEY (id)
);

-- 为客户每日账单表创建索引
CREATE INDEX idx_customer_daily_account_id_${year} ON "customer_daily_usage_bill_${year}" (account_id);
CREATE INDEX idx_customer_daily_product_id_${year} ON "customer_daily_usage_bill_${year}" (product_id);
CREATE INDEX idx_customer_daily_customer_id_${year} ON "customer_daily_usage_bill_${year}" (customer_id);
CREATE INDEX idx_customer_daily_service_id_${year} ON "customer_daily_usage_bill_${year}" (service_id);

-- 添加表注释
COMMENT ON TABLE "customer_daily_usage_bill_${year}" IS '客户-每日-账单表 ${year}';

-- 添加字段注释
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".id IS '主键 ID';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".create_time IS '创建时间戳';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".bill_date IS '账单日期(yyyyMMddhh)';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".timezone IS '时区';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".customer_id IS '客户 ID';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".account_id IS '账户 ID';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".product_id IS '产品 ID';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".service_id IS '服务 ID';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".usage_count IS '使用量';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".usage_unit IS '使用量单位';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".cash_amount IS '现金消费金额';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".cash_amount_currency IS '现金消费金额货币单位';
COMMENT ON COLUMN "customer_daily_usage_bill_${year}".point_amount IS '积分消费金额';

-- 供应商每日账单表
CREATE TABLE "supplier_daily_usage_bill_${year}"
(
    id                   BIGINT      NOT NULL,        -- 主键 ID
    create_time          BIGINT      NOT NULL,        -- 创建时间戳
    bill_date            VARCHAR(16) NOT NULL,        -- 账单日期(yyyyMMddhh)
    supplier_id          BIGINT      NOT NULL,        -- 供应商 ID
    account_id           BIGINT      NOT NULL,        -- 账户 ID
    resource_id          BIGINT      NOT NULL,        -- 资源 ID
    resource_service_id  BIGINT      NOT NULL,        -- 资源服务 ID
    usage_count          DECIMAL(18, 2) DEFAULT 0,    -- 使用量
    usage_unit           VARCHAR(16)    DEFAULT NULL, -- 使用量单位
    cash_amount          DECIMAL(18, 6) DEFAULT 0,    -- 现金消费金额
    cash_amount_currency VARCHAR(3),                  -- 现金消费金额货币单位
    point_amount         DECIMAL(18, 0) DEFAULT 0,    -- 积分消费金额

    CONSTRAINT pk_supplier_daily_usage_bill PRIMARY KEY (id)
);

-- 为供应商每日账单表创建索引
CREATE INDEX idx_supplier_daily_account_id_${year} ON "supplier_daily_usage_bill_${year}" (account_id);
CREATE INDEX idx_supplier_daily_resource_id_${year} ON "supplier_daily_usage_bill_${year}" (resource_id);
CREATE INDEX idx_supplier_daily_supplier_id_${year} ON "supplier_daily_usage_bill_${year}" (supplier_id);
CREATE INDEX idx_supplier_daily_resource_service_id_${year} ON "supplier_daily_usage_bill_${year}" (resource_service_id);

-- 添加表注释
COMMENT ON TABLE "supplier_daily_usage_bill_${year}" IS '供应商-每日-账单表 ${year}';

-- 添加字段注释
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".id IS '主键 ID';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".create_time IS '创建时间戳';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".bill_date IS '账单日期(yyyyMMddhh)';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".supplier_id IS '供应商 ID';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".account_id IS '账户 ID';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".resource_id IS '资源 ID';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".resource_service_id IS '资源服务 ID';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".usage_count IS '使用量';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".usage_unit IS '使用量单位';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".cash_amount IS '现金消费金额';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".cash_amount_currency IS '现金消费金额货币单位';
COMMENT ON COLUMN "supplier_daily_usage_bill_${year}".point_amount IS '积分消费金额';

-- 后付费产品收入账单表
drop TABLE "postpaid_product_income_bill_${year}";
CREATE TABLE "postpaid_product_income_bill_${year}"
(
    product_bill_id          BIGINT         NOT NULL,          -- 产品账单id
    customer_id              BIGINT         NOT NULL,          -- 客户id
    account_id               BIGINT         NOT NULL,          -- 账户id
    product_id               BIGINT         NOT NULL,          -- 产品id
    entity_id                BIGINT         NOT NULL,          -- 主体id
    contract_id              BIGINT              DEFAULT NULL, -- 合同id
    wallet_id                BIGINT         NOT NULL,          -- 钱包id
    plan_id                  BIGINT              DEFAULT NULL, -- 计划id
    discount_id              VARCHAR(128)        DEFAULT NULL, -- 优惠id
    subscribe_id             BIGINT         NOT NULL,          -- 订阅id
    payment_method           SMALLINT       NOT NULL,          -- 支付方式 0-现金, 1-积分
    bill_status              SMALLINT       NOT NULL,          -- 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
    tax_rate                 DECIMAL(18, 6)      DEFAULT 0,    -- 税率
    original_price           DECIMAL(18, 6)      DEFAULT 0,    -- 目录总价(原单价)
    discounted_price         DECIMAL(18, 6)      DEFAULT 0,    -- 订阅总价(优惠的目录价)
    discount_amount          DECIMAL(18, 6)      DEFAULT 0,    -- 优惠的金额
    bill_discount_amount     DECIMAL(18, 6)      DEFAULT 0,    -- 账单优惠的金额
    invoiced_amount          DECIMAL(18, 6)      DEFAULT 0,    -- 已开票金额
    available_invoice_amount DECIMAL(18, 6)      DEFAULT 0,    -- 可开票金额(=优惠价-已开票金额)
    amount_with_tax          DECIMAL(18, 6)      DEFAULT 0,    -- 含税总金额
    amount_without_tax       DECIMAL(18, 6)      DEFAULT 0,    -- 不含税金额
    paid_amount              DECIMAL(18, 6) NULL DEFAULT 0,    -- 已支付金额
    unpaid_amount            DECIMAL(18, 6) NULL DEFAULT 0,    -- 未支付金额
    currency                 VARCHAR(3)          DEFAULT NULL, -- 货币单位 CNY USD
    payment_time             BIGINT              DEFAULT NULL, -- 实际支付时间戳
    billing_time             BIGINT              DEFAULT NULL, -- 出账时间戳
    billing_start_time       BIGINT              DEFAULT NULL, -- 出账开始时间戳（毫秒）
    billing_end_time         BIGINT              DEFAULT NULL, -- 出账结束时间戳（毫秒）
    billing_cycle_type       SMALLINT            DEFAULT NULL, -- 出账周期类型(dict)，0：每月，1：每周，2：每季度，3：每年
    billing_day              INTEGER             DEFAULT NULL, -- 出账第几天
    billing_cycle            VARCHAR(32)         DEFAULT NULL, -- 出账周期标识（如202507-202509）
    timezone                 VARCHAR(32)         DEFAULT NULL, -- 时区
    create_time              BIGINT         NOT NULL,          -- 数据创建时间戳
    deleted                  BOOLEAN,                          -- 是否删除
    billing_status           SMALLINT            DEFAULT NULL, -- 出账状态 0-待处理, 1-处理中, 2-已完成, 3-失败
    bill_discount_details    JSON                DEFAULT NULL, -- 账单优惠详情json
    callback_url             VARCHAR(500)        DEFAULT NULL, -- 回调地址，异步通知使用
    callback_status          SMALLINT            DEFAULT 0,    -- 回调状态：0-未回调，1-回调成功，2-回调失败
    legal_information        VARCHAR(512)        DEFAULT NULL, -- 法律信息
    additional_information   VARCHAR(512)        DEFAULT NULL, -- 附加信息
    bill_code                varchar(64),                      -- 账单号码-唯一
    refund_invoice_amount    decimal(18, 6),                   -- 开票退款金额
    wallet_deduct_status     SMALLINT       NULL DEFAULT 0,    -- '钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）'
    CONSTRAINT pk_postpaid_product_income_bill_${year} PRIMARY KEY (product_bill_id)
);

-- 为后付费产品收入账单表创建索引
CREATE INDEX idx_postpaid_product_account_id_${year} ON "postpaid_product_income_bill_${year}" (account_id);
CREATE INDEX idx_postpaid_product_customer_id_${year} ON "postpaid_product_income_bill_${year}" (customer_id);
CREATE INDEX idx_postpaid_product_product_id_subscribe_id_${year} ON "postpaid_product_income_bill_${year}" (product_id, subscribe_id, billing_start_time);
CREATE INDEX idx_postpaid_product_bill_code_${year} ON "postpaid_product_income_bill_${year}" (bill_code);
-- 添加表注释
COMMENT ON TABLE "postpaid_product_income_bill_${year}" IS '后付费-收入-按产品收入-账单表 ${year}';

-- 添加字段注释
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".product_bill_id IS '产品账单id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".customer_id IS '客户id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".account_id IS '账户id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".product_id IS '产品id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".entity_id IS '主体id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".contract_id IS '合同id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".wallet_id IS '钱包id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".plan_id IS '计划id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".discount_id IS '优惠id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".subscribe_id IS '订阅id';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".payment_method IS '支付方式 0-现金, 1-积分';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".bill_status IS '账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".tax_rate IS '税率';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".original_price IS '目录总价(原单价)';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".discounted_price IS '订阅总价(优惠的目录价)';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".discount_amount IS '优惠的金额';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".invoiced_amount IS '已开票金额';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".available_invoice_amount IS '可开票金额(=优惠价-已开票金额)';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".amount_with_tax IS '含税总金额';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".amount_without_tax IS '不含税金额';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".currency IS '货币单位 CNY USD';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".payment_time IS '实际支付时间戳';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".billing_time IS '出账时间戳';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".billing_start_time IS '出账开始时间戳（毫秒）';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".billing_end_time IS '出账结束时间戳（毫秒）';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".billing_cycle_type IS '出账周期类型，0：每月，1：每周，2：每季度，3：每年';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".billing_day IS '出账第几天';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".billing_cycle IS '出账周期标识（如202507-202509）';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".timezone IS '时区';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".create_time IS '数据创建时间戳';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".deleted IS '是否删除';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".callback_url IS '回调地址，异步通知使用';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".callback_status IS '回调状态：0-未回调，1-回调成功，2-回调失败';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".bill_discount_details IS '服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".bill_discount_amount IS '账单优惠的金额';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".paid_amount IS '已支付金额';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".unpaid_amount IS '未支付金额';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".billing_status IS '出账状态 0-待处理, 1-处理中, 2-已完成, 3-失败';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".legal_information IS '法律信息';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".additional_information IS '附加信息';
COMMENT ON COLUMN "postpaid_product_income_bill_${year}".wallet_deduct_status IS '钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）';

-- 后付费产品服务收入账单表
drop TABLE if exists "postpaid_product_service_income_bill_${year}";
CREATE TABLE IF NOT EXISTS "postpaid_product_service_income_bill_${year}"
(
    product_service_bill_id BIGINT         NOT NULL,          -- 产品服务账单id
    billing_time            BIGINT              DEFAULT NULL, -- 出账时间戳
    service_id              BIGINT         NOT NULL,          -- 服务id
    service_code            VARCHAR(32)         DEFAULT NULL, -- 服务编码
    billing_type            SMALLINT       NOT NULL,          -- 计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费
    bill_id                 BIGINT              DEFAULT NULL, -- 产品账单id
    entity_id               BIGINT         NOT NULL,          -- 主体id
    contract_id             BIGINT              DEFAULT NULL, -- 合同id
    customer_id             BIGINT         NOT NULL,          -- 客户id
    account_id              BIGINT         NOT NULL,          -- 账户id
    wallet_id               BIGINT         NOT NULL,          -- 钱包id
    plan_id                 BIGINT              DEFAULT NULL, -- 计划id
    product_id              BIGINT         NOT NULL,          -- 产品id
    subscribe_id            BIGINT         NOT NULL,          -- 订阅id
    payment_method          SMALLINT       NOT NULL,          -- 支付方式 0-现金, 1-积分
    usage_count             DECIMAL(18, 2) NOT NULL,          -- 消耗量
    usage_unit              VARCHAR(16)    NULL,              -- 消耗量单位
    charge_usage_count      DECIMAL(18, 2) NOT NULL,          -- 计费消耗量(charge_measure*charge_unit_count)
    charge_unit_count       DECIMAL(18, 2) NOT NULL,          -- 实际计费单位数
    charge_measure          DECIMAL(18, 2) NOT NULL,          -- 计量单位
    charge_measure_unit     VARCHAR(16)    NULL,              -- 计费计量单位
    charge_measure_ceil     SMALLINT       NOT NULL,          -- 计量单位是否向上取整 0-不向上取整, 1-向上取整
    tax_rate                DECIMAL(18, 6)      DEFAULT 0,    -- 税率
    original_unit_price     DECIMAL(18, 6)      DEFAULT 0,    -- 目录价(原单价)
    discounted_unit_price   DECIMAL(18, 6)      DEFAULT 0,    -- 订阅价(优惠的目录价)
    original_price          DECIMAL(18, 6) NULL DEFAULT 0,    -- 目录总价(原单价)
    discounted_price        DECIMAL(18, 6) NULL DEFAULT 0,    -- 订阅总价(优惠的目录价)
    discount_amount         DECIMAL(18, 6) NULL DEFAULT 0,    -- 优惠的金额
    amount_with_tax         DECIMAL(18, 6)      DEFAULT 0,    -- 含税总金额
    amount_without_tax      DECIMAL(18, 6)      DEFAULT 0,    -- 不含税金额
    paid_amount             DECIMAL(18, 6) NULL DEFAULT 0,    -- 已支付金额
    unpaid_amount           DECIMAL(18, 6) NULL DEFAULT 0,    -- 未支付金额
    currency                VARCHAR(3)          DEFAULT NULL, -- 货币单位 CNY USD
    payment_time            BIGINT              DEFAULT NULL, -- 实际支付时间戳
    billing_start_time      BIGINT              DEFAULT NULL, -- 出账开始时间戳（毫秒）
    billing_end_time        BIGINT              DEFAULT NULL, -- 出账结束时间戳（毫秒）
    billing_cycle_type      SMALLINT            DEFAULT NULL, -- 出账周期类型，0：每月，1：每周，2：每季度，3：每年
    billing_day             INTEGER             DEFAULT NULL, -- 出账第几天
    billing_cycle           VARCHAR(32)         DEFAULT NULL, -- 出账周期标识（如202507-202509）
    timezone                VARCHAR(32)         DEFAULT NULL, -- 时区
    bill_status             SMALLINT       NOT NULL,          -- 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
    billing_status          SMALLINT            DEFAULT 0,    -- 出账状态 0-待处理, 1-处理中, 2-已完成, 3-失败
    create_time             BIGINT         NOT NULL,          -- 数据创建时间戳
    deleted                 BOOLEAN,                          -- 是否删除
    rate_details            JSON                DEFAULT NULL, -- 费用详情json
    discount_details        JSON                DEFAULT NULL, -- 服务优惠详情json
    wallet_deduct_status    SMALLINT       NULL DEFAULT 0,    -- 钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）

    CONSTRAINT pk_postpaid_product_service_income_bill_${year} PRIMARY KEY (product_service_bill_id)
);

-- 为后付费产品服务收入账单表创建索引
CREATE INDEX idx_postpaid_product_service_service_id_subscribe_id_${year} ON "postpaid_product_service_income_bill_${year}" (service_id, subscribe_id, billing_start_time);
CREATE INDEX idx_postpaid_product_service_customer_id_${year} ON "postpaid_product_service_income_bill_${year}" (customer_id);
CREATE INDEX idx_postpaid_product_service_product_id_${year} ON "postpaid_product_service_income_bill_${year}" (product_id);
CREATE INDEX idx_postpaid_product_service_account_id_${year} ON "postpaid_product_service_income_bill_${year}" (account_id);

-- 添加表注释
COMMENT ON TABLE "postpaid_product_service_income_bill_${year}" IS '后付费-收入-按产品收入-服务详细-账单表 ${year}';

-- 添加字段注释
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".product_service_bill_id IS '产品服务账单id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".billing_time IS '出账时间戳';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".service_id IS '服务id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".service_code IS '服务编码';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".billing_type IS '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".bill_id IS '产品账单id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".entity_id IS '主体id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".contract_id IS '合同id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".customer_id IS '客户id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".account_id IS '账户id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".wallet_id IS '钱包id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".plan_id IS '计划id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".product_id IS '产品id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".subscribe_id IS '订阅id';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".payment_method IS '支付方式 0-现金, 1-积分';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".usage_count IS '消耗量';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".usage_unit IS '消耗量单位';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".charge_unit_count IS '计费单位数';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".charge_usage_count IS '计费消耗量(measure*charge_unit_count)';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".charge_measure IS '计量单位数';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".charge_measure_unit IS '计费计量单位';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".charge_measure_ceil IS '计量单位是否向上取整 0-不向上取整, 1-向上取整';

COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".tax_rate IS '税率';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".original_price IS '目录总价(原单价)';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".discounted_price IS '订阅总价(优惠的目录价)';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".discount_amount IS '优惠金额';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".amount_with_tax IS '含税总金额';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".amount_without_tax IS '不含税金额';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".currency IS '货币单位 CNY USD';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".payment_time IS '实际支付时间戳';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".billing_start_time IS '出账开始时间戳（毫秒）';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".billing_end_time IS '出账结束时间戳（毫秒）';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".billing_cycle_type IS '出账周期类型，0：每月，1：每周，2：每季度，3：每年';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".billing_day IS '出账第几天';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".billing_cycle IS '出账周期标识（如202507-202509）';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".timezone IS '时区';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".create_time IS '数据创建时间戳';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".deleted IS '是否删除';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".rate_details IS '费用详情json 见 com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".discount_details IS '服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".original_unit_price IS '目录价(原单价)';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".discounted_unit_price IS '订阅价(优惠的目录价)';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".paid_amount IS '已支付金额';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".unpaid_amount IS '未支付金额';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".billing_status IS '出账状态 0-待处理, 1-处理中, 2-已完成, 3-失败';
COMMENT ON COLUMN "postpaid_product_service_income_bill_${year}".wallet_deduct_status IS '钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）';

-- 后付费资源成本账单表
CREATE TABLE "postpaid_resource_cost_bill_${year}"
(
    resource_bill_id         BIGINT         NOT NULL,     -- 资源成本账单id
    business_time            BIGINT         NOT NULL,     -- 业务产生的时间戳
    service_id               BIGINT         NOT NULL,     -- 服务id
    supplier_id              BIGINT         NOT NULL,     -- 供应商id
    account_id               BIGINT         NOT NULL,     -- 账户id
    resource_id              BIGINT         NOT NULL,     -- 资源id
    entity_id                BIGINT         NOT NULL,     -- 主体id
    discount_id              VARCHAR(128)   DEFAULT NULL, -- 优惠id
    purchase_id              BIGINT         NULL,         -- 采购id
    usage_count              DECIMAL(18, 6) NOT NULL,     -- 消耗量
    usage_unit               VARCHAR(16)    NOT NULL,     -- 消耗量单位
    payment_method           SMALLINT       NOT NULL,     -- 支付方式 0-现金, 1-积分
    billing_type             SMALLINT       NOT NULL,     -- 计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费
    in_trial                 BOOLEAN,                     -- 在试用期内
    bill_status              SMALLINT       NOT NULL,     -- 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
    tax_rate                 DECIMAL(18, 6) DEFAULT 0,    -- 税率
    unit_price               DECIMAL(18, 6) DEFAULT 0,    -- 单价
    original_price           DECIMAL(18, 6) DEFAULT 0,    -- 目录总价(原单价)
    discounted_price         DECIMAL(18, 6) DEFAULT 0,    -- 订阅总价(优惠的目录价)
    discount_amount          DECIMAL(18, 6) DEFAULT 0,    -- 优惠的金额
    invoiced_amount          DECIMAL(18, 6) DEFAULT 0,    -- 已开票金额
    available_invoice_amount DECIMAL(18, 6) DEFAULT 0,    -- 可开票金额(=优惠价-已开票金额)
    amount_with_tax          DECIMAL(18, 6) DEFAULT 0,    -- 含税总金额
    amount_without_tax       DECIMAL(18, 6) DEFAULT 0,    -- 不含税金额
    currency                 VARCHAR(3)     DEFAULT NULL, -- 货币单位 CNY USD
    payment_time             BIGINT         DEFAULT NULL, -- 实际支付时间戳
    billing_time             BIGINT         DEFAULT NULL, -- 出账时间戳
    create_time              BIGINT         NOT NULL,     -- 数据创建时间戳
    deleted                  BOOLEAN,                     -- 是否删除

    CONSTRAINT pk_postpaid_resource_cost_bill PRIMARY KEY (resource_bill_id)
);

-- 为后付费资源成本账单表创建索引
CREATE INDEX idx_postpaid_resource_account_id_${year} ON "postpaid_resource_cost_bill_${year}" (account_id);
CREATE INDEX idx_postpaid_resource_supplier_id_${year} ON "postpaid_resource_cost_bill_${year}" (supplier_id);
CREATE INDEX idx_postpaid_resource_resource_id_${year} ON "postpaid_resource_cost_bill_${year}" (resource_id);

-- 添加表注释
COMMENT ON TABLE "postpaid_resource_cost_bill_${year}" IS '后付费-成本-按资源成本-账单表 ${year}';

-- 添加字段注释
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".resource_bill_id IS '资源成本账单id';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".business_time IS '业务产生的时间戳';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".service_id IS '服务id';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".supplier_id IS '供应商id';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".account_id IS '账户id';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".resource_id IS '资源id';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".entity_id IS '主体id';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".discount_id IS '优惠id';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".purchase_id IS '采购id';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".usage_count IS '消耗量';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".usage_unit IS '消耗量单位';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".payment_method IS '支付方式 0-现金, 1-积分';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".billing_type IS '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".in_trial IS '在试用期内';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".bill_status IS '账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".tax_rate IS '税率';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".unit_price IS '单价';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".original_price IS '目录总价(原单价)';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".discounted_price IS '订阅总价(优惠的目录价)';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".discount_amount IS '优惠的金额';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".invoiced_amount IS '已开票金额';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".available_invoice_amount IS '可开票金额(=优惠价-已开票金额)';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".amount_with_tax IS '含税总金额';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".amount_without_tax IS '不含税金额';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".currency IS '货币单位 CNY USD';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".payment_time IS '实际支付时间戳';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".billing_time IS '出账时间戳';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".create_time IS '数据创建时间戳';
COMMENT ON COLUMN "postpaid_resource_cost_bill_${year}".deleted IS '是否删除';

-- 后付费资源服务成本账单表
CREATE TABLE "postpaid_resource_service_cost_bill_${year}"
(
    resource_service_bill_id BIGINT         NOT NULL,     -- 资源服务账单id
    billing_time             BIGINT         DEFAULT NULL, -- 出账时间戳
    service_id               BIGINT         NOT NULL,     -- 服务id
    service_code             VARCHAR(32)    DEFAULT NULL, -- 服务编码
    entity_id                BIGINT         NOT NULL,     -- 主体id
    supplier_id              BIGINT         NOT NULL,     -- 供应商id
    account_id               BIGINT         NOT NULL,     -- 账户id
    resource_id              BIGINT         NOT NULL,     -- 资源id
    payment_method           SMALLINT       NOT NULL,     -- 支付方式 0-现金, 1-积分
    usage_count              DECIMAL(18, 2) NOT NULL,     -- 消耗量
    usage_unit               VARCHAR(16)    NULL,         -- 消耗量单位
    charge_usage_count       DECIMAL(18, 2) NOT NULL,     -- 计费消耗量(charge_measure*charge_unit_count)
    charge_unit_count        DECIMAL(18, 2) NOT NULL,     -- 实际计费单位数
    charge_measure           DECIMAL(18, 2) NOT NULL,     -- 计量单位
    charge_measure_unit      VARCHAR(16)    NULL,         -- 计费计量单位
    charge_measure_ceil      SMALLINT       NOT NULL,     -- 计量单位是否向上取整 0-不向上取整, 1-向上取整
    tax_rate                 DECIMAL(18, 6) DEFAULT 0,    -- 税率
    original_price           DECIMAL(18, 6) DEFAULT 0,    -- 目录总价(原单价)
    amount_with_tax          DECIMAL(18, 6) DEFAULT 0,    -- 含税总金额
    amount_without_tax       DECIMAL(18, 6) DEFAULT 0,    -- 不含税金额
    currency                 VARCHAR(3)     DEFAULT NULL, -- 货币单位 CNY USD
    payment_time             BIGINT         DEFAULT NULL, -- 实际支付时间戳
    create_time              BIGINT         NOT NULL,     -- 数据创建时间戳
    deleted                  BOOLEAN,                     -- 是否删除
    billing_start_time       BIGINT         DEFAULT NULL, -- 出账开始时间戳（毫秒）
    billing_end_time         BIGINT         DEFAULT NULL, -- 出账结束时间戳（毫秒）
    billing_cycle_type       SMALLINT       DEFAULT NULL, -- 出账周期类型，0：每月，1：每周，2：每季度，3：每年
    billing_day              INTEGER        DEFAULT NULL, -- 出账第几天
    billing_cycle            VARCHAR(32)    DEFAULT NULL, -- 出账周期标识（如202507-202509）
    timezone                 VARCHAR(32)    DEFAULT NULL, -- 时区
    billing_status           SMALLINT       DEFAULT 0,    -- 出账状态 0-待处理, 1-处理中, 2-已完成, 3-失败
    rate_details             JSON           DEFAULT NULL, -- 费用详情json

    CONSTRAINT pk_postpaid_resource_service_cost_bill PRIMARY KEY (resource_service_bill_id)
);

-- 为后付费资源服务成本账单表创建索引
CREATE INDEX idx_postpaid_resource_service_supplier_id_${year} ON "postpaid_resource_service_cost_bill_${year}" (supplier_id);
CREATE INDEX idx_postpaid_resource_service_resource_id_${year} ON "postpaid_resource_service_cost_bill_${year}" (resource_id);
CREATE INDEX idx_postpaid_resource_service_account_id_${year} ON "postpaid_resource_service_cost_bill_${year}" (account_id);

-- 添加表注释
COMMENT ON TABLE "postpaid_resource_service_cost_bill_${year}" IS '后付费-成本-按资源成本-服务详细-账单表 ${year}';

-- 添加字段注释
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".resource_service_bill_id IS '资源服务账单id';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".billing_time IS '出账时间戳';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".service_id IS '服务id';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".service_code IS '服务编码';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".resource_bill_id IS '资源账单id';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".entity_id IS '主体id';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".supplier_id IS '供应商id';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".account_id IS '账户id';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".resource_id IS '资源id';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".payment_method IS '支付方式 0-现金, 1-积分';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".unit_price IS '单价';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".usage_count IS '消耗量';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".usage_unit IS '消耗量单位';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".tax_rate IS '税率';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".original_price IS '目录总价(原单价)';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".amount_with_tax IS '含税总金额';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".amount_without_tax IS '不含税金额';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".currency IS '货币单位 CNY USD';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".payment_time IS '实际支付时间戳';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".create_time IS '数据创建时间戳';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".deleted IS '是否删除';
COMMENT ON COLUMN "postpaid_resource_service_cost_bill_${year}".tiered_price IS '阶梯费用json {min_usage, max_usage, unit_price, usage_unit, tax_rate, original_price }';

-- PostgreSQL转换说明：
-- 1. 移除了Doris特有的ENGINE、DISTRIBUTED BY、PROPERTIES等语法
-- 2. 将bigint(20)转换为BIGINT，tinyint转换为SMALLINT
-- 3. 将json类型转换为JSON（PostgreSQL推荐）
-- 4. 将INDEX语句转换为独立的CREATE INDEX语句
-- 5. 移除了反引号，使用双引号包围表名（支持变量替换）
-- 6. 调整了注释语法，使用COMMENT ON语句
-- 7. 添加了PRIMARY KEY约束定义
