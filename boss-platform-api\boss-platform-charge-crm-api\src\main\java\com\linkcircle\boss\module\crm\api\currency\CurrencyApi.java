package com.linkcircle.boss.module.crm.api.currency;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.currency.vo.CurrencyRespVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 15:15
 */

@FeignClient(name = ApiConstants.NAME,
        path = ApiConstants.PREFIX + "/charge-currency",
        fallbackFactory = CurrencyApiFallback.class,
        dismiss404 = true)
@Tag(name = "RPC 服务 - 货币信息")
public interface CurrencyApi {

    // 获取货币下拉列表
    @GetMapping("/list")
    @Operation(summary = "获取货币列表")
    public CommonResult<List<CurrencyRespVO>> list();
}
