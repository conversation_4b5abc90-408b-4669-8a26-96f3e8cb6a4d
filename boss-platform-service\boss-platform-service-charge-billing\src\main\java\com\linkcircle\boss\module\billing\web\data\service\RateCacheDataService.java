package com.linkcircle.boss.module.billing.web.data.service;

import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025-06-20 09:09
 * @description 费率缓存数据服务接口
 */
public interface RateCacheDataService {

    /**
     * 检查固定费率是否已计费
     *
     * @param billType       账单类型
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期
     * @return true-已计费，false-未计费
     */
    boolean isFixedRateBilled(BillTypeEnum billType, Long subscriptionId, String billingCycle);

    /**
     * 设置固定费率计费锁定
     *
     * @param billType       账单类型
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期
     * @param ttl            锁定时间
     * @return true-设置成功，false-已存在
     */
    boolean setFixedRateBillingLock(BillTypeEnum billType, Long subscriptionId, Long serviceId, String billingCycle, Duration ttl);

    /**
     * 检查套餐费率是否已计费
     *
     * @param billType       账单类型
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期
     * @return true-已计费，false-未计费
     */
    boolean isPackageRateBilled(BillTypeEnum billType, Long subscriptionId, String billingCycle);

    /**
     * 设置套餐费率计费锁定
     *
     * @param billType       账单类型
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期
     * @param ttl            锁定时间
     * @return true-设置成功，false-已存在
     */
    boolean setPackageRateBillingLock(BillTypeEnum billType, Long subscriptionId, String billingCycle, Duration ttl);

    /**
     * 获取费率累计用量（通用方法）
     *
     * @param billType       账单类型
     * @param rateType       费率类型
     * @param subscriptionId 订阅ID（阶梯、套餐、固定费率使用）
     * @param accountId      账户ID（按量费率使用）
     * @param serviceId      服务ID（按量费率使用）
     * @param billingCycle   计费周期
     * @return 累计用量，不存在返回0
     */
    BigDecimal getRateUsage(BillTypeEnum billType,
                            ChargeRateTypeEnum rateType,
                            Long subscriptionId,
                            Long accountId,
                            Long serviceId,
                            String billingCycle);

    /**
     * 增加费率用量（通用方法）
     *
     * @param billType       账单类型
     * @param rateType       费率类型
     * @param subscriptionId 订阅ID（阶梯、套餐、固定费率使用） / 采购id
     * @param accountId      账户ID（按量费率使用）
     * @param serviceId      服务ID（按量费率使用）
     * @param billingCycle   计费周期
     * @param usage          本次用量
     * @param usageUnit      用量单位
     * @param ttl            缓存时间
     * @return 累计用量
     */
    BigDecimal addRateUsage(BillTypeEnum billType,
                            ChargeRateTypeEnum rateType,
                            Long subscriptionId,
                            Long accountId,
                            Long serviceId,
                            String billingCycle,
                            BigDecimal usage,
                            String usageUnit,
                            Duration ttl,
                            Boolean inTrail);

    /**
     * 删除费率缓存（通用方法）
     *
     * @param billType       账单类型
     * @param rateType       费率类型
     * @param subscriptionId 订阅ID（阶梯、套餐、固定费率使用）
     * @param accountId      账户ID（按量费率使用）
     * @param serviceId      服务ID（按量费率使用）
     * @param billingCycle   计费周期
     */
    void deleteRateCache(BillTypeEnum billType, ChargeRateTypeEnum rateType, Long subscriptionId, Long accountId, Long serviceId, String billingCycle);


}
