package com.linkcircle.boss.module.report.web.bill.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.crm.api.basicConfig.BasicConfigApi;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import com.linkcircle.boss.module.report.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.report.enums.DownLoadEnum;
import com.linkcircle.boss.module.report.web.bill.convert.PostpaidIncomeProductConvert;
import com.linkcircle.boss.module.report.web.bill.mapper.PostpaidIncomeProductBillMapper;
import com.linkcircle.boss.module.report.web.bill.mapper.PostpaidIncomeProductServiceBillMapper;
import com.linkcircle.boss.module.report.web.bill.model.dto.BillQueryByCustomerIdsReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.makeup.BillQueryPageReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.postpaid.PostPaidDetailReqDto;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.postpaid.PostpaidBillExportVO;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.postpaid.PostpaidBillExportVOConvert;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.report.web.bill.model.vo.makeup.MakeupIncomeBillDetailVo;
import com.linkcircle.boss.module.report.web.bill.model.vo.makeup.MakeupIncomeProductBilDetailVo;
import com.linkcircle.boss.module.report.web.bill.model.vo.makeup.MakeupIncomeProductServiceBillDetailVo;
import com.linkcircle.boss.module.report.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.postpaid.PostpaidIncomeServiceVO;
import com.linkcircle.boss.module.report.web.bill.service.BillContentHandler;
import com.linkcircle.boss.module.report.web.bill.service.PostpaidBillService;
import com.linkcircle.boss.module.report.web.download.model.dto.OnlineExportEnhanceDTO;
import com.linkcircle.boss.module.report.web.download.service.DownLoadService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/30 13:57
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PostpaidBillServiceImpl implements PostpaidBillService {


    private final PostpaidIncomeProductBillMapper postpaidIncomeProductBillMapper;

    private final PostpaidIncomeProductServiceBillMapper postpaidIncomeProductServiceBillMapper;

    private final CustomerApi customerApi;
    private final EntityApi entityApi;
    private final DownLoadService downLoadService;
    private  final BasicConfigApi basicConfigApi;
    private final BillContentHandler billContentHandler ;
    /**
     * 查询后付费收入产品账单详情分页
     *
     * @param reqDTO 请求DTO对象，包含查询条件
     * @return 分页结果对象，包含后付费收入产品账单详情列表
     */
    @Override
    public PageResult<PostpaidIncomeProductBillDetailVO> queryBillPage(BillQueryPageReqDTO reqDTO) {
        // 构建分页对象
        Page<?> page = MyBatisUtils.buildPage(reqDTO);
        initPageQueryTime(reqDTO);
        // 执行查询并忽略执行中的异常
        List<PostpaidProductIncomeBillDO> list = TenantUtils.executeIgnore(
                 () -> postpaidIncomeProductBillMapper.queryBillPage(page, reqDTO)
        );
        return processList(list,page,reqDTO.getStartTimeLong(),reqDTO.getEndTimeLong());
    }

    public PageResult<PostpaidIncomeProductBillDetailVO> processList(List<PostpaidProductIncomeBillDO> list, Page<?> page, Long startTimeLong, Long endTimeLong){

        // 创建结果集
        List<PostpaidIncomeProductBillDetailVO> vos = new ArrayList<>();

        // 判断查询结果是否为空
        if (CollectionUtils.isNotEmpty(list)) {
            // 将DO对象转换为VO对象
            vos = list.stream().map(PostpaidIncomeProductConvert.INSTANCE::convert).toList();

        }
        PageResult<PostpaidIncomeProductBillDetailVO> pageResult = MyBatisUtils.convert2PageResult(page, vos);
        if(CollectionUtils.isNotEmpty(vos)){
            // 处理账单列
            handBillColumn(vos);
            // 提取所有账单ID
            List<Long> productBillIds = vos.stream().map(PostpaidIncomeProductBillDetailVO::getProductBillId).toList();

            // 查询服务账单
            List<PostpaidProductServiceIncomeBillDO> serviceBills = TenantUtils.executeIgnore(()->postpaidIncomeProductServiceBillMapper.queryByProductBillIds(productBillIds, startTimeLong,
                    endTimeLong));

            // 判断服务账单是否为空
            if (CollectionUtils.isNotEmpty(serviceBills)) {
                // 将服务账单按账单ID分组
                Map<String, List<PostpaidProductServiceIncomeBillDO>> serviceBillMap = serviceBills.stream().collect(Collectors.groupingBy(x -> x.getBillId().toString()));

                // 遍历结果集，设置服务账单信息
                for (PostpaidIncomeProductBillDetailVO vo : vos) {
                    // 获取当前账单ID对应的服务账单列表
                    List<PostpaidProductServiceIncomeBillDO> serviceBillList = serviceBillMap.get(vo.getProductBillId().toString());
                    // 判断服务账单列表是否为空
                    if (CollectionUtils.isNotEmpty(serviceBillList)) {
                        // 将服务账单DO对象转换为VO对象
                        vo.setServices(serviceBillList.stream().map(PostpaidIncomeProductConvert.INSTANCE::convert).toList());

                    }
                    vo.setProductNames(List.of(vo.getProductName()==null?"":vo.getProductName()));
                }
                // 处理服务账单列
                handleBillServiceColumn(vos);
                vos.forEach(vo->{
                    vo.setServiceNames(vo.getServices().stream().map(PostpaidIncomeServiceVO::getServiceName).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
                });
            }
        }

        // 将分页对象转换为分页结果对象
        return pageResult;
    }

    @Override
    public PageResult<PostpaidIncomeProductBillDetailVO> queryBillPageByCustomerIds(BillQueryByCustomerIdsReqDTO pageReq) {
        // 构建分页对象
        Page<?> page = MyBatisUtils.buildPage(pageReq);
        List<Long> customerIds = processIds(pageReq.getCustomerIds());
        List<Long> accountIds = processIds(pageReq.getAccountIds());
        // 执行查询并忽略执行中的异常
        List<PostpaidProductIncomeBillDO> list = TenantUtils.executeIgnore(
                () -> postpaidIncomeProductBillMapper.queryBillPageByCustomerIds(page, customerIds,accountIds)
        );
        return processList(list,page,0L,System.currentTimeMillis());
    }


    /**
     * 查询账单详情
     *
     * @param detailDto 请求参数对象，包含账单详情ID和计费时间
     * @return 返回账单详情展示对象
     */
    @Override
    public PostpaidIncomeProductBillDetailVO queryBill(PostPaidDetailReqDto detailDto) {
        // 查询账单信息
        PostpaidProductIncomeBillDO bill = TenantUtils.executeIgnore(
                () -> postpaidIncomeProductBillMapper.queryByIdAndTime(detailDto.getProductBillId(),detailDto.getBillingTime())
        );
        if (bill == null) {
            log.error("查询账单信息为空，productBillId:{},billingTime:{}", detailDto.getProductBillId(),detailDto.getBillingTime());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        // 将账单信息转换为展示对象
        PostpaidIncomeProductBillDetailVO showVO = PostpaidIncomeProductConvert.INSTANCE.convertDetail(bill);

        handleDiscount(showVO, PostpaidIncomeProductBillDetailVO::getBillDiscountDetails,PostpaidIncomeProductBillDetailVO::setBillCoupons);
        handleDiscount(showVO, PostpaidIncomeProductBillDetailVO::getDiscountDetails,PostpaidIncomeProductBillDetailVO::setProductCoupons);
        // 查询服务账单信息
        List<PostpaidProductServiceIncomeBillDO> serviceIncomeBillDOS = TenantUtils.executeIgnore(()-> postpaidIncomeProductServiceBillMapper.queryByProductBillIds(List.of(bill.getProductBillId()),
                detailDto.getBillingTime(), detailDto.getBillingTime()));
        // 判断服务账单信息是否非空
        if (CollectionUtils.isNotEmpty(serviceIncomeBillDOS)) {
            // 将服务账单信息转换为展示对象列表
            List<PostpaidIncomeServiceVO> services = serviceIncomeBillDOS.stream().map(PostpaidIncomeProductConvert.INSTANCE::convert).toList();
            // 处理账单列
            handBillColumn(showVO);
            // 设置服务列表到展示对象
            showVO.setServices(services);
            // 处理账单服务列
            handleBillServiceColumn(showVO);
            handleDiscount(services, PostpaidIncomeServiceVO::getDiscountDetails,PostpaidIncomeServiceVO::setCoupons);
            handleRateDetail(services, PostpaidIncomeServiceVO::getRateDetails, PostpaidIncomeServiceVO::getBillingType,
                     PostpaidIncomeServiceVO::getTaxRate
                    , PostpaidIncomeServiceVO::setFixedRateConfig,PostpaidIncomeServiceVO::setPackageRateConfig,
                    PostpaidIncomeServiceVO::setTierRateConfig, PostpaidIncomeServiceVO::setUsageBasedRateConfig);
        }
        // 查询客户信息
        CommonResult<ChargeCustomerInfoVO> commonResult = customerApi.findById(bill.getCustomerId());
        // 判断客户查询是否成功
        if (commonResult.isSuccess()) {
            // 获取客户信息并设置到展示对象
            ChargeCustomerInfoVO customer = commonResult.getData();
            showVO.setCustomer(customer);
        }
        // 查询实体信息
        CommonResult<EntityDetailsVO> entityApiById = entityApi.findById(bill.getCustomerId());
        // 判断实体查询是否成功
        if (entityApiById.isSuccess()) {
            // 获取实体信息并设置到展示对象
            EntityDetailsVO entity = entityApiById.getData();
            showVO.setEntity(entity);
        }
        processBillConfigDetail(showVO,PostpaidIncomeProductBillDetailVO::getEntityId, PostpaidIncomeProductBillDetailVO::setBillConfigDetails);
        processBillContentAndCoupons(showVO);
        return showVO;
    }




    private void processBillContentAndCoupons(PostpaidIncomeProductBillDetailVO showVO) {
        List<BillDiscountConfigDTO> billCoupons = showVO.getBillCoupons();
        List<BillDiscountConfigDTO> productCoupons = showVO.getProductCoupons();
        List<BillCoupon> coupons = new ArrayList<>(billContentHandler.handleCoupons(billCoupons, null, null, showVO.getCurrency()));
        coupons.addAll(billContentHandler.handleCoupons(productCoupons, showVO.getProductId(), null, showVO.getCurrency()));
        BillContent productContent = new BillContent();
        productContent.setDescription(StringUtils.isNotEmpty(showVO.getProductName())?showVO.getProductName():"");
        for (PostpaidIncomeServiceVO service : showVO.getServices()) {
                coupons.addAll(billContentHandler.handleCoupons(service.getCoupons(),Long.valueOf(service.getProductId()),service.getServiceId(),showVO.getCurrency()));
                billContentHandler.handleContent(productContent,service);
        }
        showVO.setShowCoupons(coupons);
        showVO.setShowContents(List.of(productContent));
    }

    public<T> void processBillConfigDetail(T showVO, Function<T,Long> entityIdMapper, BiConsumer<T, InvoiceDetailsVO> handler){
        CommonResult<InvoiceDetailsVO> invoiceDetailsVOCommonResult = basicConfigApi.queryInvoice(entityIdMapper.apply(showVO), 1);
        if(invoiceDetailsVOCommonResult.isSuccess()){
            InvoiceDetailsVO invoiceDetailsVO = invoiceDetailsVOCommonResult.getData();
            if(invoiceDetailsVO!=null){
                handler.accept(showVO,invoiceDetailsVO);
            }
        }
    }

    @Override
    public void exportBill(BillQueryPageReqDTO reqDTO, HttpServletResponse response) {
        try {
            // ③ 将查询结果转换为导出所需的VO列表
            OnlineExportEnhanceDTO<PostpaidIncomeProductBillDetailVO, PostpaidBillExportVO> online = new OnlineExportEnhanceDTO<>();
            online.pageSize(DownLoadEnum.PAGE_SIZE_1000)
                    .fileName("后付费收入账单导出")
                    .sheetName("账单列表")
                    .exportClass(PostpaidBillExportVO.class)
                    .response(response)
                    .pageParam(reqDTO)
                    .queryPageFunc(p -> queryBillPage((BillQueryPageReqDTO) p))
                    .convertFun(PostpaidBillExportVOConvert.INSTANCE::convert)
                    .handle(ts -> {});
            downLoadService.exportOnline(online);
        } catch (Exception e) {
            log.error("导出供应商信息异常", e);
            // ⑤ 如果发生IO异常，则抛出ServiceException异常
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_BILL_EXPORT_ERROR);
        }
    }


}
