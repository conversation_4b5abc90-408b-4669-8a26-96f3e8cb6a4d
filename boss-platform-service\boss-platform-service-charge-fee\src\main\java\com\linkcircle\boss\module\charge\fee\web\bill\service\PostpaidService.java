package com.linkcircle.boss.module.charge.fee.web.bill.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.ProductServiceTieredPriceDTO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.api.wallet.model.dto.WalletDeductionMqDTO;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.enums.BillEnum;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.makeup.create.MakeupServiceBillCreateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.makeup.update.MakeupServiceBillUpdateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.makeup.update.TimeWrapDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.makeup.update.TimeWrapUtils;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.postpaid.PostpaidProductServiceUpdateDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.postpaid.PostpaidProductUpdateDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid.PostpaidIncomeServiceVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/30 9:14
 * @description 计费服务-后付费服务
 */
public interface PostpaidService extends BillService<PostpaidIncomeProductBillDetailVO> {

    default Object buildPayOffDto(PostpaidProductIncomeBillDO postpaidProductBill) {
        WalletDeductionMqDTO dto = new WalletDeductionMqDTO();
        dto.setBillId(postpaidProductBill.getProductBillId().toString());
        dto.setServiceCode("");
        dto.setCustomerId(postpaidProductBill.getCustomerId());
        dto.setAccountId(postpaidProductBill.getAccountId());
        dto.setWalletsId(postpaidProductBill.getWalletId());
        dto.setPaymentType(1); // 后付费
        dto.setPaymentMethod(postpaidProductBill.getPaymentMethod()); // 现金
        dto.setOriginalPrice(postpaidProductBill.getOriginalPrice());
        dto.setDiscountedPrice(postpaidProductBill.getDiscountedPrice());
        dto.setDiscountAmount(postpaidProductBill.getDiscountAmount());
        dto.setTaxRate(postpaidProductBill.getTaxRate()); // 10%
        dto.setTaxAmount(postpaidProductBill.getAmountWithTax().subtract(postpaidProductBill.getAmountWithoutTax()));
        dto.setAmountWithTax(postpaidProductBill.getAmountWithTax());
        dto.setAmountWithoutTax(postpaidProductBill.getUnpaidAmount());
        dto.setCurrency(postpaidProductBill.getCurrency());
        dto.setCallbackUrl("");
        return dto;
    }


    default void handleBillServiceColumn(List<PostpaidIncomeProductBillDetailVO> vos) {
        if (!CollectionUtils.isEmpty(vos)) {
            List<PostpaidIncomeServiceVO> serviceVOS = vos.stream().map(PostpaidIncomeProductBillDetailVO::getServices).filter(x -> !CollectionUtils.isEmpty(x)).flatMap(Collection::stream).toList();
            if (!CollectionUtils.isEmpty(serviceVOS)) {
                FunctionService.of(BillEnum.BillColumn.SERVICE).handleLong(serviceVOS, PostpaidIncomeServiceVO::getServiceId, PostpaidIncomeServiceVO::setServiceName);
                Map<String, PostpaidIncomeProductBillDetailVO> voMap = vos.stream().collect(Collectors.toMap(t -> t.getProductBillId().toString(), t -> t, (d1, d2) -> d2));
                for (PostpaidIncomeServiceVO serviceVO : serviceVOS) {
                    PostpaidIncomeProductBillDetailVO productBill = voMap.get(serviceVO.getBillId().toString());
                    if (productBill != null) {
                        serviceVO.setAccountName(productBill.getAccountName());
                        serviceVO.setCustomerName(productBill.getCustomerName());
                        serviceVO.setEntityName(productBill.getEntityName());
                        serviceVO.setPlanName(productBill.getPlanName());
                        serviceVO.setWalletName(productBill.getWalletName());
                        serviceVO.setProductName(productBill.getProductName());
                        serviceVO.setContractName(productBill.getCustomerName());
                    }
                }
            }
        }
    }

    /**
     * 处理账单明细中的列信息
     *
     * @param vos 账单明细的列表
     */
    default void handBillColumn(List<PostpaidIncomeProductBillDetailVO> vos) {
        // 处理账户列信息
        FunctionService.of(BillEnum.BillColumn.ACCOUNT).handleLong(vos, PostpaidIncomeProductBillDetailVO::getAccountId, PostpaidIncomeProductBillDetailVO::setAccountName);
        // 处理服务列信息
        //FunctionService.of(FinancialEnum.BillColumn.SERVICE).handle(vos, PostpaidIncomeProductBillDetailVO::getServiceId, PostpaidIncomeProductBillDetailVO::setServiceName);
        // 处理客户列信息
        FunctionService.of(BillEnum.BillColumn.CUSTOMER).handleLong(vos, PostpaidIncomeProductBillDetailVO::getCustomerId, PostpaidIncomeProductBillDetailVO::setCustomerName);
        // 处理产品列信息
        FunctionService.of(BillEnum.BillColumn.PRODUCT).handleLong(vos, PostpaidIncomeProductBillDetailVO::getProductId, PostpaidIncomeProductBillDetailVO::setProductName);
        // 处理实体列信息
        FunctionService.of(BillEnum.BillColumn.ENTITY).handleLong(vos, PostpaidIncomeProductBillDetailVO::getEntityId, PostpaidIncomeProductBillDetailVO::setEntityName);
        // 处理合同列信息
        FunctionService.of(BillEnum.BillColumn.CONTRACT).handleLong(vos, PostpaidIncomeProductBillDetailVO::getContractId, PostpaidIncomeProductBillDetailVO::setContractName);
        // 处理钱包列信息
        FunctionService.of(BillEnum.BillColumn.WALLET).handleLong(vos, PostpaidIncomeProductBillDetailVO::getWalletId, PostpaidIncomeProductBillDetailVO::setWalletName);
        // 处理计划列信息
        FunctionService.of(BillEnum.BillColumn.PLAN).handleLong(vos, PostpaidIncomeProductBillDetailVO::getPlanId, PostpaidIncomeProductBillDetailVO::setPlanName);
        // 处理折扣列信息
         FunctionService.of(BillEnum.BillColumn.DISCOUNT).handleLong(vos, PostpaidIncomeProductBillDetailVO::getDiscountId, PostpaidIncomeProductBillDetailVO::setWalletName);
        // 处理订阅列信息
        // FunctionService.of(BillEnum.BillColumn.SUBSCRIPTION).handleLong(vos, PostpaidIncomeProductBillDetailVO::getSubscribeId, PostpaidIncomeProductBillDetailVO::setSubscribeName);
    }

    default InvoiceEnum.BillType getBillType() {
        return InvoiceEnum.BillType.POSTPAID;
    }




    public PostpaidIncomeProductBillDetailVO queryBill(BillReqDto detailDto, boolean queryCustomerInfo);



}
