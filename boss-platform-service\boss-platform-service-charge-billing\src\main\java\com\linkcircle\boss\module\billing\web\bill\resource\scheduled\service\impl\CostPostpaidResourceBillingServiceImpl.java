package com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.module.billing.api.bill.resource.model.entity.PostpaidResourceServiceCostBillDO;
import com.linkcircle.boss.module.billing.enums.BillingProcessStatusEnum;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.model.dto.CostPostpaidResourceBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.CostPostpaidResourceBillingService;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.CostPostpaidResourceServiceBillService;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.data.service.ResourcePurchaseDataService;
import com.linkcircle.boss.module.billing.web.data.service.SupplierAccountDataService;
import com.linkcircle.boss.module.billing.web.manager.BillIdManager;
import com.linkcircle.boss.module.crm.api.supplier.account.vo.SupplierAccountVO;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-17
 * @description 后付费资源服务出账服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostPostpaidResourceBillingServiceImpl implements CostPostpaidResourceBillingService {

    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final ResourcePurchaseDataService resourcePurchaseDataService;
    private final SupplierAccountDataService supplierAccountDataService;
    private final CostPostpaidResourceServiceBillService resourceServiceBillService;
    private final BillIdManager billIdManager;
    private final RedissonUtil redissonUtil;
    private final RocketMQTemplate rocketMQTemplate;

    @Override
    public void processSingleAccount(Long accountId, PaymentTypeEnum paymentType) {
        log.info("开始处理单个资源采购账户出账, accountId: {}, paymentType: {}",
                accountId, paymentType);

        try {
            // 1. 查询账户信息，获取该账户的出账周期配置
            Optional<SupplierAccountVO> accountInfoOpt = supplierAccountDataService.getSupplierAccountInfo(accountId);
            if (accountInfoOpt.isEmpty()) {
                log.warn("未找到资源采购账户信息，跳过出账, accountId: {}", accountId);
                return;
            }

            SupplierAccountVO accountInfo = accountInfoOpt.get();

            // 2. 查询账户的资源采购详情列表
            List<ResourcePurchaseVO> purchases = resourcePurchaseDataService.getAccountResourcePurchases(accountId);

            if (CollUtil.isEmpty(purchases)) {
                log.info("账户无有效资源采购，跳过出账, accountId: {}", accountId);
                return;
            }


            if (CollUtil.isEmpty(purchases)) {
                log.info("账户无符合条件的资源采购，跳过出账, accountId: {}, paymentType: {}", accountId, paymentType);
                return;
            }

            log.info("账户符合条件的资源采购数量: {}, accountId: {}, paymentType: {}", purchases.size(), accountId, paymentType);

            // 2. 遍历资源采购，处理每个资源采购的资源和服务
            for (ResourcePurchaseVO purchase : purchases) {
                try {
                    // 先判断资源采购是否生效
                    if (!isResourcePurchaseActive(purchase)) {
                        log.info("资源采购未生效，跳过出账, purchaseId: {}, accountId: {}", purchase.getId(), accountId);
                        continue;
                    }

                    processResourcePurchaseBilling(purchase, accountInfo);
                } catch (Exception e) {
                    log.error("处理资源采购出账异常, purchaseId: {}, accountId: {}", purchase.getId(), accountId, e);
                }
            }

            log.info("账户资源采购出账处理完成, accountId: {}, 处理资源采购数: {}", accountId, purchases.size());
        } catch (Exception e) {
            log.error("处理单个资源采购账户出账异常, accountId: {}, paymentType: {}", accountId, paymentType, e);
        }
    }

    /**
     * 处理资源采购出账
     *
     * @param purchase    资源采购信息
     * @param accountInfo 账户信息
     */
    public void processResourcePurchaseBilling(ResourcePurchaseVO purchase, SupplierAccountVO accountInfo) {
        Long purchaseId = purchase.getId();
        Long accountId = purchase.getAccountId();

        try {
            log.info("资源采购出账信息: {}", JsonUtils.toJsonString(purchase));

            // 1. 检查资源采购是否有详情数据
            if (CollUtil.isEmpty(purchase.getDetails())) {
                log.warn("资源采购无详情数据，跳过出账, purchaseId: {}", purchaseId);
                return;
            }

            // 2. 遍历资源采购详情，每个详情就是一个服务
            for (ResourcePurchaseVO.Detail detail : purchase.getDetails()) {
                // 每个detail就是一个资源服务，直接处理服务出账
                processServiceBilling(purchase, detail, accountInfo);
            }

            log.info("资源采购出账处理完成, purchaseId: {}", purchaseId);
        } catch (Exception e) {
            log.error("处理资源采购出账异常, purchaseId: {}, accountId: {}", purchaseId, accountId, e);
        }
    }

    /**
     * 处理服务出账
     *
     * @param purchase    资源采购信息
     * @param detail      资源服务详情信息
     * @param accountInfo 账户信息
     */
    @Override
    public void processServiceBilling(ResourcePurchaseVO purchase,
                                      ResourcePurchaseVO.Detail detail,
                                      SupplierAccountVO accountInfo) {
        Long purchaseId = purchase.getId();
        Long accountId = purchase.getAccountId();
        Long resourceServiceId = detail.getResourceServiceId();
        String resourceServiceCode = detail.getResourceServiceCode();
        Integer period = detail.getPeriod();
        Integer unitPeriod = detail.getUnitPeriod();
        Long startTime = purchase.getStartTime();
        log.info("开始处理资源服务出账, purchaseId: {}, accountId: {}, resourceServiceId: {}, resourceServiceCode: {}",
                purchaseId, accountId, resourceServiceId, resourceServiceCode);

        CyclePeriodResultVO cyclePeriodResultVO = cyclePeriodCalculateService.calculateCyclePeriod(unitPeriod, "UTC",
                System.currentTimeMillis(), startTime, period, true);
        cyclePeriodResultVO.setCurrency(accountInfo.getCurrency());
        if (!cyclePeriodResultVO.isSuccess()) {
            log.warn("周期开始时间计算失败，跳过创建, purchaseId: {}, accountId: {}, resourceServiceId: {}, resourceServiceCode: {}, errorMessage: {}",
                    purchaseId, accountId, resourceServiceId, resourceServiceCode, cyclePeriodResultVO.getErrorMessage());
            return;
        }
        try {
            // 1. 检查是否已经存在服务账单（先检查Redis缓存，再查数据库）
            if (checkResourceServiceBillExists(resourceServiceCode, purchaseId,
                    cyclePeriodResultVO.getCycleStartTime(), cyclePeriodResultVO.getCycleEndTime())) {
                log.info("资源服务账单已存在（缓存或数据库），跳过创建, accountId: {}, resourceServiceCode: {}, purchaseId: {}, billingCycle: {}",
                        accountId, resourceServiceCode, cyclePeriodResultVO.getBillingCycle(), purchaseId);
                return;
            }

            // 2. 创建服务账单
            PostpaidResourceServiceCostBillDO resourceServiceCostBillDO = createServiceBill(purchase, detail, cyclePeriodResultVO);

            // 3. 使用RocketMQ消息确保数据一致性
            boolean success = sendBillMessage(resourceServiceCostBillDO,
                    purchase, detail, cyclePeriodResultVO);
            log.info("消息发送完成: {}, billId: {}, accountId: {}, resourceServiceId: {}",
                    success, resourceServiceCostBillDO.getResourceServiceBillId(), accountId, resourceServiceId);
        } catch (Exception e) {
            log.error("处理资源服务出账异常, purchaseId: {}, resourceServiceId: {}", purchaseId, resourceServiceId, e);
            throw e;
        }
    }

    /**
     * 检查资源服务账单是否已存在（先检查Redis缓存，再查数据库）
     *
     * @param serviceCode    服务编码
     * @param purchaseId     采购ID
     * @param cycleStartTime 周期开始时间
     * @param cycleEndTime   周期结束时间
     * @return 是否已存在
     */
    protected boolean checkResourceServiceBillExists(String serviceCode, Long purchaseId, Long cycleStartTime, Long cycleEndTime) {
        // 1. 先检查Redis缓存
        String cacheKey = ChargeCacheUtils.getResourceServiceBillCacheKey(serviceCode, purchaseId, cycleStartTime, cycleEndTime);
        Duration duration = Duration.ofMillis(cycleEndTime - cycleStartTime);
        boolean setNx = redissonUtil.setNx(cacheKey, System.currentTimeMillis(), duration);
        if (!setNx) {
            log.info("Redis缓存中资源服务账单标记已存在, serviceCode: {}, purchaseId: {}, cycleStartTime: {}, cycleEndTime: {}",
                    serviceCode, purchaseId, cycleStartTime, cycleEndTime);
            return true;
        }

        // 2. 再查数据库
        boolean billExists = resourceServiceBillService.isBillExists(serviceCode, purchaseId, cycleStartTime, cycleEndTime);

        // 3. 如果数据库中存在，则设置Redis缓存标记，防止重复查询数据库
        if (billExists) {
            boolean setResult = redissonUtil.setNx(cacheKey, System.currentTimeMillis(), duration);
            log.info("数据库中资源服务账单已存在，设置Redis缓存标记: {}, serviceCode: {}, purchaseId: {}, cycleStartTime: {}, cycleEndTime: {}",
                    setResult, serviceCode, purchaseId, cycleStartTime, cycleEndTime);
        }

        return billExists;
    }

    public boolean sendBillMessage(PostpaidResourceServiceCostBillDO resourceServiceCostBillDO,
                                   ResourcePurchaseVO purchase,
                                   ResourcePurchaseVO.Detail detail,
                                   CyclePeriodResultVO cyclePeriodResultVO) {
        try {
            CostPostpaidResourceBillingMessageDTO messageDTO = new CostPostpaidResourceBillingMessageDTO();
            messageDTO.setResourceServiceCostBillDO(resourceServiceCostBillDO);
            messageDTO.setPurchase(purchase);
            messageDTO.setDetail(detail);
            messageDTO.setCyclePeriodResultVO(cyclePeriodResultVO);

            Message<CostPostpaidResourceBillingMessageDTO> msg = MessageBuilder
                    .withPayload(messageDTO)
                    .build();

            String destination = ChargeTopicUtils.getCostServiceBillingMessageNormalTopic();

            log.info("发送资源服务出账消息, destination: {}, billId: {}", destination, resourceServiceCostBillDO.getResourceServiceBillId());
            SendResult sendResult = rocketMQTemplate.syncSend(destination, msg);
            log.info("资源服务出账消息发送成功, billId: {}, sendResult: {}", resourceServiceCostBillDO.getResourceServiceBillId(), sendResult);
            return true;
        } catch (Exception e) {
            log.error("发送资源服务出账消息失败, billId: {}", resourceServiceCostBillDO.getResourceServiceBillId(), e);
            return false;
        }
    }

    /**
     * 创建服务账单
     *
     * @param purchase            资源采购信息
     * @param detail              资源服务详情信息
     * @param cyclePeriodResultVO 出账周期
     * @return 服务账单
     */
    private PostpaidResourceServiceCostBillDO createServiceBill(ResourcePurchaseVO purchase,
                                                                ResourcePurchaseVO.Detail detail,
                                                                CyclePeriodResultVO cyclePeriodResultVO) {
        long currentTime = System.currentTimeMillis();
        return PostpaidResourceServiceCostBillDO.builder()
                .resourceServiceBillId(billIdManager.createBillIdLong(BillTypeEnum.COST_SERVICE, cyclePeriodResultVO.getCycleEndTime()))
                .billingTime(currentTime)
                .serviceId(detail.getResourceServiceId())
                .serviceCode(detail.getResourceServiceCode())
                .identityId(purchase.getEntityId())
                .supplierId(purchase.getSuppliersId())
                .accountId(purchase.getAccountId())
                .resourceId(purchase.getResourceId())
                .paymentMethod(detail.getPaymentOptions())
                .usageCount(BigDecimal.ZERO)
                .chargeUsageCount(BigDecimal.ZERO)
                .chargeUnitCount(BigDecimal.ZERO)
                .chargeMeasure(BigDecimal.ONE)
                .chargeMeasureCeil(0)
                .taxRate(BigDecimal.valueOf(purchase.getRate()))
                .originalPrice(BigDecimal.ZERO)
                .amountWithTax(BigDecimal.ZERO)
                .amountWithoutTax(BigDecimal.ZERO)
                .currency(cyclePeriodResultVO.getCurrency())
                .createTime(currentTime)
                .deleted(false)
                .billingStartTime(cyclePeriodResultVO.getCycleStartTime())
                .billingEndTime(cyclePeriodResultVO.getCycleEndTime())
                .billingCycleType(cyclePeriodResultVO.getPeriodUnitEnum().getUnit())
                .billingCycle(cyclePeriodResultVO.getBillingCycle())
                .timezone(cyclePeriodResultVO.getTimezone())
                .billingStatus(BillingProcessStatusEnum.WAITING.getStatus())
                .build();
    }

    /**
     * 判断资源采购是否生效 状态
     *
     * @param purchase 资源采购信息
     * @return 是否生效
     */
    private boolean isResourcePurchaseActive(ResourcePurchaseVO purchase) {
        return purchase.hasValidPurchase();
    }
}