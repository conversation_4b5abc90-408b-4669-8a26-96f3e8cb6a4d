package com.linkcircle.boss.module.charge.crm.web.resource.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.common.util.object.BeanUtils;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.module.charge.crm.enums.ChargeResourceServiceStatusEnum;
import com.linkcircle.boss.module.charge.crm.web.resource.mapper.ChargeResourceInfoMapper;
import com.linkcircle.boss.module.charge.crm.web.resource.mapper.ChargeResourceServiceConfigMapper;
import com.linkcircle.boss.module.charge.crm.web.resource.model.ChargeResource;
import com.linkcircle.boss.module.charge.crm.web.resource.model.ChargeResourceServiceConfig;
import com.linkcircle.boss.module.charge.crm.web.resource.model.dto.ChargeResourceAddDTO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.dto.ChargeResourceQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.vo.ChargeResourceDetailVO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.vo.ChargeResourceVO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.vo.ChargeSupplierDropDownVO;
import com.linkcircle.boss.module.charge.crm.web.resource.service.IChargeResourceService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.mapper.ChargeResourceServiceMapper;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants.RESOURCE_NAME_REPEATED_EXCEPTION;

/**
 * <p>
 * 资源信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Service
@RequiredArgsConstructor
public class ChargeResourceServiceImpl extends ServiceImpl<ChargeResourceInfoMapper, ChargeResource> implements IChargeResourceService {

    private final ChargeResourceInfoMapper chargeResourceInfoMapper;

    private final ChargeResourceServiceConfigMapper chargeResourceServiceConfigMapper;

    private final ChargeResourceServiceMapper chargeResourceServiceMapper;

    @Override
    public PageResult<ChargeResourceVO> pageQuery(ChargeResourceQueryDTO queryDTO) {
        Page<?> page = MyBatisUtils.buildPage(queryDTO);
        List<ChargeResourceVO> list = chargeResourceInfoMapper.pageQuery(page, queryDTO);
        return MyBatisUtils.convert2PageResult(page, list);
    }

    @Override
    public List<ChargeSupplierDropDownVO> supplierDropDownList() {
        return chargeResourceInfoMapper.supplierDropDownList();
    }

    @Override
    public long add(ChargeResourceAddDTO chargeResourceAddDTO) {
        //验证名称
        if (checkResourceName(chargeResourceAddDTO.getResourceName(), chargeResourceAddDTO.getId())) {
            throw new ServiceException(RESOURCE_NAME_REPEATED_EXCEPTION);
        }
        ChargeResource chargeResource = new ChargeResource();
        BeanUtils.copyProperties(chargeResourceAddDTO, chargeResource);
        chargeResource.setStatus(ChargeResourceServiceStatusEnum.unActivation.getCode());
        save(chargeResource);

        List<ChargeResourceServiceConfig> chargeResourceServiceConfigs = getChargeResourceServiceConfigs(chargeResourceAddDTO, chargeResource);
        chargeResourceServiceConfigMapper.insert(chargeResourceServiceConfigs);
        return chargeResource.getId();
    }

    private boolean checkResourceName(String resourceName, Long id) {
        LambdaQueryWrapper<ChargeResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeResource::getResourceName, resourceName);
        if (ObjectUtil.isNotNull(id)) {
            queryWrapper.ne(ChargeResource::getId, id);
        }
        return chargeResourceInfoMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public CommonResult<?> edit(ChargeResourceAddDTO chargeResourceAddDTO) {
        //验证名称
        if (checkResourceName(chargeResourceAddDTO.getResourceName(), chargeResourceAddDTO.getId())) {
            throw new ServiceException(RESOURCE_NAME_REPEATED_EXCEPTION);
        }
        ChargeResource chargeResource = new ChargeResource();
        BeanUtils.copyProperties(chargeResourceAddDTO, chargeResource);
        updateById(chargeResource);
        //config
        LambdaQueryWrapper<ChargeResourceServiceConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeResourceServiceConfig::getResourceId, chargeResource.getId());
        chargeResourceServiceConfigMapper.delete(queryWrapper);
        List<ChargeResourceServiceConfig> chargeResourceServiceConfigs = getChargeResourceServiceConfigs(chargeResourceAddDTO, chargeResource);
        chargeResourceServiceConfigMapper.insert(chargeResourceServiceConfigs);
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> detail(Long id) {
        ChargeResource chargeResource = chargeResourceInfoMapper.selectById(id);
        ChargeResourceDetailVO chargeResourceDetailVO = new ChargeResourceDetailVO();
        BeanUtils.copyProperties(chargeResource, chargeResourceDetailVO);
        LambdaQueryWrapper<ChargeResourceServiceConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeResourceServiceConfig::getResourceId, id);
        List<Long> ids = chargeResourceServiceConfigMapper.selectList(queryWrapper).stream().map(ChargeResourceServiceConfig::getServicePriceId).collect(Collectors.toList());

        List<ChargeResourceServiceVersionInfoVO> chargeResourceServiceVersionInfoVOList = chargeResourceServiceMapper.getVersionInfoById(null, ids);
        chargeResourceDetailVO.setChargeResourceServiceVersionInfoVOList(chargeResourceServiceVersionInfoVOList);
        return CommonResult.success(chargeResourceDetailVO);
    }

    @Override
    public CommonResult<?> activationBatch(List<Long> resourceIds) {
        LambdaUpdateWrapper<ChargeResource> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ChargeResource::getId, resourceIds);
        updateWrapper.set(ChargeResource::getStatus, ChargeResourceServiceStatusEnum.activation.getCode());
        return CommonResult.success(update(updateWrapper));
    }

    @Override
    public CommonResult<?> deactivateBatch(List<Long> resourceIds) {
        LambdaUpdateWrapper<ChargeResource> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ChargeResource::getId, resourceIds);
        updateWrapper.set(ChargeResource::getStatus, ChargeResourceServiceStatusEnum.unActivation.getCode());
        return CommonResult.success(update(updateWrapper));
    }

    @Override
    public CommonResult<?> archive(List<Long> resourceIds) {
        //todo 采购需提示
        LambdaUpdateWrapper<ChargeResource> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ChargeResource::getId, resourceIds);
        updateWrapper.set(ChargeResource::getStatus, ChargeResourceServiceStatusEnum.archive.getCode());
        return CommonResult.success(update(updateWrapper));
    }

    @Override
    public CommonResult<?> cancelArchiveBatch(List<Long> resourceIds) {
        LambdaUpdateWrapper<ChargeResource> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ChargeResource::getId, resourceIds);
        updateWrapper.set(ChargeResource::getStatus, ChargeResourceServiceStatusEnum.unActivation.getCode());
        return CommonResult.success(update(updateWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> deleteBatch(List<Long> resourceIds) {
        removeBatchByIds(resourceIds);
        LambdaQueryWrapper<ChargeResourceServiceConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChargeResourceServiceConfig::getResourceId, resourceIds);
        chargeResourceServiceConfigMapper.delete(queryWrapper);
        return CommonResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> copy(Long resourceId) {
        ChargeResource chargeResource = chargeResourceInfoMapper.selectById(resourceId);
        chargeResource.setId(null);
        String suffix = "_copy";
        String resourceName = chargeResource.getResourceName();
        Integer size = getSizeByServiceName(resourceName);
        if (size > 0) {
            resourceName = resourceName + suffix + size;
        } else {
            resourceName = resourceName + suffix + 1;
        }
        chargeResource.setResourceName(resourceName);
        save(chargeResource);
        copyServiceConfig(resourceId, chargeResource.getId());
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<CommonVO>> findNameByIds(CommonDTO commonDTO) {
        List<ChargeResource> chargeResources = chargeResourceInfoMapper.queryNameByIds(commonDTO);
        if(CollectionUtil.isNotEmpty(chargeResources)) {
            return CommonResult.success(chargeResources.stream().map(resource ->CommonVO.builder().id(resource.getId()).name(resource.getResourceName()).build()).toList());
        }
        return CommonResult.success(List.of());
    }

    @NotNull
    private static List<ChargeResourceServiceConfig> getChargeResourceServiceConfigs(ChargeResourceAddDTO chargeResourceAddDTO, ChargeResource chargeResource) {
        List<ChargeResourceServiceConfig> chargeResourceServiceConfigs = new ArrayList<>();
        List<Long> serviceIds = chargeResourceAddDTO.getVersionIds();
        serviceIds.forEach(versionId -> {
            ChargeResourceServiceConfig chargeResourceServiceConfig = new ChargeResourceServiceConfig();
            chargeResourceServiceConfig.setResourceId(chargeResource.getId());
            chargeResourceServiceConfig.setServicePriceId(versionId);
            chargeResourceServiceConfigs.add(chargeResourceServiceConfig);
        });
        return chargeResourceServiceConfigs;
    }

    private Integer getSizeByServiceName(String resourceName) {
        LambdaQueryWrapper<ChargeResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ChargeResource::getResourceName, resourceName);
        List<ChargeResource> chargeResourceServiceList = chargeResourceInfoMapper.selectList(queryWrapper);
        return chargeResourceServiceList.size();
    }

    @Transactional(rollbackFor = Exception.class)
    protected void copyServiceConfig(Long resourceId, Long newResourceId) {
        LambdaQueryWrapper<ChargeResourceServiceConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeResourceServiceConfig::getResourceId, resourceId);
        List<ChargeResourceServiceConfig> chargeResourceServiceConfigs = chargeResourceServiceConfigMapper.selectList(queryWrapper);
        chargeResourceServiceConfigs.forEach(chargeResourceServiceConfig -> {
            chargeResourceServiceConfig.setId(null);
            chargeResourceServiceConfig.setResourceId(newResourceId);
        });
        chargeResourceServiceConfigMapper.insert(chargeResourceServiceConfigs);
    }
}
