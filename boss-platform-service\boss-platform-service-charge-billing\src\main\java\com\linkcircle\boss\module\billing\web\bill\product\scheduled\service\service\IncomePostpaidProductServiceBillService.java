package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service;

import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PostpaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.enums.BillingProcessStatusEnum;

/**
 * <AUTHOR>
 * @date 2025-07-07 16:12
 * @description 后付费产品服务账单服务接口
 */
public interface IncomePostpaidProductServiceBillService {

    /**
     * 保存单个产品服务账单（推荐使用）
     *
     * @param bill 账单信息
     * @return 是否保存成功
     */
    boolean saveServiceBill(PostpaidProductServiceIncomeBillDO bill);

    /**
     * 检查账单是否已存在
     *
     * @param serviceCode      服务Code
     * @param subscriptionId   订阅ID
     * @param billingStartTime 出账周期开始
     * @return 是否已存在
     */
    boolean isBillExists(String serviceCode,
                         Long subscriptionId,
                         Long billingStartTime,
                         Long billingEndTime);

    /**
     * 更新账单状态
     *
     * @param billId 账单ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateBillStatus(Long billId, BillingProcessStatusEnum status);

    /**
     * 根据账单ID查询账单详情
     *
     * @param billId    账单ID
     * @param startTime 计费开始时间
     * @return 账单详情
     */
    PostpaidProductServiceIncomeBillDO getBillById(Long billId, Long startTime);

    /**
     * 更新服务账单费用信息
     *
     * @param serviceIncomeBillDO 服务账单DO
     * @return 是否更新成功
     */
    boolean updateServiceBillFeeInfo(PostpaidProductServiceIncomeBillDO serviceIncomeBillDO);

    /**
     * 根据订阅ID、服务Code和时间范围统计使用量
     *
     * @param subscriptionId   订阅ID
     * @param serviceCode      服务Code
     * @param billingStartTime 计费开始时间
     * @param billingEndTime   计费结束时间
     * @return 总使用量
     */
    PostpaidIncomeBillDetailDO getTotalUsageByServiceAndTime(Integer billingType,
                                                             Long subscriptionId,
                                                             String serviceCode,
                                                             Long billingStartTime,
                                                             Long billingEndTime);
}
