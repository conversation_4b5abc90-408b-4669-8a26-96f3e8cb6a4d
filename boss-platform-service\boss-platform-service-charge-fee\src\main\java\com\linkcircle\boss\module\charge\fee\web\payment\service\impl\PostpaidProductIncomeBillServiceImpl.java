package com.linkcircle.boss.module.charge.fee.web.payment.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.tanant.TenantIgnore;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.enums.WalletsDeductStatusEnum;
import com.linkcircle.boss.module.charge.fee.web.payment.mapper.PostpaidProductIncomeBillMapper;
import com.linkcircle.boss.module.charge.fee.web.payment.mapper.PostpaidProductServiceIncomeBillMapper;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IPostpaidProductIncomeBillService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;

/**
 * 后付费 主表账单
 */
@Service
@Slf4j
public class PostpaidProductIncomeBillServiceImpl extends ServiceImpl<PostpaidProductIncomeBillMapper, PostpaidProductIncomeBillDO> implements IPostpaidProductIncomeBillService {

    @Autowired
    private PostpaidProductServiceIncomeBillMapper postpaidProductServiceIncomeBillMapper;

    @Override
    @TenantIgnore
    public boolean unSettledBillStatus(Long billId, Integer billStatus, Integer walletsStatus, JSONObject billJSON) throws Exception {
        // 已支付、未支付金额
        BigDecimal paidAmount = billJSON.getBigDecimal("paidAmount");
        BigDecimal unPaidAmount = billJSON.getBigDecimal("unPaidAmount");
        // 统计计算明细的支付金额
        BigDecimal calcPaidAmount = billJSON.getBigDecimal("paidAmount");
        // 上次支付金额
        BigDecimal lastPaidAmount = billJSON.getBigDecimal("lastPaidAmount");

        LambdaQueryWrapper<PostpaidProductServiceIncomeBillDO> serviceBillWrapper = new LambdaQueryWrapper<>();
        serviceBillWrapper.eq(PostpaidProductServiceIncomeBillDO::getBillId, billId);
        serviceBillWrapper.orderByAsc(PostpaidProductServiceIncomeBillDO::getAmountWithTax);
        List<PostpaidProductServiceIncomeBillDO> postpaidProductServiceIncomeBillDOS = postpaidProductServiceIncomeBillMapper.selectList(serviceBillWrapper);

//        long payTime = LocalDateTime.now().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        long payTime = Calendar.getInstance().getTimeInMillis();

        if (unPaidAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 未支付金额大于0 未结清的情况
            /*
             * 获取后付费服务账单明细 根据金额或者积分 从低到高排序
             */
            LambdaUpdateChainWrapper<PostpaidProductServiceIncomeBillDO> chainWrapper = new LambdaUpdateChainWrapper<>(PostpaidProductServiceIncomeBillDO.class);
            for (PostpaidProductServiceIncomeBillDO serviceIncomeBillDO : postpaidProductServiceIncomeBillDOS) {
                // 如果明细账单已经完成 则跳过
                if (Objects.equals(serviceIncomeBillDO.getWalletDeductStatus(), WalletsDeductStatusEnum.SUCCESS.getCode())) {
                    continue;
                }
                // 明细服务账单金额 - 获取剩余未支付金额
                BigDecimal amountWithTax = serviceIncomeBillDO.getUnpaidAmount();
                // 剩余已支付金额 是 0 没有可扣金额
                if (calcPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
                    chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                            .set(PostpaidProductServiceIncomeBillDO::getBillStatus, billStatus)
                            .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, walletsStatus)
                            .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, BigDecimal.ZERO)
                            .set(PostpaidProductServiceIncomeBillDO::getPaymentTime, payTime)
                            .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, amountWithTax);
                    postpaidProductServiceIncomeBillMapper.update(chainWrapper);
                    chainWrapper.clear();
                    continue;
                }
                // 剩余已支付金额 > 明细账单金额
                int compared = calcPaidAmount.compareTo(amountWithTax);
                if (compared >= 0) {
                    // 剩余已支付的金额 > 明细账单金额 减去明细账单金额
                    // 完成二次支付时 PaidAmount 就是明细账单金额
                    calcPaidAmount = calcPaidAmount.subtract(amountWithTax);
                    chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                            .set(PostpaidProductServiceIncomeBillDO::getBillStatus, InvoiceEnum.BillStatus.PAID.getCode())
                            .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, WalletsDeductStatusEnum.SUCCESS.getCode())
                            .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, serviceIncomeBillDO.getAmountWithTax())
                            .set(PostpaidProductServiceIncomeBillDO::getPaymentTime, payTime)
                            .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, BigDecimal.ZERO);
                    postpaidProductServiceIncomeBillMapper.update(chainWrapper);
                } else {
                    // 剩余已支付的金额 < 明细账单金额
                    // calcPaidAmount 变成已经支付的金额  amountWithTax-calcPaidAmount 是还未付的金额
                    chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                            .set(PostpaidProductServiceIncomeBillDO::getBillStatus, billStatus)
                            .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, walletsStatus)
                            .set(PostpaidProductServiceIncomeBillDO::getPaymentTime, payTime)
                            .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, calcPaidAmount.add(serviceIncomeBillDO.getPaidAmount()))
                            .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, amountWithTax.subtract(calcPaidAmount));
                    postpaidProductServiceIncomeBillMapper.update(chainWrapper);
                    // 将剩余扣费后 剩余已支付金额置空
                    calcPaidAmount = BigDecimal.ZERO;
                }
                chainWrapper.clear();
            }
        } else {
            // 没有未支付金额 已支付的情况
            LambdaUpdateChainWrapper<PostpaidProductServiceIncomeBillDO> chainWrapper = new LambdaUpdateChainWrapper<>(PostpaidProductServiceIncomeBillDO.class);
            for (PostpaidProductServiceIncomeBillDO serviceIncomeBillDO : postpaidProductServiceIncomeBillDOS) {
                // 明细服务账单金额
                BigDecimal amountWithTax = serviceIncomeBillDO.getAmountWithTax();
                // 没有未支付金额 说明全额支付了
                chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                        .set(PostpaidProductServiceIncomeBillDO::getBillStatus, billStatus)
                        .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, walletsStatus)
                        .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, amountWithTax)
                        .set(PostpaidProductServiceIncomeBillDO::getPaymentTime, payTime)
                        .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, BigDecimal.ZERO);
                postpaidProductServiceIncomeBillMapper.update(chainWrapper);
                chainWrapper.clear();
            }
        }

        // 主账单更新
        return lambdaUpdate().eq(PostpaidProductIncomeBillDO::getProductBillId, billId)
                .set(PostpaidProductIncomeBillDO::getBillStatus, billStatus)
                .set(PostpaidProductIncomeBillDO::getPaidAmount, paidAmount.add(lastPaidAmount))
                .set(PostpaidProductIncomeBillDO::getPaymentTime, payTime)
                .set(PostpaidProductIncomeBillDO::getUnpaidAmount, unPaidAmount)
                .update();
    }

    /**
     * 修改账单状态
     * 根据未支付金额 是否存在分别处理
     * 存在未支付金额 则计算产品归属服务明细修改满足金额的明细订单为已完成状态
     *
     * @param billId     主账单 ID
     * @param billStatus 支付状态
     * @return
     * @throws Exception
     */
    @Override
    @TenantIgnore
    public boolean updateBillStatus(Long billId, Integer billStatus, Integer walletsStatus, JSONObject billJSON) throws Exception {
        // 已支付、未支付
        BigDecimal paidAmount = billJSON.getBigDecimal("paidAmount");
        BigDecimal unPaidAmount = billJSON.getBigDecimal("unPaidAmount");
        // 统计计算明细的支付金额
        BigDecimal calcPaidAmount = billJSON.getBigDecimal("paidAmount");

        PostpaidProductServiceIncomeBillDO postpaidProductServiceIncomeBillDO = new PostpaidProductServiceIncomeBillDO();

        LambdaQueryWrapper<PostpaidProductServiceIncomeBillDO> serviceBillWrapper = new LambdaQueryWrapper<>();
        serviceBillWrapper.eq(PostpaidProductServiceIncomeBillDO::getBillId, billId);
        serviceBillWrapper.orderByAsc(PostpaidProductServiceIncomeBillDO::getAmountWithTax);
        List<PostpaidProductServiceIncomeBillDO> postpaidProductServiceIncomeBillDOS = postpaidProductServiceIncomeBillMapper.selectList(serviceBillWrapper);

        if (CollectionUtils.isEmpty(postpaidProductServiceIncomeBillDOS)) {
            log.info("明细账单不存在, 主张单 billId:{}", billId);
            return false;
        }

//        long payTime = LocalDateTime.now().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        long payTime = Calendar.getInstance().getTimeInMillis();

        if (unPaidAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 未支付金额大于0 未结清的情况
            /*
             * 获取后付费服务账单明细 根据金额或者积分 从低到高排序
             */
            LambdaUpdateWrapper<PostpaidProductServiceIncomeBillDO> chainWrapper = new LambdaUpdateWrapper<>(PostpaidProductServiceIncomeBillDO.class);
            for (PostpaidProductServiceIncomeBillDO serviceIncomeBillDO : postpaidProductServiceIncomeBillDOS) {
                // 如果明细账单已经完成 则跳过
                if (Objects.equals(serviceIncomeBillDO.getWalletDeductStatus(), WalletsDeductStatusEnum.SUCCESS.getCode())) {
                    continue;
                }
                // 明细服务账单金额
                BigDecimal amountWithTax = serviceIncomeBillDO.getAmountWithTax();
                // 剩余已支付金额 是 0 没有可扣金额
                if (calcPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
                    chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                            .set(PostpaidProductServiceIncomeBillDO::getBillStatus, billStatus)
                            .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, walletsStatus)
                            .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, BigDecimal.ZERO)
                            .set(PostpaidProductServiceIncomeBillDO::getPaymentTime, payTime)
                            .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, amountWithTax);
                    postpaidProductServiceIncomeBillMapper.update(chainWrapper);
                    chainWrapper.clear();
                    continue;
                }
                // 剩余已支付金额 > 明细账单金额
                int compared = calcPaidAmount.compareTo(amountWithTax);
                if (compared >= 0) {
                    // 剩余已支付的金额 > 明细账单金额 减去明细账单金额
                    calcPaidAmount = calcPaidAmount.subtract(amountWithTax);
                    chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                            .set(PostpaidProductServiceIncomeBillDO::getBillStatus, InvoiceEnum.BillStatus.PAID.getCode())
                            .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, WalletsDeductStatusEnum.SUCCESS.getCode())
                            .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, amountWithTax)
                            .set(PostpaidProductServiceIncomeBillDO::getPaymentTime, payTime)
                            .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, BigDecimal.ZERO);
                    postpaidProductServiceIncomeBillMapper.update(chainWrapper);
                } else {
                    // 剩余已支付的金额 < 明细账单金额
                    // calcPaidAmount 变成已经支付的金额  amountWithTax-calcPaidAmount 是还未付的金额
                    chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                            .set(PostpaidProductServiceIncomeBillDO::getBillStatus, billStatus)
                            .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, walletsStatus)
                            .set(PostpaidProductServiceIncomeBillDO::getPaymentTime, payTime)
                            .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, calcPaidAmount)
                            .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, amountWithTax.subtract(calcPaidAmount));
                    postpaidProductServiceIncomeBillMapper.update(chainWrapper);
                    // 将剩余扣费后 剩余已支付金额置空
                    calcPaidAmount = BigDecimal.ZERO;
                }
                chainWrapper.clear();
            }
        } else {
            // 没有未支付金额 已支付的情况
            LambdaUpdateChainWrapper<PostpaidProductServiceIncomeBillDO> chainWrapper = new LambdaUpdateChainWrapper<>(PostpaidProductServiceIncomeBillDO.class);
            for (PostpaidProductServiceIncomeBillDO serviceIncomeBillDO : postpaidProductServiceIncomeBillDOS) {
                // 明细服务账单金额
                BigDecimal amountWithTax = serviceIncomeBillDO.getAmountWithTax();
                // 没有未支付金额 说明全额支付了
                chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                        .set(PostpaidProductServiceIncomeBillDO::getBillStatus, billStatus)
                        .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, walletsStatus)
                        .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, amountWithTax)
                        .set(PostpaidProductServiceIncomeBillDO::getPaymentTime, payTime)
                        .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, BigDecimal.ZERO);
                postpaidProductServiceIncomeBillMapper.update(chainWrapper);
                chainWrapper.clear();
            }
        }

        // 主账单更新
        return lambdaUpdate().eq(PostpaidProductIncomeBillDO::getProductBillId, billId)
                .set(PostpaidProductIncomeBillDO::getBillStatus, billStatus)
                .set(PostpaidProductIncomeBillDO::getPaidAmount, paidAmount)
                .set(PostpaidProductIncomeBillDO::getWalletDeductStatus, walletsStatus)
                .set(PostpaidProductIncomeBillDO::getPaymentTime, payTime)
                .set(PostpaidProductIncomeBillDO::getUnpaidAmount, unPaidAmount)
                .update();
    }

    @Override
    @TenantIgnore
    public boolean updateBillNotSufficientFundsStatus(Long billId, Integer billStatus, JSONObject billJSON) throws Exception {
        Integer paymentMethod = billJSON.getInteger("paymentMethod");
        // 待支付金额 or 积分
        BigDecimal unpaidAmount = 0 == paymentMethod ? billJSON.getBigDecimal("cashAmount") : billJSON.getBigDecimal("pointAmount");

        LambdaQueryWrapper<PostpaidProductServiceIncomeBillDO> serviceBillWrapper = new LambdaQueryWrapper<>();
        serviceBillWrapper.eq(PostpaidProductServiceIncomeBillDO::getBillId, billId);
        serviceBillWrapper.orderByAsc(PostpaidProductServiceIncomeBillDO::getAmountWithTax);
        List<PostpaidProductServiceIncomeBillDO> postpaidProductServiceIncomeBillDOS = postpaidProductServiceIncomeBillMapper.selectList(serviceBillWrapper);

        LambdaUpdateChainWrapper<PostpaidProductServiceIncomeBillDO> chainWrapper = new LambdaUpdateChainWrapper<>(PostpaidProductServiceIncomeBillDO.class);
        for (PostpaidProductServiceIncomeBillDO serviceIncomeBillDO : postpaidProductServiceIncomeBillDOS) {
            chainWrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, serviceIncomeBillDO.getProductServiceBillId())
                    .set(PostpaidProductServiceIncomeBillDO::getBillStatus, billStatus)
                    .set(PostpaidProductServiceIncomeBillDO::getWalletDeductStatus, WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())
                    .set(PostpaidProductServiceIncomeBillDO::getPaidAmount, BigDecimal.ZERO)
                    .set(PostpaidProductServiceIncomeBillDO::getUnpaidAmount, serviceIncomeBillDO.getAmountWithTax());
            postpaidProductServiceIncomeBillMapper.update(chainWrapper);
            chainWrapper.clear();
        }

        // 主账单更新
        return lambdaUpdate().eq(PostpaidProductIncomeBillDO::getProductBillId, billId)
                .set(PostpaidProductIncomeBillDO::getBillStatus, billStatus)
                .set(PostpaidProductIncomeBillDO::getPaidAmount, BigDecimal.ZERO)
                .set(PostpaidProductIncomeBillDO::getWalletDeductStatus, WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())
                .set(PostpaidProductIncomeBillDO::getUnpaidAmount, unpaidAmount)
                .update();
    }

    public PostpaidProductIncomeBillDO getBillByID(Long billId) throws Exception {
        return lambdaQuery().select(PostpaidProductIncomeBillDO::getProductBillId,
                        PostpaidProductIncomeBillDO::getAmountWithTax,
                        PostpaidProductIncomeBillDO::getPaidAmount,
                        PostpaidProductIncomeBillDO::getUnpaidAmount,
                        PostpaidProductIncomeBillDO::getBillStatus)
                .eq(PostpaidProductIncomeBillDO::getProductBillId, billId)
                .one();
    }

}
