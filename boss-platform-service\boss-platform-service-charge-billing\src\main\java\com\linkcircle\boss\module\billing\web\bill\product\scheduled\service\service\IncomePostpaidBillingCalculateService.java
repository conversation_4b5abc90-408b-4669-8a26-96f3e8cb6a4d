package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service;

import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.IncomePostpaidBillingMessageDTO;

/**
 * <AUTHOR>
 * @date 2025-07-07 16:12
 * @description 后付费出账计算服务接口
 */
public interface IncomePostpaidBillingCalculateService {

    /**
     * 处理出账计算
     *
     * @param billingMessage 出账消息
     */
    void processBillingCalculation(IncomePostpaidBillingMessageDTO billingMessage);

    /**
     * 查询使用量数据并计算费用
     *
     * @param billingMessage 出账消息
     * @return 是否计算成功
     */
    boolean calculateUsageAndFee(IncomePostpaidBillingMessageDTO billingMessage);

}
