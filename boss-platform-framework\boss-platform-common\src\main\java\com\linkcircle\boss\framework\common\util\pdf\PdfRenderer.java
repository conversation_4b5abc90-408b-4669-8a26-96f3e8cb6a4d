package com.linkcircle.boss.framework.common.util.pdf;

import cn.hutool.core.io.FileUtil;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/18 15:34
 * @description PDF渲染器   该类主要用于将模板文件渲染为PDF文件。
 */
@Slf4j
public class PdfRenderer {

    public static final String FONT_DIR = "/C:/Windows/Fonts/";
    public static final String FTL_DIR = "/templates/";
    public static final String FTL_CSS_DIR = "templates/css/";


    public static class Builder {
        private String templateFileName;
        private String cssFileName;
        private Map<String, Object> data;

        /**
         * 设置模板文件名
         *
         * @param templateFileName 模板文件名
         * @return 当前构建器实例
         */
        public Builder withTemplate(String templateFileName) {
            this.templateFileName = templateFileName;
            return this;
        }

        /**
         * 设置CSS文件名
         *
         * @param cssFileName CSS文件名
         * @return 返回Builder对象，以便进行链式调用
         */
        public Builder withCssFileName(String cssFileName) {
            this.cssFileName = cssFileName;
            return this;
        }

        /**
         * 向构建器中添加数据
         *
         * @param data 需要添加的数据，数据类型为 Map<String, Object>
         * @return 返回构建器本身，方便链式调用
         */
        public Builder withData(Map<String, Object> data) {
            this.data = data;
            return this;
        }

        /**
         * 生成并返回渲染后的输出流
         *
         * @return 渲染后的输出流
         * @throws Exception 如果在生成输出流过程中发生异常，则抛出该异常
         */
        public OutputStream renderStream() throws Exception {
            return withOutputStream(createHtmlStrAndCss(data, templateFileName, cssFileName));
        }

        /**
         * 渲染模板并生成字节数组
         *
         * @return 渲染后的字节数组
         * @throws Exception 如果渲染过程中出现异常，则抛出异常
         */
        public byte[] render() throws Exception {
            return withBytes(createHtmlStrAndCss(data, templateFileName, cssFileName));
        }

        /**
         * 将文档渲染为HTML格式。
         *
         * @return 渲染后的HTML字符串
         * @throws Exception 如果渲染过程中出现错误
         */
        public String renderHtml() throws Exception {
            return documentToHtml(createHtmlStrAndCss(data, templateFileName, cssFileName));
        }
    }

    /**
     * 将Document对象转换为HTML格式的字符串
     *
     * @param doc 要转换的Document对象
     * @return 转换后的HTML字符串
     * @throws TransformerException 如果转换过程中出现错误
     */
    public static String documentToHtml(Document doc) throws TransformerException {
        TransformerFactory factory = TransformerFactory.newInstance();
        Transformer transformer = factory.newTransformer();

        // 设置输出属性
        transformer.setOutputProperty(OutputKeys.METHOD, "html");
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");

        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(doc), new StreamResult(writer));
        return writer.toString();
    }


    /**
     * 模板生成html字符串
     *
     * @param data             数据
     * @param templateFileName 模板文件名
     * @return 生成的HTML字符串
     * @throws IOException       文件读取异常
     * @throws TemplateException 模板处理异常
     */
    public static String createHtmlStr(Map<String, Object> data, String templateFileName) throws IOException, TemplateException {
        // 创建FreeMarker Configuration实例
        Configuration cfg = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        // 指定FreeMarker模板文件的位置
        cfg.setClassForTemplateLoading(PdfRenderer.class, FTL_DIR);
        // 获取模板文件
        Template template = cfg.getTemplate(templateFileName, "UTF-8");
        StringWriter stringWriter = new StringWriter();
        try (BufferedWriter writer = new BufferedWriter(stringWriter)) {
            template.process(data, writer);
        }

        // 将现代CSS转换为pdf专用样式
        return PdfCssCompatConverter.convertModernCssToPdfCompatible(stringWriter.toString());
    }

    /**
     * 根据给定的数据和模板生成带有CSS样式的HTML字符串，并返回解析后的Document对象。
     *
     * @param data             包含模板所需数据的Map对象
     * @param templateFileName 模板文件的名称
     * @param cssFileName      CSS文件的名称
     * @return 解析后的Document对象
     * @throws IOException       如果发生I/O错误
     * @throws TemplateException 如果模板处理时发生错误
     */
    public static Document createHtmlStrAndCss(Map<String, Object> data, String templateFileName, String cssFileName) throws IOException, TemplateException {
        String htmlStr = createHtmlStr(data, templateFileName);
        if (cssFileName == null || cssFileName.isEmpty()) {
            return html5ParseDocument(htmlStr);
        } else {

            // 处理Html5中的特殊字符
            Document doc = html5ParseDocument(htmlStr);
            String file = PdfRenderer.class.getClassLoader().getResource(FTL_CSS_DIR + cssFileName).getFile();
            String cssStr = FileUtil.readString(new File(file), StandardCharsets.UTF_8);
            appendCss(doc, templateFileName, cssStr);
            return doc;
        }
    }

    /**
     * 使用给定的文档生成PDF输出流。
     *
     * @param doc 需要渲染为PDF的文档对象
     * @return 生成的PDF输出流
     * @throws Exception 如果在生成PDF时发生任何错误，将抛出异常
     */
    private static OutputStream withOutputStream(Document doc) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // pdf渲染器
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.useFastMode();
            handleFont(builder);
            builder.withW3cDocument(doc, null);
            builder.toStream(outputStream);
            builder.useDefaultPageSize(310, 297, PdfRendererBuilder.PageSizeUnits.MM);
            builder.run();
            return outputStream;
        } catch (Exception e) {
            log.error("PDF渲染时发生错误: ", e);
            throw new Exception("PDF渲染失败", e);
        }
    }

    /**
     * 将HTML字符串转换为PDF并返回输出流
     *
     * @param htmlStr HTML字符串
     * @return PDF输出流
     * @throws Exception 如果PDF渲染失败则抛出异常
     */
    private static OutputStream withOutputStream(String htmlStr) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 创建pdf渲染器
            // pdf渲染器
            PdfRendererBuilder builder = new PdfRendererBuilder();
            // 使用快速模式
            builder.useFastMode();
            // 处理字体设置
            handleFont(builder);
            // 解析HTML字符串为Document对象，处理Html5中的特殊字符
            // 处理Html5中的特殊字符
            Document doc = html5ParseDocument(htmlStr);
            // 将Document对象传递给渲染器
            builder.withW3cDocument(doc, null);
            // 将渲染结果输出到指定的输出流
            builder.toStream(outputStream);
            // 执行渲染操作
            builder.run();
            return outputStream;
        } catch (Exception e) {
            // 日志记录错误信息
            log.error("PDF渲染时发生错误: ", e);
            // 抛出新的异常，提示PDF渲染失败
            throw new Exception("PDF渲染失败", e);
        }
    }


    /**
     * 将CSS样式添加到指定文档的<head>标签中
     *
     * @param doc              文档对象，表示需要添加CSS样式的文档
     * @param templateFileName 模板文件名，用于在异常信息中提示用户
     * @param cssStr           需要添加的CSS样式字符串
     * @throws RuntimeException 如果添加CSS样式失败，抛出运行时异常
     */
    private static void appendCss(Document doc, String templateFileName, String cssStr) {
        try {
            // 获取head标签元素
            Element head = (Element) doc.getElementsByTagName("head").item(0);
            // 创建style标签元素
            Element style = doc.createElement("style");
            // 设置style标签的文本内容为cssStr
            style.setTextContent(cssStr);
            // 4. 组装DOM树
            // 将style标签添加到head标签中
            head.appendChild(style);
        } catch (Exception e) {
            // 抛出运行时异常，说明添加CSS样式失败
            throw new RuntimeException("添加CSS样式失败,模板文件" + templateFileName + "可能不存在head标签", e);
        }
    }


    /**
     * 将HTML字符串转换成字节数组
     *
     * @param htmlStr HTML字符串
     * @return 转换后的字节数组
     * @throws Exception 如果转换过程中发生异常
     */
    private static byte[] withBytes(String htmlStr) throws Exception {
        // 使用 withOutputStream 方法获取 ByteArrayOutputStream 对象
        ByteArrayOutputStream outputStream = (ByteArrayOutputStream) withOutputStream(htmlStr);
        // 将 ByteArrayOutputStream 对象转换为字节数组并返回
        return outputStream.toByteArray();
    }


    /**
     * 将给定的Document对象转换为字节数组。
     *
     * @param document 要转换的Document对象
     * @return 转换后的字节数组
     * @throws Exception 如果转换过程中出现异常，则抛出异常
     */
    private static byte[] withBytes(Document document) throws Exception {
        ByteArrayOutputStream outputStream = (ByteArrayOutputStream) withOutputStream(document);
        return outputStream.toByteArray();
    }

    /**
     * 处理字体。
     *
     * @param builder PdfRendererBuilder对象，用于构建Pdf渲染器。
     * @throws IOException 如果在读取系统属性或处理文件路径时发生I/O错误。
     */
    private static void handleFont(PdfRendererBuilder builder) throws IOException {
        // 获取操作系统名称并转换为小写
        String os = System.getProperty("os.name").toLowerCase(Locale.ROOT);

        // 根据操作系统类型设置字体目录路径
        Path fontDirectory = os.contains("win") ? Paths.get(FONT_DIR.substring(1)) : Paths.get(FONT_DIR);

        try {
            // 在字体目录中查找所有字体
            List<AutoFont.CSSFont> fonts = AutoFont.findFontsInDirectory(fontDirectory);

            // 将找到的字体添加到PdfRendererBuilder对象中
            AutoFont.toBuilder(builder, fonts);
        } catch (IOException e) {
            // 如果在查找字体时发生异常，记录错误信息并重新抛出异常
            log.error("在目录中查找字体时发生错误: ", e);
            throw e;
        }
    }


    /**
     * 将HTML字符串解析为Document对象。
     *
     * @param htmlStr HTML字符串
     * @return 解析后的Document对象
     */
    private static Document html5ParseDocument(String htmlStr) {
        org.jsoup.nodes.Document doc = Jsoup.parse(htmlStr);
        // 处理Html5中的特殊字符
        doc.outputSettings().escapeMode(org.jsoup.nodes.Entities.EscapeMode.xhtml);
        return new W3CDom().fromJsoup(doc);
    }


}
