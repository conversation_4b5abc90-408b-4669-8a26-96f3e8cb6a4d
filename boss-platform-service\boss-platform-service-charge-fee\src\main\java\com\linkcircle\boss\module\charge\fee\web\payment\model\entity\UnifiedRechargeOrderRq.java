package com.linkcircle.boss.module.charge.fee.web.payment.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一充值下单请求
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("unified_recharge_order_rq")
public class UnifiedRechargeOrderRq {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 支付方式编码
     * 支付宝扫码:ALLDEBIT_AP,微信扫码:ALLDEBIT_WX,paynow扫码:ALLDEBIT_PN
     */
    @Schema(description = "支付方式编码")
    private String wayCode;

    /**
     * 业务生成的订单号
     */
    @Schema(description = "业务生成的订单号")
    private String mchOrderNo;

    /**
     * 支付金额,单位分
     */
    @Schema(description = "支付金额")
    private Long amount;

    /**
     * 货币类型 固定SGD
     */
    @Schema(description = "货币类型")
    private String currency;

    /**
     * 客户端IP地址,如************
     */
    @Schema(description = "客户端IP地址")
    private String clientIp;

    /**
     * 商品标题
     */
    @Schema(description = "商品标题")
    private String subject;

    /**
     * 商品描述信息
     */
    @Schema(description = "商品描述信息")
    private String body;

    /**
     * 支付结果异步回调URL，只有传了该值才会发起回调
     */
    @Schema(description = "支付结果异步回调URL")
    private String notifyUrl;

    /**
     * 服务商ID，由支付中心分配   固定10000000000000001
     */
    @Schema(description = "服务商ID")
    private String isvId;

    /**
     * 客户用户名

     private String mchName; */

    /**
     * 客户ID
     */
    @Schema(description = "商户ID")
    private String mchNo;

    @Schema(description = "钱包id")
    private Long walletsId;

    /**
     * 版本号：固定1.0
     */
    @Schema(description = "版本号")
    private String version;

    /**
     * 签名值
     */
    @Schema(description = "签名值")
    private String sign;

    /**
     * 请求接口时间,13位时间戳
     */
    @Schema(description = "请求接口时间")
    private Long reqTime;

    /**
     * 签名类型，如：MD5
     */
    @Schema(description = "签名类型")
    private String signType;

    /**
     * 请求时区,为数字0-23
     */
    @Schema(description = "请求时区")
    private String reqTimezone;

    /**
     * 支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭
     */
    @Schema(description = "支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭")
    private Integer status;


    /**
     * 请求ID

     private String requestId;*/

    /**
     * 订单失效时间,单位秒,默认2小时.订单在(创建时间+失效时间)后失效
     */
    @Schema(description = "订单失效时间")
    private Integer expiredTime;

    /**
     * 商户扩展参数,回调时会原样返回

     private String extParam;*/

    /**
     * 特定渠道发起的额外参数,json格式字符串.详见渠道参数说明

     private String channelExtra;*/

    /**
     * 渠道用户ID

     private String channelUserId;*/
}

