package com.linkcircle.boss.module.charge.fee.web.bill.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.framework.web.context.LoginUser;
import com.linkcircle.boss.framework.web.core.util.WebFrameworkUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.convert.IncomeBillHistoryConvert;
import com.linkcircle.boss.module.charge.fee.web.bill.convert.PostpaidIncomeProductConvert;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.IncomeBillHistoryMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.PostpaidIncomeProductServiceBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.PostpaidMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqHistoryDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.InvoiceBillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.IncomeBillHistoryDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.history.IncomeBillHistoryVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid.PostpaidIncomeServiceVO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillContentHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.InvoiceIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.PostpaidService;
import com.linkcircle.boss.module.charge.fee.web.bill.util.NumberUtil;
import com.linkcircle.boss.module.charge.fee.web.invoice.mapper.InvoiceMapper;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.*;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.entity.ChargeInvoiceDO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/30 9:53
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PostpaidServiceImpl implements PostpaidService {


    private final InvoiceMapper invoiceMapper;


    private final PostpaidMapper postpaidMapper;

    private final PostpaidIncomeProductServiceBillMapper postpaidIncomeProductServiceBillMapper;

    private final RocketMQTemplate rocketMQTemplate;
    private final CustomerApi customerApi;
    private final EntityApi entityApi;
    private final IncomeBillHistoryMapper incomeBillHistoryMapper;
    private final InvoiceIdGenerator invoiceIdGenerator;

    /**
     * 处理创建发票的逻辑
     *
     * @param invoiceAmount 发票金额对象，包含发票金额等信息
     * @param detailMap     账单详情映射，包含账单详情对象
     * @param invoiceDOs    发票对象列表，用于存储创建的发票对象
     * @param createReqDTO  创建发票请求对象，包含发票类型等信息
     */
    public void handleCreateInvoice(BillInvoiceAmountDTO invoiceAmount, Map<String, PostpaidProductIncomeBillDO> detailMap, List<ChargeInvoiceDO> invoiceDOs,

                                    BillInvoiceCreateReqDTO createReqDTO) {
        Long billDetailId = invoiceAmount.getBillDetailId();
        PostpaidProductIncomeBillDO billDetailDO = detailMap.get(billDetailId.toString());
        // 如果账单详情不存在，则抛出异常
        if (billDetailDO == null) {
            log.info("账单详情不存在,billDetailId:{}", billDetailId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST);
        }

        // 如果开票金额小于等于0，则跳过
        if (invoiceAmount.getInvoiceAmount() == null || invoiceAmount.getInvoiceAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("开票金额小于等于0 不需要开票,billDetailId:{},invoiceAmount:{}", billDetailId, invoiceAmount.getInvoiceAmount());
            return;
        }

        // 如果账单已结清，则抛出异常
        if (!billDetailDO.getBillStatus().equals(InvoiceEnum.BillStatus.PAID.getCode())) {
            log.info("账单已结清,billDetailId:{}", billDetailId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_NOT_PAID_INVOICE);
        }

        // 如果开票金额大于可开票金额，则抛出异常
        if (billDetailDO.getAvailableInvoiceAmount().compareTo(invoiceAmount.getInvoiceAmount()) < 0) {
            log.info("开票金额大于可开票金额,billDetailId:{},invoiceAmount:{}", billDetailId, invoiceAmount.getInvoiceAmount());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_AMOUNT_NOT_ENOUGH);
        }
        String invoiceNo = invoiceIdGenerator.generateId(createReqDTO.getEntityId());
        // 创建发票DTO
        // 创建发票DTO
        ChargeInvoiceDO invoiceDTO = ChargeInvoiceDO.builder()
                .id(IdUtil.getSnowflakeNextId())
                .invoiceId(StringUtils.isNotBlank(invoiceNo) ? invoiceNo : billDetailDO.getBillNo())
                .invoiceBillingId(billDetailId.toString())
                .invoiceBillingNo(billDetailDO.getBillNo())
                .invoiceAmount(invoiceAmount.getInvoiceAmount())
                .invoiceType(createReqDTO.getInvoiceType())
                .billType(InvoiceEnum.BillType.POSTPAID.getCode())
                .type(createReqDTO.getType())
                .entityId(billDetailDO.getEntityId())
                .customerId(billDetailDO.getCustomerId())
                .accountId(billDetailDO.getAccountId())
                .status(InvoiceEnum.Status.WAIT_AUDIT.getCode())
                .billServiceCode("")
                .currencySymbol(billDetailDO.getCurrency())
                .billTime(billDetailDO.getBillingTime())
                .accountId(createReqDTO.getAccountId())
                .customerJsonStr(JSONUtil.toJsonStr(createReqDTO.getCustomer()))
                .entityJsonStr(JSONUtil.toJsonStr(createReqDTO.getEntity()))
                .build();

        // 发送mq 消息
        invoiceDOs.add(invoiceDTO);

        // 更新账单详情 已开票金额 已可开票金额
        BigDecimal invoicedAmount = billDetailDO.getInvoicedAmount().add(invoiceAmount.getInvoiceAmount());
        BigDecimal availableInvoiceAmount = billDetailDO.getDiscountAmount().subtract(invoicedAmount);

        // 更新账单可开票金额
        billDetailDO.setInvoicedAmount(invoicedAmount);
        billDetailDO.setAvailableInvoiceAmount(availableInvoiceAmount);

        // 发送mq消息
        // 更新账单详情 已开票金额 已可开票金额
        BigDecimal billInvoicedAmount = billDetailDO.getInvoicedAmount().add(invoiceAmount.getInvoiceAmount());
        BigDecimal billAvailableInvoiceAmount = billDetailDO.getDiscountAmount().subtract(invoicedAmount);

        // 更新账单可开票金额
        billDetailDO.setInvoicedAmount(billInvoicedAmount);
        billDetailDO.setAvailableInvoiceAmount(billAvailableInvoiceAmount);
    }

    /**
     * 检查账单发票
     *
     * @param checkReqDTO 检查请求对象，包含账单详情ID列表、提交类型等信息
     * @throws ServiceException 如果检查失败，抛出ServiceException异常
     */
    public void checkInvoice(BillInvoiceCheckDTO checkReqDTO) {
        BillInvoiceIdDTO idDTO = checkReqDTO.getIdDTO();
        if (idDTO == null || CollectionUtils.isEmpty(idDTO.getBillDetailIds())
        ) {
            log.info("账单详情ID列表为空");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_INVOICE_CHECK_IDS);
        }
        if (StringUtils.isBlank(idDTO.getStartTime()) || StringUtils.isBlank(idDTO.getEndTime())) {
            log.info("账单详情时间范围为空,{}发票校验失败", JSONUtil.toJsonStr(checkReqDTO));
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_INVOICE_CHECK_BILL_TIME_RANGE);
        }

        //tax_rate 税率  available_invoice_amount 可开票金额 >0  entity_id 出账主体id
        Long startTime = strToLong.apply(idDTO.getStartTime());
        Long endTime = strToLong.apply(idDTO.getEndTime());
        List<Long> detailIds = checkReqDTO.getIdDTO().getBillDetailIds();
        List<BillInvoiceCheckGroupDTO> checkResults = executeIgnore(() -> postpaidMapper.checkByIds(detailIds, startTime, endTime));
        if (CollectionUtils.isNotEmpty(checkResults) && Objects.equals(checkResults.size(), 1) && Objects.equals(checkResults.getFirst().getCount().intValue(), detailIds.size())) {
            log.info("账单详情ID列表符合开票条件,ids:{}", detailIds);
        } else {
            log.info("账单详情ID列表不符合开票条件,ids:{},result:{}", detailIds, JSONUtil.toJsonStr(checkResults));
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_INVOICE_CHECK_NOT_MATCH);
        }

    }


    /**
     * 创建发票
     *
     * @param createReqDTO 创建发票请求对象
     */
    @Transactional
    public void createInvoice(BillInvoiceCreateReqDTO createReqDTO) {
        checkInvoiceCustomerAndEntity(createReqDTO, ErrorCodeConstants.POSTPAID_INCOME_BILL_INVOICE_CREATE_CUSTOMER_NOT_EXIST, ErrorCodeConstants.POSTPAID_INCOME_BILL_INVOICE_CREATE_ENTITY_NOT_EXIST);

        // 获取发票金额列表
        List<BillInvoiceAmountDTO> invoiceAmounts = createReqDTO.getInvoiceAmounts();
        // 计算最大和最小账单时间
        Long maxTime = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillingTime).max(Long::compareTo).orElse(0L);
        Long minTime = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillingTime).min(Long::compareTo).orElse(0L);
        List<Long> productBillIds = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillDetailId).toList();
        // 根据账单详情ID查询账单详情
        List<PostpaidProductIncomeBillDO> bills = executeIgnore(() -> postpaidMapper.queryBillByIds(productBillIds, minTime, maxTime));

        // 如果账单详情为空，则抛出异常
        if (CollectionUtils.isEmpty(bills)) {
            log.info("账单详情不存在,billDetailIds:{}", invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillDetailId).toList());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        // 将账单详情转换为Map，方便后续查询
        Map<String, PostpaidProductIncomeBillDO> detailMap = bills.stream().collect(Collectors.toMap(t -> t.getProductBillId().toString(), t -> t, (d1, d2) -> d2));
        // 创建发票DTO列表和账单详情DO列表
        List<ChargeInvoiceDO> invoiceDOs = new ArrayList<>();
        // 遍历发票金额列表，处理每个发票金额
        for (BillInvoiceAmountDTO invoiceAmount : invoiceAmounts) {
            // check and handle 发票 和 账单 开票金额
            handleCreateInvoice(invoiceAmount, detailMap, invoiceDOs, createReqDTO);
        }
        invoiceMapper.insertOrUpdate(invoiceDOs);
        // 更新账单详情 已开票金额 已可开票金额
        executeIgnore(() -> postpaidMapper.updateById(detailMap.values()));
    }

    @Override
    public PostpaidIncomeProductBillDetailVO queryInvoiceBillDetail(InvoiceBillReqDto billReqDto) {
        PostpaidIncomeProductBillDetailVO detailVO = queryBill(BillReqDto.builder().billId(billReqDto.getBillId()).billingTime(billReqDto.getBillingTime()).build(), false);
        ChargeInvoiceDO chargeInvoiceDO = invoiceMapper.selectById(billReqDto.getInvoiceId());
        if (chargeInvoiceDO == null) {
            log.info("发票不存在,billId:{}", billReqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_INVOICE_NOT_EXIST);
        }
        assign(chargeInvoiceDO, t -> JSONUtil.toBean(t.getCustomerJsonStr(), ChargeCustomerInvoiceDTO.class), detailVO, PostpaidIncomeProductBillDetailVO::setCustomerInvoice);
        assign(chargeInvoiceDO, t -> JSONUtil.toBean(t.getEntityJsonStr(), EntityDetailsVO.class), detailVO, PostpaidIncomeProductBillDetailVO::setEntityInvoice);
        return detailVO;
    }

    @Override
    public PageResult<IncomeBillHistoryVO<PostpaidIncomeProductBillDetailVO>> historyBills(BillReqHistoryDto reqDto) {
        Page<?> page = MyBatisUtils.buildPage(reqDto);
        List<IncomeBillHistoryDO> historyList = executeIgnore(() -> incomeBillHistoryMapper.listByPage(page, reqDto.getBillId(), reqDto.getBillingTime()));
        List<IncomeBillHistoryVO<PostpaidIncomeProductBillDetailVO>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(historyList)) {
            list = historyList.stream().map(t -> {
                IncomeBillHistoryVO<PostpaidIncomeProductBillDetailVO> convert = IncomeBillHistoryConvert.INSTANCE.convertPostpaid(t);
                convert.setBillDetailVo(JSONUtil.toBean(t.getBillDetail(), PostpaidIncomeProductBillDetailVO.class));
                return convert;
            }).toList();
        }
        return MyBatisUtils.convert2PageResult(page, list);
    }

    @Override
    public void deleteBill(BillReqDto reqDto) {
        PostpaidIncomeProductBillDetailVO postpaidIncomeProductBillDetailVO = queryBill(reqDto, false);
        if (postpaidIncomeProductBillDetailVO == null) {
            log.info("账单不存在,billId:{}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        executeIgnore(() -> postpaidMapper.deleteByProduct(postpaidIncomeProductBillDetailVO));
        if (CollectionUtils.isNotEmpty(postpaidIncomeProductBillDetailVO.getServices())) {
            executeIgnore(() -> postpaidIncomeProductServiceBillMapper.deleteByServices(postpaidIncomeProductBillDetailVO.getServices()));
        }
    }

    @Override
    public void toDraft(BillReqDto reqDto) {
        // 执行查询操作，根据账单ID和账单时间查询账单数据
        List<PostpaidProductIncomeBillDO> makeupIncomeBillDOS = executeIgnore(() -> postpaidMapper.queryBillByIds(List.of(reqDto.getBillId()), reqDto.getBillingTime(), reqDto.getBillingTime()));

        // 判断查询结果是否为空
        if (CollectionUtils.isEmpty(makeupIncomeBillDOS)) {
            // 如果为空，则记录日志并抛出异常
            log.info("手工账单不存在，billId={}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }

        // 获取第一个账单对象
        PostpaidProductIncomeBillDO billDO = makeupIncomeBillDOS.getFirst();

        // 判断账单状态是否为待支付状态
        if (!InvoiceEnum.BillStatus.WAIT_PAYMENT.is(billDO.getBillStatus())) {
            // 如果不是待支付状态，则记录日志并抛出异常
            log.info("手工账单不是确认状态[待支付],不能退回到草稿状态，billId={}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_DRAFT_NOT_MATCH);
        }

        // 更新产品账单状态为草稿状态
        postpaidMapper.updateStatus(reqDto.getBillId(), reqDto.getBillingTime(), InvoiceEnum.BillStatus.DRAFT);
    }


    // markHistory(bill);

    private void markHistory(PostpaidProductIncomeBillDO bill) {
        IncomeBillHistoryDO last = executeIgnore(() -> incomeBillHistoryMapper.lastBillingHistory(bill.getProductBillId(), bill.getBillingTime(), true));
        LoginUser user = WebFrameworkUtils.getLoginUser();
        IncomeBillHistoryDO historyDO = IncomeBillHistoryDO.builder()
                .historyId(IdUtil.getSnowflakeNextId())
                .billId(bill.getProductBillId())
                .billingTime(bill.getBillingTime())
                .billType(getBillType().getCode())
                .billAmount(bill.getAmountWithTax())
                .creator(user.getUsername())
                .createTime(System.currentTimeMillis())
                .deleted(last == null ? 1 : 0)
                .billDetail(JSONUtil.toJsonStr(queryBill(BillReqDto.builder().billId(bill.getProductBillId()).billingTime(bill.getBillingTime()).build(), true)))
                .build();
        executeIgnore(() -> incomeBillHistoryMapper.insert(historyDO));
    }


    @Override
    public void payOffBill(BillReqDto reqDto) {

        List<PostpaidProductIncomeBillDO> postpaidProductBills = executeIgnore(() -> postpaidMapper.queryBillByIds(List.of(reqDto.getBillId()), reqDto.getBillingTime(), reqDto.getBillingTime()));
        if (CollectionUtils.isEmpty(postpaidProductBills)) {
            log.info("账单不存在，id:{}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        PostpaidProductIncomeBillDO postpaidProductBill = postpaidProductBills.getFirst();
        if (!InvoiceEnum.BillStatus.WAIT_PAYMENT.is(postpaidProductBill.getBillStatus()) && !InvoiceEnum.BillStatus.UNSETTLED.is(postpaidProductBill.getBillStatus())) {
            log.info("账单状态不允许支付，id:{},billStatus:{}", reqDto.getBillId(), postpaidProductBill.getBillStatus());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_PAY_OFF_NOT_MATCH);
        }
        rocketMQTemplate.convertAndSend(ChargeTopicUtils.getWalletDeductionNoticeTopic(), buildPayOffDto(postpaidProductBill));
    }


    /**
     * 查询账单详情
     *
     * @param detailDto 请求参数对象，包含账单详情ID和计费时间
     * @return 返回账单详情展示对象
     */
    //@Override
    public PostpaidIncomeProductBillDetailVO queryBill(BillReqDto detailDto, boolean queryCustomerInfo) {
        // 查询账单信息
        List<PostpaidProductIncomeBillDO> bills = TenantUtils.executeIgnore(
                () -> postpaidMapper.queryBillByIds(List.of(detailDto.getBillId()), detailDto.getBillingTime(), detailDto.getBillingTime())
        );
        if (CollectionUtils.isEmpty(bills)) {
            log.info("账单详情不存在,billId:{}", detailDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        // 将账单信息转换为展示对象
        PostpaidIncomeProductBillDetailVO showVO = PostpaidIncomeProductConvert.INSTANCE.convertDetail(bills.getFirst());

        handleDiscount(showVO, PostpaidIncomeProductBillDetailVO::getBillDiscountDetails, PostpaidIncomeProductBillDetailVO::setBillCoupons);
        handleDiscount(showVO, PostpaidIncomeProductBillDetailVO::getDiscountDetails, PostpaidIncomeProductBillDetailVO::setProductCoupons);
        // 查询服务账单信息
        List<PostpaidProductServiceIncomeBillDO> serviceIncomeBillDOS = executeIgnore(
                () -> postpaidIncomeProductServiceBillMapper.queryByProductBillIds(List.of(showVO.getProductBillId()),
                        detailDto.getBillingTime(), detailDto.getBillingTime())
        );
        // 判断服务账单信息是否非空
        if (CollectionUtils.isNotEmpty(serviceIncomeBillDOS)) {
            // 将服务账单信息转换为展示对象列表
            List<PostpaidIncomeServiceVO> services = serviceIncomeBillDOS.stream().map(PostpaidIncomeProductConvert.INSTANCE::convert).toList();
            // 处理账单列
            handBillColumn(List.of(showVO));
            // 设置服务列表到展示对象
            showVO.setServices(services);

            handleDiscount(services, PostpaidIncomeServiceVO::getDiscountDetails, PostpaidIncomeServiceVO::setCoupons);
            handleRateDetail(services, PostpaidIncomeServiceVO::getRateDetails, PostpaidIncomeServiceVO::getBillingType,
                    PostpaidIncomeServiceVO::getTaxRate
                    , PostpaidIncomeServiceVO::setFixedRateConfig, PostpaidIncomeServiceVO::setPackageRateConfig,
                    PostpaidIncomeServiceVO::setTierRateConfig, PostpaidIncomeServiceVO::setUsageBasedRateConfig);
            // 处理账单服务列
            handleBillServiceColumn(List.of(showVO));
        }
        if (queryCustomerInfo) {
            // 查询客户信息
            assign(showVO, PostpaidIncomeProductBillDetailVO::getCustomerId, customerApi::findById, showVO, PostpaidIncomeProductBillDetailVO::setCustomer);
            assign(showVO, PostpaidIncomeProductBillDetailVO::getEntityId, entityApi::findById, showVO, PostpaidIncomeProductBillDetailVO::setEntity);
        }
        processBillContentAndCoupons(showVO);
        return showVO;
    }

    private final BillContentHandler billContentHandler;
    private void processBillContentAndCoupons(PostpaidIncomeProductBillDetailVO showVO) {
        List<BillDiscountConfigDTO> billCoupons = showVO.getBillCoupons();
        List<BillDiscountConfigDTO> productCoupons = showVO.getProductCoupons();
        List<BillCoupon> coupons = new ArrayList<>(billContentHandler.handleCoupons(billCoupons, null, null, showVO.getCurrency()));
        coupons.addAll(billContentHandler.handleCoupons(productCoupons, showVO.getProductId(), null, showVO.getCurrency()));
        BillContent productContent = new BillContent();
        productContent.setDescription(StringUtils.isNotEmpty(showVO.getProductName())?showVO.getProductName():"");
        for (PostpaidIncomeServiceVO service : showVO.getServices()) {
            coupons.addAll(billContentHandler.handleCoupons(service.getCoupons(),Long.valueOf(service.getProductId()),service.getServiceId(),showVO.getCurrency()));
            billContentHandler.handleContent(productContent,service);
        }
        showVO.setShowCoupons(coupons);
        showVO.setShowContents(List.of(productContent));
    }

    @Override
    public void cancelInvoice(BillInvoiceAmountCancelDTO cancelDTO) {
        log.info("取消发票,{}", JSONUtil.toJsonStr(cancelDTO));
        // 查询 账单数据
        List<PostpaidProductIncomeBillDO> makeupBills = postpaidMapper.queryBillByIds(List.of(cancelDTO.getBillDetailId()), cancelDTO.getBillingTime(), cancelDTO.getBillingTime());
        if (CollectionUtils.isEmpty(makeupBills)) {
            log.info("账单详情不存在,invoiceId:{},billId:{}", cancelDTO.getInvoiceId(), cancelDTO.getBillDetailId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        PostpaidProductIncomeBillDO incomeBillDO = makeupBills.getFirst();
        incomeBillDO.setAvailableInvoiceAmount(cancelDTO.getInvoiceAmount().add(incomeBillDO.getInvoicedAmount()));
        incomeBillDO.setInvoicedAmount(incomeBillDO.getInvoicedAmount().subtract(cancelDTO.getInvoiceAmount()));
        postpaidMapper.updateById(List.of(incomeBillDO));
    }


    @Override
    public boolean refundInvoice(Long billId, Long billingTime, BigDecimal refundAmount) {
        List<PostpaidProductIncomeBillDO> makeupIncomeBillDOS = executeIgnore(() -> postpaidMapper.queryBillByIds(List.of(billId), billingTime, billingTime));
        if (CollectionUtils.isEmpty(makeupIncomeBillDOS)) {
            log.info("账单不存在,billId:{}", billId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        PostpaidProductIncomeBillDO billDO = makeupIncomeBillDOS.getFirst();
        BigDecimal refundInvoiceAmount = billDO.getRefundInvoiceAmount();
        BigDecimal finalRefundAmount = NumberUtil.add(refundInvoiceAmount, refundAmount);
        return executeIgnore(() -> postpaidMapper.refundInvoice(billId, billingTime, finalRefundAmount) > 0);
    }


    @Override
    public CustomerApi getCustomerApi() {
        return customerApi;
    }

    @Override
    public EntityApi getEntityApi() {
        return entityApi;
    }

    @Override
    public Logger getLogger() {
        return log;
    }


}
