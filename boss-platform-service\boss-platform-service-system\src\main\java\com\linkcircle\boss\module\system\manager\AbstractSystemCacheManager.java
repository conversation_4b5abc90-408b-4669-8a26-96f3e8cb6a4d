package com.linkcircle.boss.module.system.manager;

import cn.hutool.extra.spring.SpringUtil;
import com.linkcircle.boss.framework.common.util.cache.SystemCacheUtils;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.transation.TransactionUtils;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:55
 * @description 缓存操作管理类
 */
public abstract class AbstractSystemCacheManager {

    /**
     * 删除秘钥缓存
     */
    public void delSecretKeyCache(String appId) {
        TransactionUtils.doAfterTransaction(() -> {
            String secretKey = SystemCacheUtils.getSecretKey(appId);
            getRedissonUtil().delete(secretKey);
        });
    }

    private RedissonUtil getRedissonUtil() {
        return SpringUtil.getBean(RedissonUtil.class);
    }
}
