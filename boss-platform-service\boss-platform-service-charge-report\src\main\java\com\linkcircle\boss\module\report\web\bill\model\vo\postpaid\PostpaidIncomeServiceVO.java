package com.linkcircle.boss.module.report.web.bill.model.vo.postpaid;

import com.baomidou.mybatisplus.annotation.TableField;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.ProductServiceTieredPriceDTO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.*;
import com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30 14:14
 */
@Schema(description = "后付费 产品-服务详情")
@Data
public class PostpaidIncomeServiceVO {

    /**
     * 服务id
     */

    @Schema(description = "服务id")
    private Long serviceId;

    @Schema(description = "服务名称", example = "服务名称")
    private String serviceName;

    /**
     * 产品服务账单id
     */
    @Schema(description = "产品服务账单id")
    private Long productServiceBillId;

    /**
     * 出账时间戳
     */
    @Schema(description = "出账时间戳")
    private Long billingTime;


    /**
     * 产品账单id
     */
    @Schema(description = "产品账单id")
    private Long billId;

    /**
     * 主体id
     */
    @Schema(description = "主体id")
    private String entityId;

    @Schema(description = "主体名称", example = "客户名称")
    private String entityName;

    /**
     * 合同id
     */
    @Schema(description = "合同id")
    private String contractId;

    @Schema(description = "合同名称", example = "合同名称")
    private String contractName;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private String customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称", example = "客户名称")
    private String customerName;
    /**
     * 账户id
     */
    @Schema(description = "账户id")
    private String accountId;
    /**
     * 账户名称
     */
    @Schema(description = "账户名称", example = "账户名称")
    private String accountName;
    /**
     * 钱包id
     */
    @Schema(description = "钱包id")
    private String walletId;
    /**
     * 钱包名称
     */
    @Schema(description = "钱包名称", example = "钱包名称")
    private String walletName;
    /**
     * 计划id
     */
    @Schema(description = "计划id")
    private String planId;
    /**
     * 计划名称
     */
    @Schema(description = "计划名称", example = "计划名称")
    private String planName;
    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private String productId;
    @Schema(description = "产品名称", example = "产品名称")
    private String productName;
    /**
     * 支付方式 0-现金, 1-积分
     */
    @Schema(description = "支付方式 0-现金, 1-积分")
    private Integer paymentMethod;

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal unitPrice;

    /**
     * 消耗量
     */
    @Schema(description = "消耗量")
    private BigDecimal usageCount;

    /**
     * 消耗量单位
     */
    @Schema(description = "消耗量单位")
    private String usageUnit;

    /**
     * 税率
     */
    @Schema(description = "税率")
    private BigDecimal tax;

    /**
     * 税率百分比
     */
    @Schema(description = "税率百分比")
    private BigDecimal taxRate;

    /**
     * 目录价(原价)
     */
    @Schema(description = "目录价(原价)")
    private BigDecimal originalPrice;

    /**
     * 含税总金额
     */
    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */
    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 实际支付时间戳
     */
    @Schema(description = "实际支付时间戳")
    private Long paymentTime;

    /**
     * 数据创建时间戳
     */
    @Schema(description = "数据创建时间戳")
    private Long createTime;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Boolean deleted;





    @Schema(description = "固定费率配置")
    private FixedRateConfigFeeDTO fixedRateConfig;

    @Schema(description = "套餐计费配置")
    private PackageRateConfigFeeDTO packageRateConfig;

    @Schema(description = "阶梯费率配置")
    private TierRateConfigFeeDTO tierRateConfig;


    @Schema(description = "按量计费配置")
    private UsageBasedRateConfigFeeDTO usageBasedRateConfig;

    /**
     * 费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*
     */
    @Schema(description = "费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*")
    @TableField("rate_details")
    private String rateDetails;

    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>")
    @TableField("discount_details")
    private String discountDetails;

    /**
     * 计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费
     */
    @TableField("billing_type")
    @Schema(description = "计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer billingType;

    @Schema(description = "产品优惠配置列表")
    private List<BillDiscountConfigDTO> coupons;

    @Schema(description = "服务价格详情")
    private ChargeProductServicePriceVo servicePriceVo;
}
