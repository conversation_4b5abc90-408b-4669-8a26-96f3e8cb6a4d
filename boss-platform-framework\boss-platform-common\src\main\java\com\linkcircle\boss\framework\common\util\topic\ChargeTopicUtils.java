package com.linkcircle.boss.framework.common.util.topic;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;

/**
 * <AUTHOR>
 * @date 2025-06-17 14:09
 * @description
 */
public class ChargeTopicUtils {

    /**
     * 接收收入话单主题
     */
    public static String getChargeReceiveIncomeBill() {
        return ChargeTopicConstant.CHARGE_NORMAL_TOPIC + ":" + ChargeTopicConstant.TAG_RECEIVE_INCOME_BILL;
    }

    /**
     * 接收成本话单主题
     */
    public static String getChargeReceiveCostBill() {
        return ChargeTopicConstant.CHARGE_NORMAL_TOPIC + ":" + ChargeTopicConstant.TAG_RECEIVE_COST_BILL;
    }

    /**
     * 计费完成发送给收费系统 预付费-钱包扣款通知
     */
    public static String getWalletDeductionNoticeTopic() {
        return ChargeTopicConstant.CHARGE_NORMAL_TOPIC + ":" + ChargeTopicConstant.TAG_WALLET_DEDUCTION_NOTICE;
    }

    /**
     * 收入 按服务出账 普通消息
     */
    public static String getIncomeServiceBillingMessageNormalTopic() {
        return ChargeTopicConstant.CHARGE_NORMAL_TOPIC + ":" + ChargeTopicConstant.TAG_INCOME_BILL_SERVICE_OUT;
    }

    /**
     * 陈本 按服务出账
     */
    public static String getCostServiceBillingMessageNormalTopic() {
        return ChargeTopicConstant.CHARGE_NORMAL_TOPIC + ":" + ChargeTopicConstant.TAG_COST_BILL_SERVICE_OUT;
    }

    /**
     * 接收- 下载任务-财务明细 主题
     */
    public static String getDownloadTaskFinanceDetailTopic() {
        return ChargeTopicConstant.CHARGE_NORMAL_TOPIC + ":" + ChargeTopicConstant.TAG_DOWNLOAD_TASK_FINANCE_DETAIL;
    }

}
