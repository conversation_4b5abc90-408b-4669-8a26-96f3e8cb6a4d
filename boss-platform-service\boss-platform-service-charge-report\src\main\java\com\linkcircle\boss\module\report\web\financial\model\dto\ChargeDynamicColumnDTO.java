package com.linkcircle.boss.module.report.web.financial.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/14 15:07
 */
@Data
public class ChargeDynamicColumnDTO {

    @Schema(description = "列名")
    protected String columnName;

    @Schema(description = "值")
    private Object value;

    @Schema(description = "最小值")
    private Object minValue;

    @Schema(description = "最大值")
    private Object maxValue;

    @Schema(description = "范围类型 0:单个值,1:区间")
    private Integer rangeType;
}
