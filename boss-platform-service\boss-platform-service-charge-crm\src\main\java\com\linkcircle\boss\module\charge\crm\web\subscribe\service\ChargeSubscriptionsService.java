package com.linkcircle.boss.module.charge.crm.web.subscribe.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto.ChargeSubscriptionsCreateReqDTO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto.ChargeSubscriptionsPageReqDTO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto.ChargeSubscriptionsUpdateReqDTO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.entity.ChargeSubscriptionsDO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo.ChargeSubscriptionsDetailVO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo.ChargeSubscriptionsPageVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;

import java.util.List;

/**
 * 订阅管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ChargeSubscriptionsService extends IService<ChargeSubscriptionsDO> {

    PageResult<ChargeSubscriptionsPageVO> page(ChargeSubscriptionsPageReqDTO dto);

    ChargeSubscriptionsDetailVO detail(Long id);

    Long create(ChargeSubscriptionsCreateReqDTO dto);

    boolean update(ChargeSubscriptionsUpdateReqDTO dto);

    boolean changeStatus(Long id, Integer status);

    boolean delete(Long id);

    List<AccountSubscriptionsVO> getAccountSubscriptionsList(Long accountId, Integer paymentType);

    AccountSubscriptionsVO getSubscriptionDetail(Long subscriptionId);

    List<Long> getAllSubscriptionAccountIds(Integer paymentType);

    List<AccountSubscriptionsVO> getSubscriptionsListByBillingType(Integer billingType);

    List<Coupon> getSubscriptionCouponList(Long subscriptionId, Long serviceId);
}