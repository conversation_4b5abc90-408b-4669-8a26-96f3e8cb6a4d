NACOS_SERVER: @server-addr@
NACOS_USERNAME: @username@
NACOS_PASSWORD: @password@
NACOS_NAMESPACE: @namespace@
NACOS_GROUP: @group@
spring:
  lifecycle:
    timeout-per-shutdown-phase: 30s
  servlet:
    multipart:
      max-file-size: 10MB
  application:
    name: @project.artifactId@

  config:
    import:
      - optional:nacos:${spring.application.name}.yaml

  cloud:
    nacos:
      server-addr: ${NACOS_SERVER}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      config:
        file-extension: yaml
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_GROUP}
        enabled: true
      discovery:
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_GROUP}
        namingLoadCacheAtStart: true
        metadata:
          context-path: ${server.servlet.context-path}
          management-context-path: ${server.servlet.context-path}/actuator
        failure-tolerance-enabled: true
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  threads:
    virtual:
      enabled: true

management:
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      probes:
        enabled: true
      show-details: ALWAYS
    sentinel:
      enabled: false
    shutdown:
      access: unrestricted
  info:
    git:
      mode: full
  endpoints:
    web:
      exposure:
        include: "*"
        exclude:
          - nacosconfig
          - nacosdiscovery
          - beans

logging:
  file:
    name: /home/<USER>/logs/${spring.application.name}/${spring.application.name}-info.log
  structured:
    format:
      console: ecs
      file: ecs