<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.linkcircle.boss</groupId>
    <artifactId>boss-platform</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <modules>
        <module>boss-platform-dependencies</module>
        <module>boss-platform-framework</module>
        <!--  各种 module 拓展 -->
        <module>boss-platform-api</module>
        <module>boss-platform-service</module>
    </modules>

    <name>${project.artifactId}</name>
    <description>BOSS平台</description>

    <properties>
        <revision>3.0.0</revision>
        <!-- Maven 相关 -->
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <!-- 看看咋放到 bom 里 -->
        <lombok.version>1.18.36</lombok.version>
        <spring.boot.version>3.5.4</spring.boot.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.linkcircle.boss</groupId>
                <artifactId>boss-platform-dependencies</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- maven-surefire-plugin 插件，用于运行单元测试。 -->
                <!-- 注意，需要使用 3.0.X+，因为要支持 Junit 5 版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <!-- maven-compiler-plugin 插件，解决 Lombok + MapStruct 组合 -->
                <!-- https://stackoverflow.com/questions/33483697/re-run-spring-boot-configuration-annotation-processor-to-update-generated-metada -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring.boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                        <!-- 编译参数写在 arg 内，解决 Spring Boot 3.2 的 Parameter Name Discovery 问题 -->
                        <debug>false</debug>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>

                <!-- 复制，删除，打包，重命名文件等... -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>1.8</version>
                    <executions>
                        <execution>
                            <!-- maven生命周期阶段 -->
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <!-- 任务 -->
                                <tasks>
                                    <!-- 复制jar包到指定目录 -->
                                    <copy overwrite="true"
                                          tofile="../../docker/${project.build.finalName}.jar"
                                          file="${project.build.directory}/${project.build.finalName}.jar"/>
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

                <!-- Git提交信息插件 -->
                <plugin>
                    <groupId>io.github.git-commit-id</groupId>
                    <artifactId>git-commit-id-maven-plugin</artifactId>
                    <version>7.0.0</version>
                    <executions>
                        <execution>
                            <id>get-the-git-infos</id>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                            <phase>initialize</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <generateGitPropertiesFile>true</generateGitPropertiesFile>
                        <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties
                        </generateGitPropertiesFilename>
                        <includeOnlyProperties>
                            <!-- Git 分支 -->
                            <includeOnlyProperty>^git.branch$</includeOnlyProperty>
                            <!-- 构建相关信息 -->
                            <includeOnlyProperty>^git.build.host$</includeOnlyProperty>
                            <includeOnlyProperty>^git.build.time$</includeOnlyProperty>
                            <includeOnlyProperty>^git.build.user.email$</includeOnlyProperty>
                            <includeOnlyProperty>^git.build.user.name$</includeOnlyProperty>
                            <includeOnlyProperty>^git.build.version$</includeOnlyProperty>
                            <!-- 最近标签相关 -->
                            <includeOnlyProperty>^git.closest.tag.name$</includeOnlyProperty>
                            <includeOnlyProperty>^git.closest.tag.commit.count$</includeOnlyProperty>
                            <!-- 提交信息 -->
                            <includeOnlyProperty>^git.commit.id$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.id.abbrev$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.id.describe$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.id.describe-short$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.message.full$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.message.short$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.time$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.user.email$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.user.name$</includeOnlyProperty>
                            <!-- 其他信息 -->
                            <includeOnlyProperty>^git.dirty$</includeOnlyProperty>
                            <includeOnlyProperty>^git.local.branch$</includeOnlyProperty>
                            <includeOnlyProperty>^git.remote.origin.fetch$</includeOnlyProperty>
                            <includeOnlyProperty>^git.remote.origin.url$</includeOnlyProperty>
                            <includeOnlyProperty>^git.total.commit.count$</includeOnlyProperty>
                        </includeOnlyProperties>
                        <commitIdGenerationMode>full</commitIdGenerationMode>
                        <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                        <failOnNoGitDirectory>false</failOnNoGitDirectory>
                        <verbose>false</verbose>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>oss</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <!--需要配置 不然profiles 无法找到需要替换哪些配置文件-->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.json</include>
                    <include>**/*.properties</include>
                    <include>**/*.png</include>
                    <include>**/*.csv</include>
                    <include>**/*.ftl</include>
                    <include>**/*.css</include>
                    <include>META-INF/services/**</include>
                </includes>
            </resource>
        </resources>
    </build>

    <!-- 使用 huawei / aliyun 的 Maven 源，提升下载速度 -->
    <repositories>
        <repository>
            <id>huaweicloud</id>
            <name>huawei</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>

        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>


    <!-- 环境 -->
    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!--当前环境-->
                <profile.name>dev</profile.name>
                <!--Nacos地址-->
                <server-addr>10.255.1.8:8848</server-addr>
                <!--命名空间称-->
                <namespace>boss</namespace>
                <!--分组名称-->
                <group>boss</group>
                <username>cloud</username>
                <password>cloud@1234</password>
            </properties>
        </profile>

        <profile>
            <id>local</id>
            <properties>
                <!--当前环境-->
                <profile.name>local</profile.name>
                <!--Nacos地址-->
                <server-addr>127.0.0.1:8848</server-addr>
                <!--命名空间称-->
                <namespace>boss</namespace>
                <!--分组名称-->
                <group>boss</group>
                <username>nacos</username>
                <password>nacos</password>
            </properties>
        </profile>
    </profiles>
</project>
