package com.linkcircle.boss.module.charge.fee.web.bill.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "账单优惠")
public class BillCoupon {
    @Schema(description = "描述")
    private String description;

    @Schema(description = "总价")
    private String discountPrice;

    @Schema(description = "总金额")
    private String totalAmount;

    public BillCoupon() {
        this.description = "";
        this.discountPrice = "";
        this.totalAmount = "";
    }
}