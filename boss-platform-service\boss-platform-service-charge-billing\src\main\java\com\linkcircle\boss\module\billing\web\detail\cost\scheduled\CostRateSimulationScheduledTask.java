package com.linkcircle.boss.module.billing.web.detail.cost.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.data.service.ResourcePurchaseDataService;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.CostBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.ResourceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.model.vo.CostBillDetailResponseVO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.CostBillDetailService;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.service.CostRateUsageManageService;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-10 15:24
 * @description 成本固定费率, 阶梯费率 根据间隔时长周期 定时模拟发送详单到mq
 * 1. 查询有固定费率, 阶梯费率的采购 @ResourcePurchaseDataService#getPurchasesListByBillingType
 * 1.1 要判断采购是否生效..
 * 2. 计算间隔时长 晚1小时 是否到期 @CyclePeriodCalculateService#calculateCyclePeriod
 * 3. 发送详单消息到mq @CostBillDetailService#createCostBillDetail
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CostRateSimulationScheduledTask {

    private final ResourcePurchaseDataService resourcePurchaseDataService;
    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final CostBillDetailService costBillDetailService;
    private final CostRateUsageManageService costRateUsageManageService;
    private final RedissonUtil redissonUtil;

    /**
     * 固定费率模拟详单发送定时任务
     * 每小时执行一次
     */
    @XxlJob("costFixedRateSimulationHandler")
    @XxlJobRegister(
            cron = "0 25 * * * ?",
            jobDesc = "成本-固定费率-模拟账单发送",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.LEAST_FREQUENTLY_USED,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void costFixedRateSimulationHandler() {
        executeRateSimulation(ChargeRateTypeEnum.FIXED);
    }

    /**
     * 阶梯费率模拟详单发送定时任务
     * 每小时执行一次
     */
    @XxlJob("costTieredRateSimulationHandler")
    @XxlJobRegister(
            cron = "0 35 * * * ?",
            jobDesc = "成本-阶梯费率-模拟账单发送",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.LEAST_FREQUENTLY_USED,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void costTieredRateSimulationHandler() {
        executeRateSimulation(ChargeRateTypeEnum.TIERED);
    }

    /**
     * 执行费率模拟详单发送
     *
     * @param rateType 费率类型
     */
    private void executeRateSimulation(ChargeRateTypeEnum rateType) {
        String rateTypeName = rateType.getName();
        String traceId = IdUtil.fastSimpleUUID();
        TraceIdUtil.buildAndSetTraceId(" ", rateTypeName, traceId);

        try {
            log.info("开始执行{}模拟详单发送定时任务", rateTypeName);

            // 1. 查询有指定费率类型的采购
            List<ResourcePurchaseVO> purchases = resourcePurchaseDataService.getPurchasesListByBillingType(rateType.getType());
            if (purchases.isEmpty()) {
                log.info("未找到{}类型的采购，跳过执行", rateTypeName);
                return;
            }

            log.info("找到{}个{}类型的采购", purchases.size(), rateTypeName);

            int successCount = 0;
            int failureCount = 0;

            for (ResourcePurchaseVO purchase : purchases) {
                Long purchaseId = purchase.getId();
                try {
                    // 1. 判断采购是否生效
                    if (!isPurchaseActive(purchase)) {
                        log.info("采购{}未生效，跳过处理", purchaseId);
                        continue;
                    }

                    // 2. 根据rateType过滤出匹配的采购服务
                    List<ResourceSubscriptionInfoDTO> matchedServices = filterServicesByRateType(purchase, rateType);
                    if (matchedServices.isEmpty()) {
                        log.info("采购{}未找到匹配的{}服务", purchaseId, rateTypeName);
                        continue;
                    }

                    // 3. 遍历匹配的服务，进行间隔时长判断和详单发送
                    for (ResourceSubscriptionInfoDTO serviceInfo : matchedServices) {
                        try {
                            boolean success = processServiceBillDetail(purchase, serviceInfo, rateType);
                            if (success) {
                                successCount++;
                            }
                        } catch (Exception e) {
                            failureCount++;
                            ResourcePurchaseVO.Detail detail = serviceInfo.getDetail();
                            log.error("处理采购{}服务{}时发生异常", purchaseId, detail.getResourceServiceId(), e);
                        }
                    }

                } catch (Exception e) {
                    failureCount++;
                    log.error("处理采购{}时发生异常", purchase.getId(), e);
                }
            }

            log.info("成本{}模拟详单发送定时任务执行完成，成功: {}, 失败: {}", rateTypeName, successCount, failureCount);

        } catch (Exception e) {
            log.error("成本{}模拟详单发送定时任务执行异常", rateTypeName, e);
        } finally {
            TraceIdUtil.remove();
        }
    }

    /**
     * 根据费率类型过滤出匹配的采购服务
     *
     * @param purchase 采购信息
     * @param rateType 费率类型
     * @return 匹配的服务列表，包含详情信息
     */
    private List<ResourceSubscriptionInfoDTO> filterServicesByRateType(ResourcePurchaseVO purchase, ChargeRateTypeEnum rateType) {
        List<ResourceSubscriptionInfoDTO> matchedServices = new ArrayList<>();

        // 1. 检查 details
        List<ResourcePurchaseVO.Detail> details = purchase.getDetails();
        if (CollUtil.isEmpty(details)) {
            return matchedServices;
        }

        // 2. 遍历每个 detail，过滤匹配的服务
        for (ResourcePurchaseVO.Detail detail : details) {
            if (Objects.equals(detail.getChargeType(), rateType.getType())) {
                ResourceSubscriptionInfoDTO serviceInfo = ResourceSubscriptionInfoDTO.builder()
                        .purchaseVO(purchase)
                        .detail(detail)
                        .resourceId(purchase.getResourceId())
                        .build();
                matchedServices.add(serviceInfo);
            }
        }

        return matchedServices;
    }

    /**
     * 判断采购是否生效
     *
     * @param purchase 采购信息
     * @return true-生效，false-未生效
     */
    private boolean isPurchaseActive(ResourcePurchaseVO purchase) {
        // 检查状态是否为已生效(1)或试用中(2)
        return purchase.hasValidPurchase();
    }

    /**
     * 判断服务是否应该发送详单（根据间隔时长周期的开始时间，晚1小时）
     *
     * @param serviceInfo 服务采购信息
     * @return 周期计算结果
     */
    private Optional<CyclePeriodResultVO> shouldSendBillDetailForService(ResourceSubscriptionInfoDTO serviceInfo) {
        try {
            ResourcePurchaseVO.Detail detail = serviceInfo.getDetail();
            ResourcePurchaseVO purchase = serviceInfo.getPurchaseVO();

            // 获取服务的间隔时长配置
            Integer period = detail.getPeriod();
            Integer unitPeriod = detail.getUnitPeriod();
            Long startTime = purchase.getStartTime();
            String timezone = "UTC";

            long businessTime = System.currentTimeMillis();

            // 计算周期
            CyclePeriodResultVO cyclePeriodResultVO = cyclePeriodCalculateService.calculateCyclePeriod(
                    unitPeriod, timezone, businessTime, startTime, period);
            if (Objects.isNull(cyclePeriodResultVO) || !cyclePeriodResultVO.isSuccess()) {
                return Optional.empty();
            }
            return Optional.of(cyclePeriodResultVO);
        } catch (Exception e) {
            log.error("计算服务{}间隔时长时发生异常", serviceInfo.getDetail().getResourceServiceId(), e);
        }
        return Optional.empty();
    }

    /**
     * 构造服务级别的模拟详单请求
     *
     * @param purchase            采购信息
     * @param serviceInfo         服务采购信息
     * @param cyclePeriodResultVO 周期计算
     * @return 详单请求DTO
     */
    private CostBillDetailRequestDTO buildSimulationRequestForService(ResourcePurchaseVO purchase,
                                                                      ResourceSubscriptionInfoDTO serviceInfo,
                                                                      CyclePeriodResultVO cyclePeriodResultVO) {
        CostBillDetailRequestDTO requestDTO = new CostBillDetailRequestDTO();

        // 获取服务详情信息
        ResourcePurchaseVO.Detail detail = serviceInfo.getDetail();

        requestDTO.setAccountId(purchase.getAccountId());
        requestDTO.setBusinessId(IdUtil.fastSimpleUUID());
        requestDTO.setBusinessTime(cyclePeriodResultVO.getCycleStartTime());
        requestDTO.setTenantId(purchase.getTenantId());
        requestDTO.setResourceId(purchase.getResourceId());
        requestDTO.setResourceServiceId(detail.getResourceServiceId());
        requestDTO.setTimestamp(System.currentTimeMillis());

        // 模拟使用量（使用count和unitLabel）
        Integer purchaseCount = detail.getCount();
        String unitLabel = detail.getUnitLabel();

        // 如果没有配置count，则不处理该服务
        if (purchaseCount == null || purchaseCount <= 0) {
            log.info("服务{}未配置count或配置值无效，跳过处理", detail.getResourceServiceId());
            return null;
        }

        requestDTO.setUsage(BigDecimal.valueOf(purchaseCount));
        requestDTO.setUsageUnit(unitLabel);
        return requestDTO;
    }

    /**
     * 处理单个服务的详单生成
     *
     * @param purchase    采购信息
     * @param serviceInfo 服务信息
     * @param rateType    费率类型
     * @return true-处理成功，false-处理失败或跳过
     */
    private boolean processServiceBillDetail(ResourcePurchaseVO purchase,
                                             ResourceSubscriptionInfoDTO serviceInfo,
                                             ChargeRateTypeEnum rateType) {
        ResourcePurchaseVO.Detail detail = serviceInfo.getDetail();
        Long purchaseId = purchase.getId();
        Long serviceId = detail.getResourceServiceId();

        // 1. 在服务级别进行间隔时长判断
        Optional<CyclePeriodResultVO> cyclePeriodResultOpt = shouldSendBillDetailForService(serviceInfo);
        if (cyclePeriodResultOpt.isEmpty()) {
            log.info("采购{}服务{} 未到周期计算失败，跳过处理", purchaseId, serviceId);
            return false;
        }

        CyclePeriodResultVO cyclePeriodResultVO = cyclePeriodResultOpt.get();
        cyclePeriodResultVO.setRateTypeEnum(rateType);
        cyclePeriodResultVO.setSubscriptionId(purchaseId);
        cyclePeriodResultVO.setServiceId(serviceId);

        // 2. 检查是否已计费
        boolean billed = checkBilled(cyclePeriodResultVO);
        if (billed) {
            log.info("采购{}服务{} 已生成详单，跳过处理", purchaseId, serviceId);
            return false;
        }

        // 3. 构造发送MQ的对象
        CostBillDetailRequestDTO requestDTO = buildSimulationRequestForService(purchase, serviceInfo, cyclePeriodResultVO);
        if (requestDTO == null) {
            log.info("采购{}服务{}构造详单请求失败，跳过处理", purchaseId, serviceId);
            return false;
        }

        // 4. 调用接口发送详单
        CostBillDetailResponseVO responseVO = costBillDetailService.createCostBillDetail(requestDTO);
        log.info("采购{}服务{}模拟详单发送成功 response: {}",
                purchaseId, serviceId, JsonUtils.toJsonString(responseVO));
        return true;
    }

    /**
     * 检测是否已计费过
     * 优化流程：
     * 1. 先查询Redis SETNX，使用周期差作为有效期
     * 2. 如果SETNX不成功，说明已计费过，直接返回true
     * 3. 如果SETNX成功，再查询数据库确认是否已计费
     * 4. 如果数据库中已计费，删除Redis锁并返回true
     */
    private boolean checkBilled(CyclePeriodResultVO cyclePeriodResultVO) {
        ChargeRateTypeEnum rateType = cyclePeriodResultVO.getRateTypeEnum();
        String billingCycle = cyclePeriodResultVO.getBillingCycle();
        Long purchaseId = cyclePeriodResultVO.getSubscriptionId();
        Long serviceId = cyclePeriodResultVO.getServiceId();
        String currency = cyclePeriodResultVO.getCurrency();

        if (ChargeRateTypeEnum.FIXED.equals(rateType)) {
            return costRateUsageManageService.isFixedRateBilled(purchaseId, serviceId, billingCycle, currency);
        }
        if (ChargeRateTypeEnum.TIERED.equals(rateType)) {
            return costRateUsageManageService.isTieredRateBilled(purchaseId, serviceId, billingCycle, currency);
        }
        return false;
    }
}
