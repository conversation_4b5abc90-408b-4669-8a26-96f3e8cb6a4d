package com.linkcircle.boss.module.billing.web.bill.product.scheduled.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.enums.BillingProcessStatusEnum;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.product.service.IncomePostpaidProductBillService;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.product.service.IncomePostpaidProductBillingService;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidBillingService;
import com.linkcircle.boss.module.billing.web.data.model.vo.BillingCycleResultVO;
import com.linkcircle.boss.module.billing.web.data.service.BillingCycleCalculateService;
import com.linkcircle.boss.module.billing.web.data.service.CustomerAccountDataService;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.billing.web.manager.BillIdManager;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.BillStatusEnum;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-08 11:45
 * @description 后付费产品出账服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomePostpaidProductBillingServiceImpl implements IncomePostpaidProductBillingService {

    private final SubscriptionDataService subscriptionDataService;
    private final CustomerAccountDataService customerAccountDataService;
    private final BillingCycleCalculateService billingCycleCalculateService;
    private final IncomePostpaidProductBillService productBillService;
    private final IncomePostpaidBillingService incomePostpaidBillingService;
    private final BillIdManager billIdManager;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void processAccountProductBilling(Long accountId) {
        log.info("开始处理账户产品出账, accountId: {}", accountId);

        Optional<CustomerAccountVO> accountInfoOpt = customerAccountDataService.getCustomerAccountInfo(accountId);
        if (accountInfoOpt.isEmpty()) {
            log.warn("未找到账户信息, accountId: {}", accountId);
            return;
        }
        // 0. 检查账户是否到达出账日
        CustomerAccountVO customerAccountVO = accountInfoOpt.get();
        BillingCycleResultVO billingCycleResult = billingCycleCalculateService.calculateBillingCycle(customerAccountVO);

        if (!billingCycleResult.getAllowBilling()) {
            log.info("账户未到达出账日，跳过产品出账, accountId: {}", accountId);
            return;
        }

        // 1. 获取账户订阅信息
        Optional<List<AccountSubscriptionsVO>> subscriptionsOpt = subscriptionDataService.getAccountSubscriptions(accountId, PaymentTypeEnum.POSTPAID);
        if (subscriptionsOpt.isEmpty() || CollUtil.isEmpty(subscriptionsOpt.get())) {
            log.info("账户无有效订阅，跳过产品出账, accountId: {}", accountId);
            return;
        }

        List<AccountSubscriptionsVO> subscriptions = subscriptionsOpt.get();

        // 2. 按产品分组处理
        for (AccountSubscriptionsVO subscription : subscriptions) {
            if (CollUtil.isEmpty(subscription.getDetails())) {
                continue;
            }

            for (AccountSubscriptionsVO.Detail detail : subscription.getDetails()) {
                if (CollUtil.isEmpty(detail.getProducts())) {
                    continue;
                }

                for (AccountSubscriptionsVO.Product product : detail.getProducts()) {
                    processProductBilling(subscription, detail, product, customerAccountVO, billingCycleResult);
                }
            }
        }

        log.info("账户产品出账处理完成, accountId: {}", accountId);
    }

    public void processProductBilling(AccountSubscriptionsVO subscription,
                                      AccountSubscriptionsVO.Detail detail,
                                      AccountSubscriptionsVO.Product product,
                                      CustomerAccountVO customerAccountVO,
                                      BillingCycleResultVO billingCycleResult) {
        Long subscriptionId = subscription.getId();
        Long productId = product.getProductId();
        Long accountId = subscription.getAccountId();
        Long billingStartTime = billingCycleResult.getBillingStartTime();
        Long billingEndTime = billingCycleResult.getBillingEndTime();
        log.info("开始处理产品出账, subscriptionId: {}, productId: {}, accountId: {}",
                subscriptionId, productId, accountId);

        if (CollUtil.isEmpty(product.getServices())) {
            log.warn("产品无服务数据，跳过, productId: {}", productId);
            return;
        }

        // 0. 检查产品账单是否已存在
        if (productBillService.isProductBillExists(productId, subscriptionId, billingStartTime, billingEndTime)) {
            log.info("产品账单已存在，跳过创建, productId: {}, subscriptionId: {}", productId, subscriptionId);
            return;
        }

        // 1. 检查服务出账状态
        List<Long> serviceIds = product.getServices().stream()
                .map(AccountSubscriptionsVO.Service::getServiceId)
                .collect(Collectors.toList());

        List<PostpaidProductServiceIncomeBillDO> serviceBills = productBillService.getServiceBillsByIds(
                serviceIds, subscriptionId, productId, billingStartTime, billingEndTime);
        if (CollUtil.isEmpty(serviceBills)) {
            return;
        }
        if (serviceBills.size() != serviceIds.size()) {
            log.warn("服务账单数量与服务数量不一致, 服务数量: {}, 账单数量: {}",
                    serviceIds.size(), serviceBills.size());
        }

        // 2. 检查是否所有服务都已出账完成
        Map<Long, PostpaidProductServiceIncomeBillDO> serviceBillMap = serviceBills.stream()
                .collect(Collectors.toMap(PostpaidProductServiceIncomeBillDO::getServiceId, bill -> bill));

        boolean allServicesCompleted = true;
        for (AccountSubscriptionsVO.Service service : product.getServices()) {
            PostpaidProductServiceIncomeBillDO serviceBill = serviceBillMap.get(service.getServiceId());
            if (serviceBill == null) {
                // 服务账单不存在，触发服务出账
                log.info("服务账单不存在，触发服务出账, serviceId: {}", service.getServiceId());
                triggerServiceBilling(subscription, detail, product, service, customerAccountVO);
                allServicesCompleted = false;
                continue;
            }
            if (!BillingProcessStatusEnum.COMPLETED.getStatus().equals(serviceBill.getBillingStatus())) {
                log.info("服务出账未完成, serviceId: {}, status: {}", service.getServiceId(), serviceBill.getBillingStatus());
                allServicesCompleted = false;
            }
        }

        if (!allServicesCompleted) {
            log.info("产品下服务未全部出账完成，跳过产品出账, productId: {}", productId);
            return;
        }

        // 4. 创建产品账单
        createProductBill(subscription, product, serviceBills, billingCycleResult);
    }

    private void triggerServiceBilling(AccountSubscriptionsVO subscription,
                                       AccountSubscriptionsVO.Detail detail,
                                       AccountSubscriptionsVO.Product product,
                                       AccountSubscriptionsVO.Service service,
                                       CustomerAccountVO customerAccountVO) {
        try {
            log.info("触发服务出账, serviceId: {}", service.getServiceId());
            incomePostpaidBillingService.processServiceBilling(subscription, detail, product, service, customerAccountVO);
        } catch (Exception e) {
            log.error("触发服务出账失败, serviceId: {}", service.getServiceId(), e);
        }
    }

    public void createProductBill(AccountSubscriptionsVO subscription,
                                  AccountSubscriptionsVO.Product product,
                                  List<PostpaidProductServiceIncomeBillDO> serviceBills,
                                  BillingCycleResultVO billingCycleResult) {
        Long productBillId = billIdManager.createBillIdLong(BillTypeEnum.INCOME_PRODUCT, billingCycleResult.getBillingEndTime());

        PostpaidProductIncomeBillDO productBill = buildProductBill(subscription, product, serviceBills,
                productBillId, billingCycleResult);

        transactionTemplate.executeWithoutResult((action) -> {
            productBillService.saveProductBill(productBill);

            // 3. 更新服务账单的产品账单ID
            productBillService.updateServiceBillProductId(serviceBills, productBillId);

            log.info("产品账单创建成功, productBillId: {}, productId: {}, serviceCount: {}",
                    productBillId, product.getProductId(), serviceBills.size());
        });

    }

    private static PostpaidProductIncomeBillDO buildProductBill(AccountSubscriptionsVO subscription,
                                                                AccountSubscriptionsVO.Product product,
                                                                List<PostpaidProductServiceIncomeBillDO> serviceBills,
                                                                Long productBillId,
                                                                BillingCycleResultVO billingCycleResult) {
        // 1. 统计服务账单数据
        BigDecimal totalOriginalPrice = BigDecimal.ZERO;
        BigDecimal totalDiscountedPrice = BigDecimal.ZERO;
        BigDecimal totalAmountWithTax = BigDecimal.ZERO;
        BigDecimal totalAmountWithoutTax = BigDecimal.ZERO;

        for (PostpaidProductServiceIncomeBillDO bill : serviceBills) {
            if (bill.getOriginalPrice() != null) {
                totalOriginalPrice = totalOriginalPrice.add(bill.getOriginalPrice());
            }
            if (bill.getDiscountedPrice() != null) {
                totalDiscountedPrice = totalDiscountedPrice.add(bill.getDiscountedPrice());
            }
            if (bill.getAmountWithTax() != null) {
                totalAmountWithTax = totalAmountWithTax.add(bill.getAmountWithTax());
            }
            if (bill.getAmountWithoutTax() != null) {
                totalAmountWithoutTax = totalAmountWithoutTax.add(bill.getAmountWithoutTax());
            }
        }

        // 2. 创建产品账单
        return PostpaidProductIncomeBillDO.builder()
                .productBillId(productBillId)
                .customerId(subscription.getCustomerId())
                .accountId(subscription.getAccountId())
                .productId(product.getProductId())
                .entityId(subscription.getEntityId())
                .contractId(subscription.getContractId())
                .subscribeId(subscription.getId())
                .paymentMethod(product.getServices().getFirst().getPaymentOptions())
                .billStatus(BillStatusEnum.DRAFT.getStatus())
                .billingStatus(BillingProcessStatusEnum.COMPLETED.getStatus())
                .taxRate(subscription.getRate())
                .originalPrice(totalOriginalPrice)
                .discountedPrice(totalDiscountedPrice)
                .amountWithTax(totalAmountWithTax)
                .amountWithoutTax(totalAmountWithoutTax)
                .unpaidAmount(totalAmountWithTax)
                .currency(serviceBills.getFirst().getCurrency())
                .invoicedAmount(BigDecimal.ZERO)
                .paidAmount(BigDecimal.ZERO)
                .walletId(subscription.getWalletsId())
                .billingTime(System.currentTimeMillis())
                .billingStartTime(billingCycleResult.getBillingStartTime())
                .billingEndTime(billingCycleResult.getBillingStartTime())
                .billingCycleType(billingCycleResult.getBillingCycleType())
                .billingCycle(billingCycleResult.getBillingCycle())
                .billingDay(billingCycleResult.getBillingDay())
                .timezone(billingCycleResult.getTimezone())
                .createTime(System.currentTimeMillis())
                .deleted(false)
                .build();
    }

}