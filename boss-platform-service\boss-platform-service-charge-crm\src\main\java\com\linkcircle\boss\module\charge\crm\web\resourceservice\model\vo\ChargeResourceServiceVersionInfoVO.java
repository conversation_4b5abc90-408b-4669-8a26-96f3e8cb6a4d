package com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 资源服务基本信息版本信息返回返回
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@Schema(description = "资源服务基本信息分页返回")
public class ChargeResourceServiceVersionInfoVO implements Serializable {
    @Schema(description = "版本id")
    private Long versionId;

    @Schema(description = "资源服务id")
    private Long resourceServiceId;

    @Schema(description = "服务名称")
    @ExcelProperty("服务名称")
    private String serviceName;

    @Schema(description = "服务code")
    private String serviceCode;

    @Schema(description = "发票显示名称")
    private String invoiceShows;

    @Schema(description = "0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费")
    @ExcelProperty(value = "计费类型")
    private Integer chargeType;

    @Schema(description = "状态，0：未激活，1：激活,2:存档")
    private Integer status;

    @Schema(description = "量表表结构id")
    private Long scaleId;

    @Schema(description = "事件名称")
    private String eventName;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "描述")
    @TableField("description")
    private String description;

    @Schema(description = "版本名称（如v1.0）")
    private String versionName;

    @Schema(description = "版本号对比与排序使用")
    private Integer versionCode;


    @Schema(description = "单位标签")
    private String unitLabel;

    @Schema(description = "支付方式，0:现金，1：积分")
    private Integer paymentOptions;

    @Schema(description = "货币id集合 ，分隔")
    private String currencyCollection;

    @Schema(description = "价格配置json")
    private String currencyPriceJson;

    @Schema(description = "是否含套餐外,0:不包含，1：包含")
    private Integer inPackage;


    @Schema(description = "间隔时长")
    private Integer period;

    @Schema(description = "间隔时长单位 0：一次性, 1:日，2：周，3：月，4：季度，5：年")
    private Integer unitPeriod;

    @Schema(description = "租户编号")
    private Long tenantId;


}
