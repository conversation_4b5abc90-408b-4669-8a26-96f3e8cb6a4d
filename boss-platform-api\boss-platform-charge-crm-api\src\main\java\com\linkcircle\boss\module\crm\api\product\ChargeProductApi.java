package com.linkcircle.boss.module.crm.api.product;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.CommonApi;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:31
 */
@FeignClient(name = ApiConstants.NAME,path = ApiConstants.PREFIX + "/product",fallback = ChargeProductApiFallback.class)
@Tag(name = "RPC 产品信息")
public interface ChargeProductApi extends CommonApi {

    @PostMapping("/findNameByIds")
    @Operation(summary = "根据id 获取 名称 信息")
    CommonResult<List<CommonVO>> findNameByIds(@RequestBody CommonDTO commonDTO);
}
