package com.linkcircle.boss.module.charge.fee.web.bill.service.impl;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.fee.web.bill.service.CurrencySymbolService;
import com.linkcircle.boss.module.crm.api.currency.CurrencyApi;
import com.linkcircle.boss.module.crm.api.currency.vo.CurrencyRespVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 15:27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CurrencySymbolServiceImpl implements CurrencySymbolService {

    private final CurrencyApi currencyApi;

    /**
     * 根据货币代码获取货币符号。
     *
     * @param currencyCode 货币代码
     * @return 货币符号，如果无法获取则返回货币代码
     */
    @Override
    public String getCurrencySymbol(String currencyCode) {
        // 调用currencyApi的list方法获取货币信息
        CommonResult<List<CurrencyRespVO>> currencyRespVOCommonResult = currencyApi.list();
        // 判断调用是否成功且返回的数据不为空
        if (currencyRespVOCommonResult.isSuccess() && currencyRespVOCommonResult.getData() != null) {
            // 遍历返回的货币信息列表
            for (CurrencyRespVO currencyRespVO : currencyRespVOCommonResult.getData()) {
                // 判断当前遍历到的货币代码是否与输入的货币代码匹配
                if (currencyRespVO.getCurrencyCode().equals(currencyCode)) {
                    // 打印日志，表示获取货币符号成功
                    log.info("获取货币符号成功，currencyCode:{}, symbol:{}", currencyRespVO.getCurrencyCode(), currencyRespVO.getSymbol());
                    // 返回匹配的货币符号
                    return currencyRespVO.getSymbol();
                }
            }
        } else {
            // 打印日志，表示获取货币符号失败
            log.error("获取货币符号失败，currencyCode:{}", currencyCode);
        }
        // 如果未找到匹配的货币代码，则返回输入的货币代码
        return currencyCode;
    }

}
