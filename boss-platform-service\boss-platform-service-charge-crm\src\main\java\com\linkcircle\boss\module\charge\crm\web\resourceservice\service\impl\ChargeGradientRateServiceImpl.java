package com.linkcircle.boss.module.charge.crm.web.resourceservice.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.crm.enums.ChargeTypeEnum;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceAddDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceEditDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceOperationBaseDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceOperationDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.entity.ChargeResourceServicePrice;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.service.IChargeResourceServicePriceService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.service.base.IChargeTypeService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import static com.linkcircle.boss.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants.RESOURCE_SERVICE_VERSION_DTO_EXCEPTION;

/**
 * <p>
 * 梯度费率处理服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Service
@RequiredArgsConstructor
public class ChargeGradientRateServiceImpl implements IChargeTypeService<ChargeResourceServiceOperationBaseDTO, ChargeResourceServicePrice> {

    private final IChargeResourceServicePriceService chargeResourceServicePriceService;

    @Override
    public CommonResult<?> add(ChargeResourceServiceOperationBaseDTO baseDTO, ChargeResourceServicePrice chargeResourceServicePrice) {
        ChargeResourceServiceAddDTO addDTO = convertToConcreteDTO(baseDTO, ChargeResourceServiceAddDTO.class, RESOURCE_SERVICE_VERSION_DTO_EXCEPTION);
        return getCommonResult(chargeResourceServicePrice, addDTO);
    }

    @NotNull
    private CommonResult<?> getCommonResult(ChargeResourceServicePrice chargeResourceServicePrice, ChargeResourceServiceAddDTO addDTO) {
        CommonResult<?> commonResult = checkParam(addDTO);
        if (!commonResult.isSuccess()) {
            return commonResult;
        }
        chargeResourceServicePrice.setCurrencyPriceJson(JSONUtil.toJsonStr(addDTO.getChargeGradientRateDTOS()));
        chargeResourceServicePriceService.save(chargeResourceServicePrice);
        return CommonResult.success();
    }

    private CommonResult<?> checkParam(ChargeResourceServiceOperationDTO dto) {
        if (CollUtil.isEmpty(dto.getChargeGradientRateDTOS())) {
            return CommonResult.error(BAD_REQUEST.getCode(), "请求参数不正确:阶梯费率参数为空");
        }
        if (StrUtil.isEmpty(dto.getUnitLabel())) {
            return CommonResult.error(BAD_REQUEST.getCode(), "请求参数不正确:单位标签参数为空");
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> addVersion(ChargeResourceServiceOperationBaseDTO baseDTO, ChargeResourceServicePrice chargeResourceServicePrice) {
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> edit(ChargeResourceServiceOperationBaseDTO baseDTO, ChargeResourceServicePrice chargeResourceServicePrice) {
        ChargeResourceServiceEditDTO editDTO = convertToConcreteDTO(baseDTO, ChargeResourceServiceEditDTO.class, RESOURCE_SERVICE_VERSION_DTO_EXCEPTION);
        CommonResult<?> commonResult = checkParam(editDTO);
        if (!commonResult.isSuccess()) {
            return commonResult;
        }
        chargeResourceServicePrice.setCurrencyPriceJson(JSONUtil.toJsonStr(editDTO.getChargeGradientRateDTOS()));
        chargeResourceServicePriceService.updateById(chargeResourceServicePrice);
        return CommonResult.success();
    }

    @Override
    public ChargeTypeEnum getType() {
        return ChargeTypeEnum.ladderRate;
    }
}
