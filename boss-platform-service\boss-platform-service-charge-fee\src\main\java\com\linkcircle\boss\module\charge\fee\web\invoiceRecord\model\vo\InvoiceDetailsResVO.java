package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo;

import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 发票记录详情
 *
 * <AUTHOR> zyuan
 * @data : 2025-06-30
 */
@Schema(description = "发票 - 发票记录详情 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceDetailsResVO {

    @Schema(description = "发票编号(如CN-1, LA20240530-0001)", requiredMode = Schema.RequiredMode.REQUIRED, example = "LA20240530-0001")
    private String invoiceId;

    @Schema(description = "票据类型 0-信用票据 1-普票 2-国外发票 3-增值税专票 4-红冲发票", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer type;

    @Schema(description = "开票账单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "bill202502030182012")
    private String invoiceBillingId;

    @Schema(description = "发票状态 0-待审核 1-审核不通过 2-已开票 3-已作废", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    // 客户信息
    @Schema(description = "客户信息")
    private ChargeCustomerInfoVO customerInfo;

    // 主体信息
    @Schema(description = "主体信息")
    private EntityDetailsVO entityInfo;

    // 发票配置信息
    @Schema(description = "发票基础配置")
    private InvoiceDetailsVO invoiceConfig;

    // 账单信息
    @Schema(description = "账单类型 1-预付费账单，2-后付费账单,3-手工账单", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer billType;

    @Schema(description = "账单所有内容")
    private List<BillContent> showContents;
}
