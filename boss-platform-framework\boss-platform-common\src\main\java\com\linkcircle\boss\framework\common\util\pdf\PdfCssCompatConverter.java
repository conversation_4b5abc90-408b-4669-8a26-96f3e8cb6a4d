package com.linkcircle.boss.framework.common.util.pdf;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.HashMap;
import java.util.Map;

/**
 * 将现代CSS转换为openhtmltopdf兼容语法的工具类
 *
 * <AUTHOR> zyuan
 * @data : 2025-07-23
 */
public class PdfCssCompatConverter {

    // 现代CSS属性到PDF兼容属性的映射
    private static final Map<String, String> CSS_REPLACEMENTS = new HashMap<>();

    static {

        // Flexbox → 表格布局
        CSS_REPLACEMENTS.put("display:\\s*flex", "display: table");
        CSS_REPLACEMENTS.put("flex-direction:\\s*row", "");
        CSS_REPLACEMENTS.put("justify-content:\\s*center", "text-align: center");

        // Grid → 浮动布局
        CSS_REPLACEMENTS.put("display:\\s*grid", "display: block");
        CSS_REPLACEMENTS.put("grid-template-columns:\\s*repeat\\(\\d+,\\s*\\d+px\\)", "");

        // 阴影/圆角等不支持属性
        CSS_REPLACEMENTS.put("box-shadow:.+?;", "");
        CSS_REPLACEMENTS.put("border-radius:.+?;", "");
    }

    // 需要添加PDF专用样式的类名
    private static final String[] PDF_SPECIAL_CLASSES = {
            "pdf-page-break { page-break-after: always; }",
            "pdf-keep-together { page-break-inside: avoid; }"
    };

    /**
     * 转换HTML中的现代CSS为PDF兼容语法
     *
     * @param originalHtml 原始HTML
     * @return 转换后的HTML
     */
    public static String convertModernCssToPdfCompatible(String originalHtml) {
        Document doc = Jsoup.parse(originalHtml);

        // 1. 处理<style>标签
        Elements styleTags = doc.select("style");
        for (Element style : styleTags) {
            String css = style.html();
            css = replaceModernCss(css);
            style.html(css);
        }

        // 2. 处理内联样式
        Elements styledElements = doc.select("[style]");
        for (Element el : styledElements) {
            String inlineStyle = el.attr("style");
            inlineStyle = replaceModernCss(inlineStyle);
            el.attr("style", inlineStyle);
        }

        // 3. 添加PDF专用样式
        StringBuilder pdfCss = new StringBuilder();
        for (String cls : PDF_SPECIAL_CLASSES) {
            pdfCss.append(cls).append("\n");
        }
        doc.head().appendElement("style").text(pdfCss.toString());

        return doc.html();
    }

    /**
     * 替换单个CSS规则
     */
    private static String replaceModernCss(String css) {
        for (Map.Entry<String, String> entry : CSS_REPLACEMENTS.entrySet()) {
            css = css.replaceAll(entry.getKey(), entry.getValue());
        }
        return css;
    }
}
