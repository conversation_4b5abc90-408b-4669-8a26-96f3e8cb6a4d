package com.linkcircle.boss.module.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-07-08 15:00
 * @description 出账周期类型枚举
 */
@Getter
@AllArgsConstructor
public enum BillingCycleTypeEnum {

    /**
     * 每周出账
     */
    WEEKLY(2, "每周"),

    /**
     * 每月出账
     */
    MONTHLY(3, "每月"),

    /**
     * 每季度出账
     */
    QUARTERLY(4, "每季度"),

    /**
     * 每年出账
     */
    YEARLY(5, "每年");

    /**
     * 类型值
     */
    private final Integer type;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 对应的枚举，如果不存在返回null
     */
    public static BillingCycleTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (BillingCycleTypeEnum cycleType : values()) {
            if (cycleType.getType().equals(type)) {
                return cycleType;
            }
        }
        return null;
    }

    public static String getDescriptionByType(Integer type) {
        BillingCycleTypeEnum cycleType = getByType(type);
        return cycleType != null ? cycleType.getDescription() : null;
    }

    /**
     * 判断类型值是否有效
     *
     * @param type 类型值
     * @return 是否有效
     */
    public static boolean isValid(Integer type) {
        return getByType(type) != null;
    }
}
