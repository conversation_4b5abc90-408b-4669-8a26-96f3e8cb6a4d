package com.linkcircle.boss.module.charge.crm.web.contract.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.web.context.LoginUser;
import com.linkcircle.boss.framework.web.core.util.WebFrameworkUtils;
import com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.crm.web.contract.mapper.ContractInfoMapper;
import com.linkcircle.boss.module.charge.crm.web.contract.mapper.ContractTeamInfoMapper;
import com.linkcircle.boss.module.charge.crm.web.contract.model.dto.ContractInfoPageQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.contract.model.dto.ContractMemberReqDTO;
import com.linkcircle.boss.module.charge.crm.web.contract.model.dto.ContractSaveReqDTO;
import com.linkcircle.boss.module.charge.crm.web.contract.model.entity.ContractInfoDO;
import com.linkcircle.boss.module.charge.crm.web.contract.model.entity.ContractTeamInfoDO;
import com.linkcircle.boss.module.charge.crm.web.contract.model.vo.*;
import com.linkcircle.boss.module.charge.crm.web.contract.service.ContractInfoService;
import com.linkcircle.boss.module.charge.crm.web.customer.mapper.CustomerAccountMapper;
import com.linkcircle.boss.module.charge.crm.web.customer.model.entity.ChargeCustomerAccountsInfo;
import com.linkcircle.boss.module.charge.crm.web.project.mapper.ProjectInfoMapper;
import com.linkcircle.boss.module.charge.crm.web.project.model.dto.ProjectInfoPageQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.project.model.entity.ProjectInfoDO;
import com.linkcircle.boss.module.charge.crm.web.project.model.vo.ProjectInfoRespVO;
import com.linkcircle.boss.module.charge.crm.web.project.service.ProjectInfoService;
import com.linkcircle.boss.module.charge.crm.web.subscribe.mapper.ChargeSubscriptionsMapper;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.entity.ChargeSubscriptionsDO;
import com.linkcircle.boss.module.charge.crm.web.supplier.mapper.SupplierAccountMapper;
import com.linkcircle.boss.module.charge.crm.web.supplier.model.entity.SupplierAccountDO;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.fee.api.bill.BillFeeApi;
import com.linkcircle.boss.module.system.api.file.dto.FileUpdateReqDTO;
import com.linkcircle.boss.module.system.api.permission.PermissionApi;
import com.linkcircle.boss.module.system.api.user.dto.UserDataScopeReqDTO;
import com.linkcircle.boss.module.system.enums.permission.UserDataScopeTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同管理 业务类
 *
 * <AUTHOR> zyuan
 * @data : 2025-06-11
 */
@Service("ContractInfoService")
@Slf4j
public class ContractInfoServiceImpl implements ContractInfoService {

    @Resource
    private ContractInfoMapper contractInfoMapper;

    @Resource
    private ProjectInfoService projectInfoService;

    @Resource
    private ContractTeamInfoMapper teamInfoMapper;

    @Resource
    private ProjectInfoMapper projectInfoMapper;

    @Resource
    private ChargeSubscriptionsMapper subscriptionsMapper;

    @Resource
    private CustomerAccountMapper accountMapper;

    @Resource
    private SupplierAccountMapper supplierAccountMapper;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private BillFeeApi billFeeApi;

    /**
     * 查询合同信息列表 分页
     *
     * @param queryDTO
     * @return
     */
    @Override
    public PageResult<ContractInfoRespVO> getContractInfoPage(ContractInfoPageQueryDTO queryDTO) {
        Page<?> page = MyBatisUtils.buildPage(queryDTO);
        List<ContractInfoRespVO> list = contractInfoMapper.queryByPage(page, queryDTO);
        return MyBatisUtils.convert2PageResult(page, list);
    }

    /**
     * 创建合同信息
     *
     * @param dto
     * @return
     */
    @Override
    public CommonResult<?> create(ContractSaveReqDTO dto) {

        //合同id、编号不可重复
        ContractInfoDO entity = dto.toEntity(null);
        entity.setId(IdUtil.getSnowflakeNextId());
        if (StringUtils.isEmpty(entity.getContractId())) {
            entity.setContractId("CT_" + IdUtil.getSnowflakeNextIdStr());
        }
        ContractInfoDO contractInfoDO = contractInfoMapper.selectOne(new LambdaQueryWrapper<ContractInfoDO>()
                .eq(ContractInfoDO::getContractId, dto.getContractId())
                .or()
                .eq(ContractInfoDO::getContractCode, dto.getContractCode()));
        if (contractInfoDO != null) {
            return CommonResult.error(ErrorCodeConstants.CONTRACT_ID_OR_CODE_ERROR);
        }
        LoginUser loginUser = WebFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            entity.setCreator(loginUser.getUsername());
        }
        entity.setCreateTime(System.currentTimeMillis());

        // 判断结束时间 是否大于开始时间
        if (dto.getEndTime() != 0) {
            int compare = DateUtil.compare(new Date(dto.getStartTime()), new Date(dto.getEndTime()));
            if (compare > 0) {
                return CommonResult.error(ErrorCodeConstants.PROJECT_TIME_ERROR);
            }
        }
        if (dto.getStartTime() <= System.currentTimeMillis()) {
            if (dto.getEndTime() <= System.currentTimeMillis()) {
                entity.setStatus(2);
            } else {
                entity.setStatus(1);
            }
        } else {
            entity.setStatus(0);
        }
        int insert = contractInfoMapper.insert(entity);

        // 添加合同成员
        if (dto.getMemberList() != null) {
            dto.getMemberList().getFirst().setContractId(entity.getId());
            addMember(dto.getMemberList());

            // 添加权限
            UserDataScopeReqDTO reqDbDTO = new UserDataScopeReqDTO();
            Set<Long> userIdDbs = new HashSet<>();
            userIdDbs.add(dto.getHandledById());
            for (ContractMemberReqDTO reqDTO : dto.getMemberList()) {
                userIdDbs.add(reqDTO.getCustomerId());
            }
            reqDbDTO.setUserIds(userIdDbs);
            Set<Long> dataIdDbs = new HashSet<>();
            dataIdDbs.add(entity.getId());
            reqDbDTO.setDataScopeIds(dataIdDbs);
            reqDbDTO.setDataScopeType(UserDataScopeTypeEnum.CONTRACT.getCode());
            permissionApi.insertUserDataScope(reqDbDTO);
        }
        return insert > 0 ? CommonResult.success() : CommonResult.error(ErrorCodeConstants.CREATE_FAILED);
    }

    /**
     * 编辑合同信息
     *
     * @param dto
     * @return
     */
    @Override
    public CommonResult<?> edit(ContractSaveReqDTO dto) {

        //合同id、编号不可重复
        ContractInfoDO contractInfoDO = contractInfoMapper.selectOne(new LambdaQueryWrapper<ContractInfoDO>()
                .eq(ContractInfoDO::getContractId, dto.getContractId()));
        if (contractInfoDO == null || contractInfoDO.getDeleted()) {
            return CommonResult.error(ErrorCodeConstants.CONTRACT_NOT_EXIST);
        }
        ContractInfoDO entity = dto.toEntity(contractInfoDO);
        if (dto.getFileList() == null || dto.getFileList().isEmpty()) {
            entity.setFileListJson("");
        }
        LoginUser loginUser = WebFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            entity.setUpdater(loginUser.getUsername());
        }
        entity.setUpdateTime(System.currentTimeMillis());

        // 判断时间
        if (dto.getEndTime() != 0) {
            int compare = DateUtil.compare(new Date(dto.getStartTime()), new Date(dto.getEndTime()));
            if (compare > 0) {
                return CommonResult.error(ErrorCodeConstants.PROJECT_TIME_ERROR);
            }
        }
        if (dto.getStartTime() <= System.currentTimeMillis()) {
            if (dto.getEndTime() <= System.currentTimeMillis()) {
                entity.setStatus(2);
            } else {
                entity.setStatus(1);
            }
        } else {
            entity.setStatus(0);
        }

        Set<Long> userIds = new HashSet<>();
        Set<Long> userIdsDel = new HashSet<>();

        // 判断合同成员是否变更
        List<ContractTeamRespVO> contractTeamRespVOS = queryMember(contractInfoDO.getId());
        Set<Long> dbId = new HashSet<>();
        List<Long> deleteIds = new ArrayList<>();
        if (contractTeamRespVOS != null && !contractTeamRespVOS.isEmpty()) {
            if (dto.getMemberList() != null && !dto.getMemberList().isEmpty()) {
                List<ContractMemberReqDTO> memberList = dto.getMemberList();
                for (ContractMemberReqDTO reqDTO : memberList) {
                    dbId.add(reqDTO.getCustomerId());
                    userIds.add(reqDTO.getCustomerId());
                }
                for (ContractTeamRespVO contractTeamRespVO : contractTeamRespVOS) {
                    userIdsDel.add(contractTeamRespVO.getCustomerId());
                    if (!dbId.contains(contractTeamRespVO.getCustomerId())) {
                        deleteIds.add(contractTeamRespVO.getId());
                    }
                }
            } else {

                // 删除全部
                for (ContractTeamRespVO contractTeamRespVO : contractTeamRespVOS) {
                    deleteIds.add(contractTeamRespVO.getId());
                    userIdsDel.add(contractTeamRespVO.getCustomerId());
                }
            }
            if (!deleteIds.isEmpty()) {
                teamInfoMapper.deleteByIds(deleteIds);
            }
        }
        int update = contractInfoMapper.updateById(entity);

        // 判断经办人是否变更
        if (dto.getHandledById() != null && !contractInfoDO.getHandledById().equals(dto.getHandledById())) {
            userIdsDel.add(contractInfoDO.getHandledById());
            userIds.add(dto.getHandledById());
        }

        // 删除权限
        if (!userIdsDel.isEmpty()) {
            UserDataScopeReqDTO reqDbDTO = new UserDataScopeReqDTO();
            reqDbDTO.setUserIds(userIdsDel);
            Set<Long> dataIdDbs = new HashSet<>();
            dataIdDbs.add(contractInfoDO.getId());
            reqDbDTO.setDataScopeIds(dataIdDbs);
            reqDbDTO.setDataScopeType(UserDataScopeTypeEnum.CONTRACT.getCode());
            permissionApi.removeUserDataScope(reqDbDTO);
        }

        // 添加权限
        if (!userIds.isEmpty()) {
            UserDataScopeReqDTO reqDbDTO = new UserDataScopeReqDTO();
            reqDbDTO.setUserIds(userIds);
            Set<Long> dataIdDbs = new HashSet<>();
            dataIdDbs.add(contractInfoDO.getId());
            reqDbDTO.setDataScopeIds(dataIdDbs);
            reqDbDTO.setDataScopeType(UserDataScopeTypeEnum.CONTRACT.getCode());
            permissionApi.insertUserDataScope(reqDbDTO);
        }

        // 添加合同成员
        if (dto.getMemberList() != null && !dto.getMemberList().isEmpty()) {
            dto.getMemberList().getFirst().setContractId(entity.getId());
            addMember(dto.getMemberList());
        }
        return update > 0 ? CommonResult.success() : CommonResult.error(ErrorCodeConstants.UPDATE_FAILED);
    }

    /**
     * 根据项目查看合同信息
     *
     * @param projectId 项目主键id
     * @return
     */
    @Override
    public List<ContractByProjectRespVO> contractByProject(Long projectId) {
        List<ContractByProjectRespVO> list = new ArrayList<>();
        List<ContractInfoDO> contractInfoDOS = contractInfoMapper.selectList(new LambdaQueryWrapper<ContractInfoDO>()
                .eq(ContractInfoDO::getProjectId, projectId)
                .eq(ContractInfoDO::getDeleted, 0));
        if (contractInfoDOS != null && !contractInfoDOS.isEmpty()) {
            for (ContractInfoDO contractInfoDO : contractInfoDOS) {
                ContractByProjectRespVO vo = new ContractByProjectRespVO();
                vo.setContractId(contractInfoDO.getContractId());
                vo.setContractName(contractInfoDO.getContractName());
                vo.setContractCode(contractInfoDO.getContractCode());
                vo.setType(contractInfoDO.getType());
                vo.setSigningTime(contractInfoDO.getSigningTime());
                vo.setStatus(contractInfoDO.getStatus());
                vo.setStartTime(contractInfoDO.getStartTime());
                vo.setEndTime(contractInfoDO.getEndTime());
                vo.setHandledBy(contractInfoDO.getHandledBy());
                vo.setCreator(contractInfoDO.getCreator());
                vo.setUpdateTime(contractInfoDO.getUpdateTime());
                vo.setCreateTime(contractInfoDO.getCreateTime());
                vo.setRemark(contractInfoDO.getRemark());

                // 判断是否为一次性费用
                if (contractInfoDO.getCostType() == 0) {
                    vo.setCostAmount(contractInfoDO.getCostAmount());
                } else {

                    // 查询费用
                    CommonResult<BigDecimal> result = billFeeApi.computeConstructFee(contractInfoDO.getId(),
                            contractInfoDO.getStartTime(), contractInfoDO.getEndTime());
                    log.info("消耗性费用合同【{}】查询金额返回: {}", contractInfoDO.getContractName(), JSON.toJSONString(result));
                    vo.setCostAmount(result.getData());
                }
                vo.setCostUnit(contractInfoDO.getCostUnit());
                list.add(vo);
            }
        }
        return list;
    }

    @Override
    public CommonResult<?> deleteById(Long id) {
        ContractInfoDO contractInfoDO = contractInfoMapper.selectById(id);
        if (contractInfoDO == null || contractInfoDO.getDeleted()) {
            return CommonResult.error(ErrorCodeConstants.DELETE_FAILED);
        }
        if (contractInfoDO.getStatus() != 0) {
            return CommonResult.error(ErrorCodeConstants.CONTRACT_ONLY_ALLOW_NOT_START);
        }

        // 判断是否有关联订阅
        Long count = subscriptionsMapper.selectCount(new LambdaQueryWrapper<ChargeSubscriptionsDO>()
                .eq(ChargeSubscriptionsDO::getContractId, contractInfoDO.getId())
                .eq(ChargeSubscriptionsDO::getDeleted, 0));
        if (count > 0) {
            return CommonResult.error(ErrorCodeConstants.CONTRACT_EXIST_SUBSCRIBE);
        }
        int i = contractInfoMapper.deleteById(id);

        // 删除成员
        List<Long> deleteIds = new ArrayList<>();
        Set<Long> userIdDbs = new HashSet<>();
        List<ContractTeamRespVO> contractTeamRespVOS = queryMember(contractInfoDO.getId());
        if (contractTeamRespVOS != null && !contractTeamRespVOS.isEmpty()) {
            for (ContractTeamRespVO vo : contractTeamRespVOS) {
                deleteIds.add(vo.getId());
                userIdDbs.add(vo.getCustomerId());
            }
        }
        if (!deleteIds.isEmpty()) {
            teamInfoMapper.deleteByIds(deleteIds);
        }

        // 删除权限
        if (!userIdDbs.isEmpty()) {
            UserDataScopeReqDTO reqDbDTO = new UserDataScopeReqDTO();
            reqDbDTO.setUserIds(userIdDbs);
            Set<Long> dataIdDbs = new HashSet<>();
            dataIdDbs.add(contractInfoDO.getId());
            reqDbDTO.setDataScopeIds(dataIdDbs);
            reqDbDTO.setDataScopeType(UserDataScopeTypeEnum.CONTRACT.getCode());
            permissionApi.removeUserDataScope(reqDbDTO);
        }
        return CommonResult.success();
    }

    /**
     * 添加合同成员
     *
     * @param dto
     * @return
     */
    @Override
    public CommonResult<?> addMember(List<ContractMemberReqDTO> dto) {
        if (dto.isEmpty()) {
            return CommonResult.success();
        }
        List<ContractTeamInfoDO> teamInfoDOS = teamInfoMapper.selectList(new LambdaQueryWrapper<ContractTeamInfoDO>()
                .eq(ContractTeamInfoDO::getContractId, dto.getFirst().getContractId())
                .eq(ContractTeamInfoDO::getDeleted, 0));
        Long contractId = dto.getFirst().getContractId();
        Map<Long, ContractTeamInfoDO> collect = null;
        if (!teamInfoDOS.isEmpty()) {
            collect = teamInfoDOS.stream()
                    .filter(team -> team.getCustomerId() != null) // 过滤掉 customerId 为 null 的对象
                    .collect(Collectors.toMap(
                            ContractTeamInfoDO::getCustomerId,
                            team -> team,
                            (oldValue, newValue) -> newValue // 如果 customerId 重复，保留新值
                    ));
        }
        List<ContractTeamInfoDO> list = new ArrayList<>();
        for (ContractMemberReqDTO reqDTO : dto) {
            ContractTeamInfoDO teamInfoDO = new ContractTeamInfoDO();
            teamInfoDO.setId(IdUtil.getSnowflakeNextId());
            teamInfoDO.setContractId(contractId);
            teamInfoDO.setCustomerId(reqDTO.getCustomerId());
            teamInfoDO.setCustomerName(reqDTO.getCustomerName());
            teamInfoDO.setCreateTime(System.currentTimeMillis());
            LoginUser loginUser = WebFrameworkUtils.getLoginUser();
            if (loginUser != null) {
                teamInfoDO.setCreator(loginUser.getUsername());
            }
            if (collect == null) {
                list.add(teamInfoDO);
            } else {
                if (collect.containsKey(reqDTO.getCustomerId())) {
                    continue;
                }
                list.add(teamInfoDO);
            }
        }
        if (list.isEmpty()) {
            return CommonResult.success();
        }
        Boolean b = teamInfoMapper.insertBatch(list);
        return b ? CommonResult.success() : CommonResult.error(ErrorCodeConstants.CREATE_FAILED);
    }

    /**
     * 删除合同成员
     *
     * @param ids
     * @return
     */
    @Override
    public CommonResult<?> deleteMember(List<Long> ids) {
        return teamInfoMapper.deleteByIds(ids) > 0 ? CommonResult.success() : CommonResult.error(ErrorCodeConstants.DELETE_FAILED);
    }

    /**
     * 查询合同成员
     *
     * @param id 合同id
     * @return
     */
    @Override
    public List<ContractTeamRespVO> queryMember(Long id) {

        // 获取经办人
        List<ContractTeamRespVO> list = new ArrayList<>();
        List<ContractTeamInfoDO> teamInfoDOS = teamInfoMapper.selectList(new LambdaQueryWrapper<ContractTeamInfoDO>()
                .eq(ContractTeamInfoDO::getContractId, id)
                .eq(ContractTeamInfoDO::getDeleted, 0));
        if (teamInfoDOS.isEmpty()) {
            return list;
        }

        // 查询经办人
        ContractInfoDO contractInfoDO = contractInfoMapper.selectOne(new LambdaQueryWrapper<ContractInfoDO>()
                .eq(ContractInfoDO::getId, id));
        for (ContractTeamInfoDO teamInfoDO : teamInfoDOS) {
            ContractTeamRespVO respVO = new ContractTeamRespVO();
            respVO.setId(teamInfoDO.getId());
            respVO.setCustomerId(teamInfoDO.getCustomerId());
            respVO.setCustomerName(teamInfoDO.getCustomerName());
            if (Objects.equals(teamInfoDO.getCustomerId(), contractInfoDO.getHandledById())) {
                respVO.setType(1);
            } else {
                respVO.setType(0);
            }
            list.add(respVO);
        }
        return list;
    }

    /**
     * 合同详细信息
     *
     * @param id 合同id（主键）
     * @return
     */
    @Override
    public ContractDetailRespVO details(Long id) {
        ContractDetailRespVO vo = new ContractDetailRespVO();
        ContractInfoDO contractInfoDO = contractInfoMapper.selectById(id);
        if (contractInfoDO == null) {
            return vo;
        }

        // 获取立项信息
        ContractBaseVO baseInfo = new ContractBaseVO();
        ProjectInfoDO projectInfoDO = projectInfoMapper.selectOne(new LambdaQueryWrapper<ProjectInfoDO>()
                .eq(ProjectInfoDO::getId, contractInfoDO.getProjectId())
                .eq(ProjectInfoDO::getDeleted, 0));
        if (projectInfoDO != null) {
            baseInfo.setProjectId(projectInfoDO.getId());
            baseInfo.setProjectCode(projectInfoDO.getProjectCode());
            baseInfo.setProjectName(projectInfoDO.getProjectName());
            ProjectInfoPageQueryDTO dto = new ProjectInfoPageQueryDTO();
            dto.setId(contractInfoDO.getProjectId());
            PageResult<ProjectInfoRespVO> projectInfoPage = projectInfoService.getProjectInfoPage(dto);
            ProjectInfoRespVO first = projectInfoPage.getList().getFirst();
            baseInfo.setProjectInfo(first);
        }
        ChargeCustomerAccountsInfo accountsInfo = accountMapper.selectById(contractInfoDO.getAccountId());
        if (accountsInfo != null) {
            baseInfo.setAccountCode(accountsInfo.getAccountCode());
            baseInfo.setAccountName(accountsInfo.getAccountName());
            baseInfo.setAccountId(contractInfoDO.getAccountId());
        } else {
            SupplierAccountDO supplierAccountDO = supplierAccountMapper.selectById(contractInfoDO.getAccountId());
            baseInfo.setAccountCode(supplierAccountDO.getAccountCode());
            baseInfo.setAccountName(supplierAccountDO.getAccountName());
            baseInfo.setAccountId(contractInfoDO.getAccountId());
        }
        vo.setBaseInfo(baseInfo);

        // 获取合同信息
        ContractInfoVO contractInfo = new ContractInfoVO();
        contractInfo.setId(contractInfoDO.getId());
        contractInfo.setContractId(contractInfoDO.getContractId());
        contractInfo.setContractCode(contractInfoDO.getContractCode());
        contractInfo.setContractName(contractInfoDO.getContractName());
        contractInfo.setHandledBy(contractInfoDO.getHandledBy());
        contractInfo.setEntityId(contractInfoDO.getEntityId());
        contractInfo.setPayType(contractInfoDO.getPayType());
        contractInfo.setSigningTime(contractInfoDO.getSigningTime());
        contractInfo.setEndTime(contractInfoDO.getEndTime());
        contractInfo.setStartTime(contractInfoDO.getStartTime());
        contractInfo.setHandledById(contractInfoDO.getHandledById());
        contractInfo.setStatementType(contractInfoDO.getStatementType());
        contractInfo.setStatementDate(contractInfoDO.getStatementDate());
        contractInfo.setCostType(contractInfoDO.getCostType());
        contractInfo.setCostUnit(contractInfoDO.getCostUnit());

        // 判断合同类型
        if (contractInfoDO.getCostType() == 0) {
            contractInfo.setCostAmount(contractInfoDO.getCostAmount());
        } else {

            // 查询账单金额
            CommonResult<BigDecimal> result = billFeeApi.computeConstructFee(contractInfoDO.getId(),
                    contractInfoDO.getStartTime(), contractInfoDO.getEndTime());
            log.info("消耗性费用合同【{}】查询金额返回: {}", contractInfoDO.getContractName(), JSON.toJSONString(result));
            contractInfo.setCostAmount(result.getData());
        }
        contractInfo.setRemark(contractInfoDO.getRemark());
        contractInfo.setTaxRateType(contractInfoDO.getTaxRateType());
        contractInfo.setTaxRate(contractInfoDO.getTaxRate());
        if (!StringUtils.isEmpty(contractInfoDO.getFileListJson())) {
            List<FileUpdateReqDTO> dtos = JSON.parseArray(contractInfoDO.getFileListJson(), FileUpdateReqDTO.class);
            contractInfo.setFileList(dtos);
        }

        // 查询成员
        List<ContractTeamRespVO> contractTeamRespVOS = queryMember(contractInfoDO.getId());
        contractInfo.setTeamRespVOS(contractTeamRespVOS);
        vo.setContractInfo(contractInfo);
        return vo;
    }

    /**
     * 合同下拉列表
     *
     * @return
     */
    @Override
    public CommonResult<List<ContractSelectRespVO>> contractSelect() {
        List<ContractSelectRespVO> list = new ArrayList<>();
        List<ContractInfoDO> infoDOS = contractInfoMapper.selectList(new LambdaQueryWrapper<ContractInfoDO>()
                .eq(ContractInfoDO::getDeleted, 0));
        if (!infoDOS.isEmpty()) {
            for (ContractInfoDO infoDO : infoDOS) {
                ContractSelectRespVO respVO = new ContractSelectRespVO();
                respVO.setId(infoDO.getId());
                respVO.setContractName(infoDO.getContractName());
                list.add(respVO);
            }
        }
        return CommonResult.success(list);
    }

    @Override
    public List<CommonVO> findNameByIds(CommonDTO commonDTO) {
        List<ContractInfoDO> contractInfos = contractInfoMapper.findNameByIds(commonDTO);
        if (CollectionUtils.isNotEmpty(contractInfos)) {
            return contractInfos.stream().map(t -> CommonVO.builder().id(t.getId()).name(t.getContractName()).build()).toList();
        }
        return List.of();
    }
}
