package com.linkcircle.boss.module.charge.fee.web.bill.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.tanant.TenantIgnore;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.enums.WalletsDeductStatusEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.convert.IncomeBillHistoryConvert;
import com.linkcircle.boss.module.charge.fee.web.bill.convert.MakeupIncomeBillConvert;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.IncomeBillHistoryMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.MakeupBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.MakeupProductBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.MakeupProductServiceBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqHistoryDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.InvoiceBillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.IncomeBillHistoryDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupProductIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.history.IncomeBillHistoryVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeBillDetailVo;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeProductBilDetailVo;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeProductServiceBillDetailVo;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillContentHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.InvoiceIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.MakeupBillService;
import com.linkcircle.boss.module.charge.fee.web.bill.util.NumberUtil;
import com.linkcircle.boss.module.charge.fee.web.invoice.mapper.InvoiceMapper;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.*;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.entity.ChargeInvoiceDO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/1 19:24
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class MakeupBillServiceImpl implements MakeupBillService {


    private final MakeupBillMapper makeupBillMapper;
    private final MakeupProductBillMapper makeupProductBillMapper;
    private final MakeupProductServiceBillMapper makeupProductServiceBillMapper;
    private final RocketMQTemplate rocketMQTemplate;

    private final InvoiceMapper invoiceMapper;
    private final CustomerApi customerApi;
    private final EntityApi entityApi;

    private final IncomeBillHistoryMapper incomeBillHistoryMapper;
    private final InvoiceIdGenerator invoiceIdGenerator;


    @Override
    public CustomerApi getCustomerApi() {
        return customerApi;
    }

    @Override
    public EntityApi getEntityApi() {
        return entityApi;
    }

    @Override
    public Logger getLogger() {
        return log;
    }


    /**
     * 将BillReqDto对象转化为草稿对象
     *
     * @param reqDto 请求对象，包含生成草稿所需的信息
     */
    @Override
    public void toDraft(BillReqDto reqDto) {
        // 执行查询操作，根据账单ID和账单时间查询账单数据
        List<MakeupIncomeBillDO> makeupIncomeBillDOS = executeIgnore(() -> makeupBillMapper.queryBillByIds(List.of(reqDto.getBillId()), reqDto.getBillingTime(), reqDto.getBillingTime()));

        // 判断查询结果是否为空
        if (CollectionUtils.isEmpty(makeupIncomeBillDOS)) {
            // 如果为空，则记录日志并抛出异常
            log.info("手工账单不存在，billId={}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }

        // 获取第一个账单对象
        MakeupIncomeBillDO billDO = makeupIncomeBillDOS.getFirst();

        // 判断账单状态是否为待支付状态
        if (!InvoiceEnum.BillStatus.WAIT_PAYMENT.is(billDO.getBillStatus())) {
            // 如果不是待支付状态，则记录日志并抛出异常
            log.info("手工账单不是确认状态[待支付],不能退回到草稿状态，billId={}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_DRAFT_NOT_MATCH);
        }
        // 更新账单状态为草稿状态
        makeupBillMapper.updateStatus(reqDto.getBillId(), reqDto.getBillingTime(), InvoiceEnum.BillStatus.DRAFT);
    }


    /**
     * 支付账单
     *
     * @param reqDto 账单请求对象，包含支付所需的所有信息
     */
    @Override
    public void payOffBill(BillReqDto reqDto) {
        List<MakeupIncomeBillDO> postpaidProductBills = executeIgnore(() -> makeupBillMapper.queryBillByIds(List.of(reqDto.getBillId()), reqDto.getBillingTime(), reqDto.getBillingTime()));
        if (CollectionUtils.isEmpty(postpaidProductBills)) {
            log.info("账单不存在，id:{}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }
        MakeupIncomeBillDO postpaidProductBill = postpaidProductBills.getFirst();
        if (!InvoiceEnum.BillStatus.WAIT_PAYMENT.is(postpaidProductBill.getBillStatus()) && !InvoiceEnum.BillStatus.UNSETTLED.is(postpaidProductBill.getBillStatus())) {
            log.info("账单状态不允许支付，id:{},billStatus:{}", reqDto.getBillId(), postpaidProductBill.getBillStatus());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_PAY_OFF_NOT_MATCH);
        }
        rocketMQTemplate.convertAndSend(ChargeTopicUtils.getWalletDeductionNoticeTopic(), buildPayOffDto(postpaidProductBill));
    }


    @Override
    public void checkInvoice(BillInvoiceCheckDTO checkReqDTO) {
        BillInvoiceIdDTO idDTO = checkReqDTO.getIdDTO();
        if (idDTO == null || CollectionUtils.isEmpty(idDTO.getBillDetailIds())
        ) {
            log.info("账单详情ID列表为空");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_INVOICE_CHECK_IDS);
        }
        if (StringUtils.isBlank(idDTO.getStartTime()) || StringUtils.isBlank(idDTO.getEndTime())) {
            log.info("账单详情时间范围为空,{}发票校验失败", JSONUtil.toJsonStr(checkReqDTO));
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_INVOICE_CHECK_BILL_TIME_RANGE);
        }

        //tax_rate 税率  available_invoice_amount 可开票金额 >0  entity_id 出账主体id
        Long startTime = strToLong.apply(idDTO.getStartTime());
        Long endTime = strToLong.apply(idDTO.getEndTime());
        List<Long> detailIds = checkReqDTO.getIdDTO().getBillDetailIds();
        List<BillInvoiceCheckGroupDTO> checkResults = executeIgnore(() -> makeupBillMapper.checkByIds(detailIds, startTime, endTime));
        if (CollectionUtils.isNotEmpty(checkResults) && Objects.equals(checkResults.size(), 1) && Objects.equals(checkResults.getFirst().getCount().intValue(), detailIds.size())) {
            log.info("账单详情ID列表符合开票条件,ids:{}", detailIds);
        } else {
            log.info("账单详情ID列表不符合开票条件,ids:{},result:{}", detailIds, JSONUtil.toJsonStr(checkResults));
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_INVOICE_CHECK_NOT_MATCH);
        }

    }

    @Override
    @Transactional
    public void createInvoice(BillInvoiceCreateReqDTO createReqDTO) {


        checkInvoiceCustomerAndEntity(createReqDTO, ErrorCodeConstants.MAKEUP_INCOME_BILL_INVOICE_CREATE_CUSTOMER_NOT_EXIST, ErrorCodeConstants.MAKEUP_INCOME_BILL_INVOICE_CREATE_ENTITY_NOT_EXIST);
        // 获取发票金额列表
        List<BillInvoiceAmountDTO> invoiceAmounts = createReqDTO.getInvoiceAmounts();
        // 计算最大和最小账单时间
        Long maxTime = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillingTime).max(Long::compareTo).orElse(0L);
        Long minTime = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillingTime).min(Long::compareTo).orElse(0L);
        List<Long> billIds = invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillDetailId).toList().stream().toList();
        // 根据账单详情ID查询账单详情
        List<MakeupIncomeBillDO> bills = executeIgnore(() -> makeupBillMapper.queryBillByIds(billIds, minTime, maxTime));

        // 如果账单详情为空，则抛出异常
        if (CollectionUtils.isEmpty(bills)) {
            log.info("账单详情不存在,billDetailIds:{}", invoiceAmounts.stream().map(BillInvoiceAmountDTO::getBillDetailId).toList());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }
        // 将账单详情转换为Map，方便后续查询
        Map<String, MakeupIncomeBillDO> detailMap = bills.stream().collect(Collectors.toMap(t -> t.getBillId().toString(), t -> t, (d1, d2) -> d2));
        // 创建发票DTO列表和账单详情DO列表
        List<ChargeInvoiceDO> invoiceDOs = new ArrayList<>();
        // 遍历发票金额列表，处理每个发票金额
        for (BillInvoiceAmountDTO invoiceAmount : invoiceAmounts) {
            // check and handle 发票 和 账单 开票金额
            handleCreateInvoice(invoiceAmount, detailMap, invoiceDOs, createReqDTO);
        }
        invoiceMapper.insertOrUpdate(invoiceDOs);
        // 更新账单详情 已开票金额 已可开票金额
        executeIgnore(() -> makeupBillMapper.updateById(detailMap.values()));
    }


    @Override
    public void cancelInvoice(BillInvoiceAmountCancelDTO cancelDTO) {
        log.info("取消发票,{}", JSONUtil.toJsonStr(cancelDTO));
        // 查询 账单数据
        List<MakeupIncomeBillDO> makeupBills = makeupBillMapper.queryBillByIds(List.of(cancelDTO.getBillDetailId()), cancelDTO.getBillingTime(), cancelDTO.getBillingTime());
        if (CollectionUtils.isEmpty(makeupBills)) {
            log.info("账单详情不存在,invoiceId:{},billId:{}", cancelDTO.getInvoiceId(), cancelDTO.getBillDetailId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }
        MakeupIncomeBillDO incomeBillDO = makeupBills.getFirst();
        incomeBillDO.setAvailableInvoiceAmount(cancelDTO.getInvoiceAmount().add(incomeBillDO.getInvoicedAmount()));
        incomeBillDO.setInvoicedAmount(incomeBillDO.getInvoicedAmount().subtract(cancelDTO.getInvoiceAmount()));
        makeupBillMapper.updateById(List.of(incomeBillDO));
    }


    private void handleCreateInvoice(BillInvoiceAmountDTO invoiceAmount, Map<String, MakeupIncomeBillDO> detailMap, List<ChargeInvoiceDO> invoiceDOs, BillInvoiceCreateReqDTO createReqDTO) {
        Long billDetailId = invoiceAmount.getBillDetailId();
        MakeupIncomeBillDO billDetailDO = detailMap.get(billDetailId.toString());
        // 如果账单详情不存在，则抛出异常
        if (billDetailDO == null) {
            log.info("账单详情不存在,billDetailId:{}", billDetailId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST);
        }

        // 如果开票金额小于等于0，则跳过
        if (invoiceAmount.getInvoiceAmount() == null || invoiceAmount.getInvoiceAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("开票金额小于等于0 不需要开票,billDetailId:{},invoiceAmount:{}", billDetailId, invoiceAmount.getInvoiceAmount());
            return;
        }

        // 如果账单已结清，则抛出异常
        if (!billDetailDO.getBillStatus().equals(InvoiceEnum.BillStatus.PAID.getCode())) {
            log.info("账单已结清,billDetailId:{}", billDetailId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_NOT_PAID_INVOICE);
        }

        // 如果开票金额大于可开票金额，则抛出异常
        if (billDetailDO.getAvailableInvoiceAmount().compareTo(invoiceAmount.getInvoiceAmount()) < 0) {
            log.info("开票金额大于可开票金额,billDetailId:{},invoiceAmount:{}", billDetailId, invoiceAmount.getInvoiceAmount());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_INVOICE_AMOUNT_NOT_ENOUGH);
        }

        // ChargeCustomerInvoiceDTO customerId = createReqDTO.getCustomer();
        String invoiceNo = invoiceIdGenerator.generateId(createReqDTO.getEntityId());
        // 创建发票DTO
        ChargeInvoiceDO invoiceDTO = ChargeInvoiceDO.builder()
                .id(IdUtil.getSnowflakeNextId())
                .invoiceId(StringUtils.isNotBlank(invoiceNo) ? invoiceNo : billDetailDO.getBillNo())
                .invoiceBillingId(billDetailId.toString())
                .invoiceBillingNo(billDetailDO.getBillNo())
                .invoiceAmount(invoiceAmount.getInvoiceAmount())
                .invoiceType(createReqDTO.getInvoiceType())
                .billType(InvoiceEnum.BillType.MAKEUP.getCode())
                .type(createReqDTO.getType())
                .entityId(billDetailDO.getEntityId())
                .customerId(billDetailDO.getCustomerId())
                .accountId(billDetailDO.getAccountId())
                .status(InvoiceEnum.Status.WAIT_AUDIT.getCode())
                .billServiceCode("")
                .currencySymbol(billDetailDO.getCurrency())
                .billTime(billDetailDO.getBillingTime())
                .accountId(createReqDTO.getAccountId())
                .customerJsonStr(JSONUtil.toJsonStr(createReqDTO.getCustomer()))
                .entityJsonStr(JSONUtil.toJsonStr(createReqDTO.getEntity()))
                .build();

        // 发送mq 消息
        invoiceDOs.add(invoiceDTO);

        // 更新账单详情 已开票金额 已可开票金额
        BigDecimal invoicedAmount = billDetailDO.getInvoicedAmount().add(invoiceAmount.getInvoiceAmount());
        BigDecimal availableInvoiceAmount = billDetailDO.getDiscountAmount().subtract(invoicedAmount);

        // 更新账单可开票金额
        billDetailDO.setInvoicedAmount(invoicedAmount);
        billDetailDO.setAvailableInvoiceAmount(availableInvoiceAmount);

        // 发送mq消息
        // 更新账单详情 已开票金额 已可开票金额
        BigDecimal billInvoicedAmount = billDetailDO.getInvoicedAmount().add(invoiceAmount.getInvoiceAmount());
        BigDecimal billAvailableInvoiceAmount = billDetailDO.getDiscountAmount().subtract(invoicedAmount);

        // 更新账单可开票金额
        billDetailDO.setInvoicedAmount(billInvoicedAmount);
        billDetailDO.setAvailableInvoiceAmount(billAvailableInvoiceAmount);
    }

    @Override
    public MakeupIncomeBillDetailVo queryInvoiceBillDetail(InvoiceBillReqDto billReqDto) {
        BillReqDto req = new BillReqDto();
        req.setBillingTime(billReqDto.getBillingTime());
        req.setBillId(billReqDto.getBillId());
        MakeupIncomeBillDetailVo incomeBillDetailVo = queryBill(req, false);
        ChargeInvoiceDO chargeInvoiceDO = invoiceMapper.selectById(billReqDto.getInvoiceId());
        if (chargeInvoiceDO == null) {
            log.info("发票不存在,billId:{}", billReqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_INVOICE_NOT_EXIST);
        }
        assign(chargeInvoiceDO, t -> JSONUtil.toBean(t.getCustomerJsonStr(), ChargeCustomerInvoiceDTO.class), incomeBillDetailVo, MakeupIncomeBillDetailVo::setCustomerInvoice);
        assign(chargeInvoiceDO, t -> JSONUtil.toBean(t.getEntityJsonStr(), EntityDetailsVO.class), incomeBillDetailVo, MakeupIncomeBillDetailVo::setEntityInvoice);
        return incomeBillDetailVo;
    }

    @Override
    public PageResult<IncomeBillHistoryVO<MakeupIncomeBillDetailVo>> historyBills(BillReqHistoryDto reqDto) {
        Page<?> page = MyBatisUtils.buildPage(reqDto);
        List<IncomeBillHistoryDO> historyList = executeIgnore(() -> incomeBillHistoryMapper.listByPage(page, reqDto.getBillId(), reqDto.getBillingTime()));
        List<IncomeBillHistoryVO<MakeupIncomeBillDetailVo>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(historyList)) {
            list = historyList.stream().map(t -> {
                IncomeBillHistoryVO<MakeupIncomeBillDetailVo> convert = IncomeBillHistoryConvert.INSTANCE.convertMakeup(t);
                convert.setBillDetailVo(JSONUtil.toBean(t.getBillDetail(), MakeupIncomeBillDetailVo.class));
                return convert;
            }).toList();
        }
        return MyBatisUtils.convert2PageResult(page, list);
    }

    @Override
    public void deleteBill(BillReqDto reqDto) {
        MakeupIncomeBillDetailVo detailVo = queryBill(reqDto, false);
        if (detailVo == null) {
            log.info("账单不存在,billId:{}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }
        TenantUtils.executeIgnore(() -> makeupBillMapper.deleteByBill(detailVo));
        if (CollectionUtils.isNotEmpty(detailVo.getProducts())) {
            TenantUtils.executeIgnore(() -> makeupProductBillMapper.deleteByProducts(detailVo.getProducts()));
            List<MakeupIncomeProductServiceBillDetailVo> serviceBills = detailVo.getProducts().stream().map(MakeupIncomeProductBilDetailVo::getServices).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).toList();
            if (CollectionUtils.isNotEmpty(serviceBills)) {
                TenantUtils.executeIgnore(() -> makeupProductServiceBillMapper.deleteByServices(serviceBills));
            }
        }
    }

    @Override
    @Transactional
    @TenantIgnore
    public boolean updateBillNotSufficientFundsStatus(Long billId, Integer billStatus, JSONObject billJSON) throws Exception {
        LambdaUpdateChainWrapper<MakeupIncomeBillDO> chainWrapper = new LambdaUpdateChainWrapper<>(MakeupIncomeBillDO.class);

//        long payTime = LocalDateTime.now().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        long payTime = Calendar.getInstance().getTimeInMillis();

        chainWrapper.eq(MakeupIncomeBillDO::getBillId, billId)
                .set(MakeupIncomeBillDO::getBillStatus, billStatus)
                .set(MakeupIncomeBillDO::getWalletDeductStatus, WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())
                .set(MakeupIncomeBillDO::getPaymentTime, payTime);
        int update = TenantUtils.executeIgnore(() -> makeupBillMapper.update(chainWrapper));
        return SqlHelper.retBool(update);
    }

    @Override
    @Transactional
    @TenantIgnore
    public boolean updateBillStatus(Long billId, Integer billStatus, Integer walletsStatus, JSONObject billJSON) throws Exception {
        // 已支付、未支付
        BigDecimal paidAmount = billJSON.getBigDecimal("paidAmount");
        BigDecimal unPaidAmount = billJSON.getBigDecimal("unPaidAmount");

//        long payTime = LocalDateTime.now().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        long payTime = Calendar.getInstance().getTimeInMillis();

        LambdaUpdateChainWrapper<MakeupIncomeBillDO> chainWrapper = new LambdaUpdateChainWrapper<>(MakeupIncomeBillDO.class);
        chainWrapper.eq(MakeupIncomeBillDO::getBillId, billId)
                .set(MakeupIncomeBillDO::getBillStatus, billStatus)
                .set(MakeupIncomeBillDO::getWalletDeductStatus, walletsStatus)
                .set(MakeupIncomeBillDO::getPaidAmount, paidAmount)
                .set(MakeupIncomeBillDO::getUnpaidAmount, unPaidAmount)
                .set(MakeupIncomeBillDO::getPaymentTime, payTime);
        int update = TenantUtils.executeIgnore(() -> makeupBillMapper.update(chainWrapper));
        return SqlHelper.retBool(update);
    }

    @Override
    @Transactional
    @TenantIgnore
    public boolean unSettledBillStatus(Long billId, Integer billStatus, Integer walletsStatus, JSONObject billJSON) throws Exception {
        // 已支付、未支付
        BigDecimal paidAmount = billJSON.getBigDecimal("paidAmount");
        BigDecimal unPaidAmount = billJSON.getBigDecimal("unPaidAmount");
        // 上次支付金额
        BigDecimal lastPaidAmount = billJSON.getBigDecimal("lastPaidAmount");

//        long payTime = LocalDateTime.now().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        long payTime = Calendar.getInstance().getTimeInMillis();

        LambdaUpdateChainWrapper<MakeupIncomeBillDO> chainWrapper = new LambdaUpdateChainWrapper<>(MakeupIncomeBillDO.class);
        chainWrapper.eq(MakeupIncomeBillDO::getBillId, billId)
                .set(MakeupIncomeBillDO::getBillStatus, billStatus)
                .set(MakeupIncomeBillDO::getWalletDeductStatus, walletsStatus)
                .set(MakeupIncomeBillDO::getPaidAmount, paidAmount.add(lastPaidAmount))
                .set(MakeupIncomeBillDO::getUnpaidAmount, unPaidAmount)
                .set(MakeupIncomeBillDO::getPaymentTime, payTime);
        int update = TenantUtils.executeIgnore(() -> makeupBillMapper.update(chainWrapper));
        return SqlHelper.retBool(update);
    }

    @Override
    public boolean refundInvoice(Long billId, Long billingTime, BigDecimal refundAmount) {
        List<MakeupIncomeBillDO> makeupIncomeBillDOS = executeIgnore(() -> makeupBillMapper.queryBillByIds(List.of(billId), billingTime, billingTime));
        if (CollectionUtils.isEmpty(makeupIncomeBillDOS)) {
            log.info("账单不存在,billId:{}", billId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }
        MakeupIncomeBillDO billDO = makeupIncomeBillDOS.getFirst();
        BigDecimal refundInvoiceAmount = billDO.getRefundInvoiceAmount();
        BigDecimal finalRefundAmount = NumberUtil.add(refundInvoiceAmount, refundAmount);
        return executeIgnore(() -> makeupBillMapper.refundInvoice(billId, billingTime, finalRefundAmount) > 0);
    }


    @Override
    public MakeupIncomeBillDetailVo queryBill(BillReqDto reqDto, boolean queryCustomerInfo) {
        // 查询账单信息
        MakeupIncomeBillDO bill = TenantUtils.executeIgnore(
                () -> makeupBillMapper.selectByIdAndTime(reqDto.getBillId(), reqDto.getBillingTime())
        );
        if (bill == null) {
            log.info("账单不存在,billId:{}", reqDto.getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }
        // 将账单信息转换为展示对象
        MakeupIncomeBillDetailVo showVO = MakeupIncomeBillConvert.INSTANCE.convertDetail(bill);
        // 查询服务账单信息
        List<MakeupProductIncomeBillDO> productBills = TenantUtils.executeIgnore(
                () -> makeupProductBillMapper.queryByBillIds(List.of(reqDto.getBillId()), reqDto.getBillingTime(), reqDto.getBillingTime())
        );
        // 查询服务账单信息
        List<MakeupProductServiceIncomeBillDO> serviceBills = TenantUtils.executeIgnore(
                () -> makeupProductServiceBillMapper.queryByBillIds(List.of(reqDto.getBillId()), reqDto.getBillingTime(), reqDto.getBillingTime())
        );
        handleDiscount(showVO, MakeupIncomeBillDetailVo::getDiscountDetails, MakeupIncomeBillDetailVo::setCoupons);
        handBillColumn(showVO);
        if (CollectionUtils.isNotEmpty(productBills) && CollectionUtils.isNotEmpty(serviceBills)) {
            Long billId = showVO.getBillId();
            List<MakeupIncomeProductBilDetailVo> productVos = productBills.stream().filter(productBill -> productBill.getBillId().equals(billId)).map(MakeupIncomeBillConvert.INSTANCE::convertDetail).sorted(Comparator.comparing(MakeupIncomeProductBilDetailVo::getProductBillId)).toList();
            List<MakeupIncomeProductServiceBillDetailVo> serviceVos = serviceBills.stream().filter(serviceBill -> serviceBill.getBillId().equals(billId)).map(MakeupIncomeBillConvert.INSTANCE::convertDetail)
                    .toList();
            for (MakeupIncomeProductBilDetailVo productVo : productVos) {
                List<MakeupIncomeProductServiceBillDetailVo> productServiceVos = serviceVos.stream().filter(serviceVo -> serviceVo.getProductBillId().equals(productVo.getProductBillId())).sorted(Comparator.comparing(MakeupIncomeProductServiceBillDetailVo::getProductServiceBillId)).toList();
                productVo.setServices(productServiceVos);


                // 处理费率详情
                handleDiscount(productServiceVos, MakeupIncomeProductServiceBillDetailVo::getDiscountDetails, MakeupIncomeProductServiceBillDetailVo::setCoupons);
                // 处理费率详情
                handleRateDetail(productServiceVos,
                        MakeupIncomeProductServiceBillDetailVo::getRateDetails,
                        MakeupIncomeProductServiceBillDetailVo::getBillingType,
                        MakeupIncomeProductServiceBillDetailVo::getTaxRate,
                        MakeupIncomeProductServiceBillDetailVo::setFixedRateConfig,
                        MakeupIncomeProductServiceBillDetailVo::setPackageRateConfig,
                        MakeupIncomeProductServiceBillDetailVo::setTierRateConfig,
                        MakeupIncomeProductServiceBillDetailVo::setUsageBasedRateConfig
                );
            }
            showVO.setProducts(productVos);

            handleBillProductServiceColumn(showVO);
        }

        if (queryCustomerInfo) {
            // 查询客户信息
            assign(showVO, MakeupIncomeBillDetailVo::getCustomerId, customerApi::findById, showVO, MakeupIncomeBillDetailVo::setCustomer);
            assign(showVO, MakeupIncomeBillDetailVo::getEntityId, entityApi::findById, showVO, MakeupIncomeBillDetailVo::setEntity);
        }
        processBillContentAndCoupons(showVO);
        return showVO;
    }

    private final BillContentHandler billContentHandler;

    private void processBillContentAndCoupons(MakeupIncomeBillDetailVo showVO) {
        List<BillDiscountConfigDTO> billCoupons = showVO.getCoupons();
        List<BillCoupon> coupons = new ArrayList<>(billContentHandler.handleCoupons(billCoupons, null, null, showVO.getCurrency()));
        List<BillContent> contents = new ArrayList<>();
        for (MakeupIncomeProductBilDetailVo product : showVO.getProducts()) {
            coupons.addAll(billContentHandler.handleCoupons(product.getCoupons(), product.getProductId(), null, showVO.getCurrency()));
            BillContent productContent = new BillContent();
            productContent.setDescription(StringUtils.isNotEmpty(product.getProductName()) ? product.getProductName() : "");
            for (MakeupIncomeProductServiceBillDetailVo service : product.getServices()) {
                coupons.addAll(billContentHandler.handleCoupons(service.getCoupons(), product.getProductId(), service.getServiceId(), showVO.getCurrency()));
                billContentHandler.handleContent(productContent, service);
            }
            contents.add(productContent);
        }
        showVO.setShowCoupons(coupons);
        showVO.setShowContents(contents);
    }

}
