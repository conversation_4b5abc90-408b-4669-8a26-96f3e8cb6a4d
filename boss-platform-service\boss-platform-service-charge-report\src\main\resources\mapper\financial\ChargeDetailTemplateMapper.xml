<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.report.web.financial.mapper.ChargeDetailTemplateMapper">


    <select id="templatePageQuery"
            resultType="com.linkcircle.boss.module.report.web.financial.model.entity.ChargeDetailTemplateDO">
        select *  from charge_detailed_template
        <where>
            <if test="null != req.templateName and ''!= req.templateName">
                and template_name like concat('%',#{req.templateName},'%')
            </if>
            and deleted = 0
        </where>

    </select>
</mapper>