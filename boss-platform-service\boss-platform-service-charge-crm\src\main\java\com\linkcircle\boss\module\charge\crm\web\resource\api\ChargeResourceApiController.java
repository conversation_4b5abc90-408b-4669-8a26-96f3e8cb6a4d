package com.linkcircle.boss.module.charge.crm.web.resource.api;


import com.linkcircle.boss.framework.common.enums.RpcConstants;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.crm.web.resource.service.IChargeResourceService;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.api.resource.ChargeResourceApi;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 资源信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Tag(name = "资源服务")
@RestController
@RequestMapping(RpcConstants.RPC_API_PREFIX + "/charge-resource")
@RequiredArgsConstructor
public class ChargeResourceApiController implements ChargeResourceApi {

    private final IChargeResourceService chargeResourceService;


    @Override
    public CommonResult<List<CommonVO>> findNameByIds(CommonDTO commonDTO) {
        return chargeResourceService.findNameByIds(commonDTO);
    }
}
