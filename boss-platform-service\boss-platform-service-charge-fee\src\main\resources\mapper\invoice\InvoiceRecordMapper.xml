<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.charge.fee.web.invoice.mapper.InvoiceMapper">

    <select id="queryByPage"
            resultType="com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceRecordResVO">
        select t.id as "id",
        t.invoice_id as "invoiceId",
        t.invoice_type as "invoiceType",
        t.entity_id as "entityId",
        t.customer_id as "customerId",
        t.account_id as "accountId",
        t.invoice_amount as "invoiceAmount",
        t.invoice_billing_id as "invoiceBillingId",
        t.status as "status",
        t.currency_symbol as "currencySymbol",
        t.create_time as "createTime",
        t.creator as "creator",
        t.bill_type as "billType",
        t.entity_json_str as "entityJsonStr",
        t.customer_json_str as "customerJsonStr"
        from charge_invoice_record t
        <where>
            t.deleted = 0
            <if test="query.invoiceId != null and query.invoiceId != ''">
                AND t.invoice_id like CONCAT('%',#{query.invoiceId},'%')
            </if>
            <if test="query.invoiceBillingId != null and query.invoiceBillingId != ''">
                AND t.invoice_billing_id like CONCAT('%',#{query.invoiceBillingId},'%')
            </if>
            <if test="query.status != null">
                AND t.status = #{query.status}
            </if>
            <if test="query.entityId != null">
                AND t.enity_id = #{query.entityId}
            </if>
            <if test="query.invoiceType != null">
                AND t.invoice_type = #{query.invoiceType}
            </if>
            <if test="query.creator != null and query.creator != ''">
                AND t.creator like CONCAT('%',#{query.creator},'%')
            </if>
            <if test="query.customerIds != null and query.customerIds.size() > 0">
                and t.customer_id IN
                <foreach item="id" collection="query.customerIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.accountIds != null and query.accountIds.size() > 0">
                and t.account_id IN
                <foreach item="id" collection="query.accountIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by t.create_time DESC
    </select>
</mapper>