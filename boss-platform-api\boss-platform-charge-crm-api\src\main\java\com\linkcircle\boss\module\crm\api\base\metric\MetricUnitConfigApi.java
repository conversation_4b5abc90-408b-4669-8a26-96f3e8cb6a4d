package com.linkcircle.boss.module.crm.api.base.metric;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.base.metric.vo.MetricUnitConfigSimpleVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-01 15:40
 * @description
 */
@FeignClient(name = ApiConstants.NAME,
        path = ApiConstants.PREFIX + "/metric/unit",
        fallbackFactory = MetricUnitConfigApiFallback.class,
        dismiss404 = true)
@Tag(name = "RPC 服务 - 指标单位配置")
public interface MetricUnitConfigApi {

    @GetMapping("/simple-list")
    @Operation(summary = "获取指标单位配置简单列表")
    CommonResult<List<MetricUnitConfigSimpleVO>> getSimpleMetricUnitConfigList();

}
