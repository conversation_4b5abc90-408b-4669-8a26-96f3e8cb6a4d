package com.linkcircle.boss.module.billing.web.data.service.impl;

import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.module.billing.web.data.service.RateCacheDataService;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-06-20 09:09
 * @description 费率缓存数据服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RateCacheDataServiceImpl implements RateCacheDataService {

    private final RedissonUtil redissonUtil;

    /**
     * Hash字段名常量
     */
    private static final String FIELD_TOTAL_USAGE = "totalUsage";
    private static final String FIELD_USAGE_UNIT = "usageUnit";

    @Override
    public boolean isFixedRateBilled(BillTypeEnum billType, Long subscriptionId, String billingCycle) {
        String cacheKey = ChargeCacheUtils.getRateBillingKey(billType.name().toLowerCase(), ChargeRateTypeEnum.FIXED.name().toLowerCase(), String.valueOf(subscriptionId), billingCycle);
        boolean exists = redissonUtil.exists(cacheKey);

        log.info("固定费率计费状态检查 - 账单类型: {}, 订阅ID: {}, 计费周期: {}, 已计费: {}", billType, subscriptionId, billingCycle, exists);
        return exists;
    }

    @Override
    public boolean setFixedRateBillingLock(BillTypeEnum billType, Long subscriptionId, Long serviceId, String billingCycle, Duration ttl) {
        String preventDuplicateKey = ChargeCacheUtils.getBillingPreventDuplicateKey(
                ChargeRateTypeEnum.FIXED.name().toLowerCase(), subscriptionId, serviceId, billingCycle);
        boolean success = redissonUtil.setNx(preventDuplicateKey, System.currentTimeMillis(), ttl);

        log.info("设置固定费率计费锁定 - 账单类型: {}, 订阅ID: {}, 计费周期: {}, 成功: {}", billType, subscriptionId, billingCycle, success);
        return success;
    }

    @Override
    public boolean isPackageRateBilled(BillTypeEnum billType, Long subscriptionId, String billingCycle) {
        String cacheKey = ChargeCacheUtils.getRateBillingKey(billType.name().toLowerCase(), ChargeRateTypeEnum.PACKAGE.name().toLowerCase(), String.valueOf(subscriptionId), billingCycle);
        boolean exists = redissonUtil.exists(cacheKey);

        log.info("套餐费率计费状态检查 - 账单类型: {}, 订阅ID: {}, 计费周期: {}, 已计费: {}", billType, subscriptionId, billingCycle, exists);
        return exists;
    }

    @Override
    public boolean setPackageRateBillingLock(BillTypeEnum billType, Long subscriptionId, String billingCycle, Duration ttl) {
        String cacheKey = ChargeCacheUtils.getRateBillingKey(billType.name().toLowerCase(), ChargeRateTypeEnum.PACKAGE.name().toLowerCase(), String.valueOf(subscriptionId), billingCycle);

        boolean success = redissonUtil.setNx(cacheKey, "1", ttl);

        log.info("设置套餐费率计费锁定 - 账单类型: {}, 订阅ID: {}, 计费周期: {}, 成功: {}", billType, subscriptionId, billingCycle, success);
        return success;
    }

    @Override
    public BigDecimal getRateUsage(BillTypeEnum billType,
                                   ChargeRateTypeEnum rateType,
                                   Long subscriptionId,
                                   Long accountId,
                                   Long serviceId,
                                   String billingCycle) {
        String cacheKey = buildCacheKey(billType, rateType, subscriptionId, accountId, serviceId, billingCycle);
        if (StrUtil.isBlank(cacheKey)) {
            return BigDecimal.ZERO;
        }

        String usageStr = redissonUtil.hget(cacheKey, FIELD_TOTAL_USAGE);
        BigDecimal usage = StrUtil.isBlank(usageStr) ? BigDecimal.ZERO : new BigDecimal(usageStr);

        log.info("获取{}累计用量 - 账单类型: {}, 缓存Key: {}, 用量: {}", rateType.getName(), billType, cacheKey, usage);
        return usage;
    }

    @Override
    public BigDecimal addRateUsage(BillTypeEnum billType,
                                   ChargeRateTypeEnum rateType,
                                   Long subscriptionId,
                                   Long accountId,
                                   Long serviceId,
                                   String billingCycle,
                                   BigDecimal usage,
                                   String usageUnit,
                                   Duration ttl,
                                   Boolean inTrail) {
        if (Boolean.TRUE.equals(inTrail)) {
            return usage;
        }
        String cacheKey = buildCacheKey(billType, rateType, subscriptionId, accountId, serviceId, billingCycle);
        if (StrUtil.isBlank(cacheKey) || usage == null) {
            log.warn("参数为空");
            return BigDecimal.ZERO;
        }

        // 更新缓存
        Object newTotalUsage = redissonUtil.hincrby(cacheKey, FIELD_TOTAL_USAGE, usage.longValue());
        // 设置过期时间
        redissonUtil.expire(cacheKey, ttl);

        log.info("增加{}用量 - 账单类型: {}, 缓存Key: {}, 本次用量: {}, 累计用量: {}", rateType.getName(), billType, cacheKey, usage, newTotalUsage);

        return new BigDecimal(newTotalUsage.toString());
    }

    @Override
    public void deleteRateCache(BillTypeEnum billType,
                                ChargeRateTypeEnum rateType,
                                Long subscriptionId,
                                Long accountId,
                                Long serviceId,
                                String billingCycle) {
        String cacheKey = buildCacheKey(billType, rateType, subscriptionId, accountId, serviceId, billingCycle);
        if (StrUtil.isBlank(cacheKey)) {
            return;
        }

        redissonUtil.delete(cacheKey);

        log.info("删除{}缓存 - 账单类型: {}, 缓存Key: {}", rateType.getName(), billType, cacheKey);
    }

    /**
     * 构建缓存Key
     */
    private String buildCacheKey(BillTypeEnum billType, ChargeRateTypeEnum rateType, Long subscriptionId, Long accountId, Long serviceId, String billingCycle) {
        if (billType == null || rateType == null || StrUtil.isBlank(billingCycle)) {
            log.warn("账单类型、费率类型或计费周期为空");
            return null;
        }

        String dimensionKey;
        switch (rateType) {
            case FIXED:
            case TIERED:
            case PACKAGE:
                dimensionKey = String.valueOf(subscriptionId);
                break;
            case USAGE:
                if (Objects.isNull(accountId) || Objects.isNull(serviceId)) {
                    log.warn("账户ID或服务ID为空");
                    return null;
                }
                dimensionKey = accountId + ":" + serviceId;
                break;
            default:
                log.warn("不支持的费率类型: {}", rateType);
                return null;
        }

        return ChargeCacheUtils.getRateBillingKey(billType.name().toLowerCase(), rateType.name().toLowerCase(), dimensionKey, billingCycle);
    }
}
