package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.controller;

import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageParam;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.excel.core.util.ExcelUtils;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.CreditInvoiceDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.InvoiceRecordReqDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.InvoiceSendMailDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.InvoiceToExamineDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceDetailsResVO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceRecordResVO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.service.InvoiceRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.linkcircle.boss.framework.common.model.CommonResult.success;

/**
 * 发票记录控制器类
 *
 * <AUTHOR> zyuan
 * @data : 2025-06-30
 */
@Tag(name = "发票 - 开票记录")
@RestController
@RequestMapping("/invoice-record")
@Validated
public class InvoiceRecordController {

    @Resource
    private InvoiceRecordService invoiceRecordService;

    // 发票分页记录查询
    @GetMapping("/page")
    @Operation(summary = "获取发票分页列表")
//    @PreAuthorize("@ss.hasPermission('invoice:invoice-record:list')")
    public CommonResult<PageResult<InvoiceRecordResVO>> getPage(@ParameterObject InvoiceRecordReqDTO queryDTO) {
        PageResult<InvoiceRecordResVO> pageResult = invoiceRecordService.getPage(queryDTO);
        return success(pageResult);
    }

    // 查看发票详情
    @GetMapping("/details/{id}")
    @Operation(summary = "获取发票详情信息")
//    @PreAuthorize("@ss.hasPermission('invoice:invoice-record:details')")
    public CommonResult<InvoiceDetailsResVO> details(@Parameter(description = "id", required = true, example = "10239089001")
                                                     @PathVariable Long id) {
        InvoiceDetailsResVO vo = invoiceRecordService.details(id);
        return CommonResult.success(vo);
    }

    // 发票审核
    @PostMapping("/toExamine")
    @Operation(summary = "审核发票")
//    @PreAuthorize("@ss.hasPermission('invoice:invoice-record:toExamine')")
    public CommonResult<Long> toExamine(@RequestBody InvoiceToExamineDTO dto) {
        return invoiceRecordService.toExamine(dto);
    }

    // 发送邮箱
    @PostMapping("/sendMail")
    @Operation(summary = "发送邮箱")
//    @PreAuthorize("@ss.hasPermission('invoice:invoice-record:sendMail')")
    public CommonResult<Boolean> sendMail(@RequestBody InvoiceSendMailDTO dto) {
        Boolean vo = invoiceRecordService.sendMail(dto);
        return CommonResult.success(vo);
    }

    // 开具信用票据
    @PostMapping("/creditInvoice")
    @Operation(summary = "开具信用票据")
//    @PreAuthorize("@ss.hasPermission('invoice:invoice-record:sendMail')")
    public CommonResult<String> creditInvoice(@RequestBody CreditInvoiceDTO dto) {
        return invoiceRecordService.creditInvoice(dto);
    }

    // 红冲

    // 发票下载
    @GetMapping("/download/{id}")
    @Operation(summary = "发票下载")
//    @PreAuthorize("@ss.hasPermission('invoice:invoice-record:download')")
    public void download(@PathVariable Long id, HttpServletResponse response) {
        try {
            invoiceRecordService.download(id, response);
        } catch (Exception e) {
            throw new ServiceException();
        }
    }

    // 发票记录导出
    @GetMapping("/report")
    @Operation(summary = "发票记录导出")
//    @PreAuthorize("@ss.hasPermission('invoice:invoice-record:report')")
    public void report(@ParameterObject InvoiceRecordReqDTO queryDTO, HttpServletResponse response) {
        try {
            queryDTO.setPageSize(PageParam.PAGE_SIZE_NONE);
            List<InvoiceRecordResVO> list = invoiceRecordService.getPage(queryDTO).getList();
            ExcelUtils.write(response, "发票信息.xls", "发票信息列表", InvoiceRecordResVO.class, list);
        } catch (Exception e) {
            throw new ServiceException();
        }
    }
}
