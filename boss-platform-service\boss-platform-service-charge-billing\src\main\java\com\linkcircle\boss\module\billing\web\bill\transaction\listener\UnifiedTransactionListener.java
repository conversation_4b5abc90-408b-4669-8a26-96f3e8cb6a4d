package com.linkcircle.boss.module.billing.web.bill.transaction.listener;

import com.linkcircle.boss.module.billing.constants.BillingConstant;
import com.linkcircle.boss.module.billing.web.bill.transaction.processor.TransactionProcessor;
import com.linkcircle.boss.module.billing.web.bill.transaction.router.TransactionRouter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQTransactionListener;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionListener;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionState;
import org.springframework.messaging.Message;

/**
 * <AUTHOR>
 * @date 2025-07-29 10:50
 * @description 统一事务监听器 - 解决多个RocketMQLocalTransactionListener冲突问题
 */
@Slf4j
@RequiredArgsConstructor
@RocketMQTransactionListener(maximumPoolSize = 8)
public class UnifiedTransactionListener implements RocketMQLocalTransactionListener {

    private final TransactionRouter transactionRouter;

    /**
     * 执行本地事务
     */
    @Override
    public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
        try {
            // 从消息头获取事务类型
            // String transactionType = getTransactionType(msg);
            // log.info("开始执行本地事务，事务类型: {}", transactionType);
            //
            // // 获取对应的事务处理器
            // TransactionProcessor processor = transactionRouter.getProcessor(transactionType);
            //
            // // 执行具体的事务处理
            // RocketMQLocalTransactionState result = processor.executeLocalTransaction(msg, arg);
            //
            // log.info("本地事务执行完成，事务类型: {}, 结果: {}", transactionType, result);
            return RocketMQLocalTransactionState.COMMIT;
        } catch (Exception e) {
            log.error("执行本地事务异常", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }

    /**
     * 检查本地事务状态（用于消息回查）
     */
    @Override
    public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
        try {
            // 从消息头获取事务类型
            String transactionType = getTransactionType(msg);
            log.info("开始检查本地事务状态，事务类型: {}", transactionType);

            // 获取对应的事务处理器
            TransactionProcessor processor = transactionRouter.getProcessor(transactionType);

            // 检查具体的事务状态
            RocketMQLocalTransactionState result = processor.checkLocalTransaction(msg);

            log.info("本地事务状态检查完成，事务类型: {}, 结果: {}", transactionType, result);
            return result;
        } catch (Exception e) {
            log.error("检查本地事务状态异常", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }

    /**
     * 从消息头获取事务类型
     *
     * @param msg 消息
     * @return 事务类型
     */
    @SuppressWarnings("rawtypes")
    private String getTransactionType(Message msg) {
        Object transactionType = msg.getHeaders().get(BillingConstant.HEADER_TRANSACTION_TYPE);
        if (transactionType == null) {
            log.error("消息头中未找到事务类型标识");
            throw new IllegalArgumentException("消息头中未找到事务类型标识");
        }
        return transactionType.toString();
    }
}
