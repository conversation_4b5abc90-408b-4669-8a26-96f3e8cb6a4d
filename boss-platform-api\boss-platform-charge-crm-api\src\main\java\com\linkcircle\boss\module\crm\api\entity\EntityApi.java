package com.linkcircle.boss.module.crm.api.entity;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.CommonApi;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-19 15:43
 * @description
 */
@FeignClient(name = ApiConstants.NAME,path = ApiConstants.PREFIX + "/basic-config",fallback = EntityApiFallback.class)
@Tag(name = "RPC 服务 - 主体信息信息")
public interface EntityApi extends CommonApi {




    @PostMapping("/findNameByIds")
    @Operation(summary = "根据id 获取 名称 信息")
    CommonResult<List<CommonVO>> findNameByIds(@RequestBody CommonDTO commonDTO);


    @GetMapping("/findById")
    @Operation(summary = "查询客户")
    public CommonResult<EntityDetailsVO> findById(@RequestParam("id") Long id);
}
