package com.linkcircle.boss.module.report.web.bill.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.*;
import com.linkcircle.boss.module.crm.api.basicConfig.BasicConfigApi;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import com.linkcircle.boss.module.crm.api.productservice.ChargeProductServiceApi;
import com.linkcircle.boss.module.crm.api.productservice.vo.ProductServiceVO;
import com.linkcircle.boss.module.report.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.report.enums.BillEnum;
import com.linkcircle.boss.module.report.enums.DownLoadEnum;
import com.linkcircle.boss.module.report.web.bill.convert.PrepaidIncomeBillDetailConvert;
import com.linkcircle.boss.module.report.web.bill.mapper.PrepaidIncomeBillDetailMapper;
import com.linkcircle.boss.module.report.web.bill.model.dto.BillQueryByCustomerIdsReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.makeup.BillQueryPageReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.prepaid.PrepaidDetailReqDto;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.postpaid.PostpaidBillExportVO;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.postpaid.PostpaidBillExportVOConvert;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.prepaid.PrepaidBillExportVO;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.prepaid.PrepaidBillExportVOConvert;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.report.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.postpaid.PostpaidIncomeServiceVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.prepaid.PrepaidIncomeBillDetailVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.prepaid.PrepaidIncomeBillPageVO;
import com.linkcircle.boss.module.report.web.bill.service.BillContentHandler;
import com.linkcircle.boss.module.report.web.bill.service.PrepaidBillService;
import com.linkcircle.boss.module.report.web.download.model.dto.OnlineExportEnhanceDTO;
import com.linkcircle.boss.module.report.web.download.service.DownLoadService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/16 15:44
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PrepaidBillServiceImpl implements PrepaidBillService {


    private final PrepaidIncomeBillDetailMapper prepaidIncomeBillDetailMapper;


    private final CustomerApi customerApi;

    private final EntityApi entityApi;

    private final ChargeProductServiceApi chargeProductServiceApi;

    private final DownLoadService downLoadService;
    private  final BasicConfigApi basicConfigApi;
    /**
     * 获取所有服务的服务代码列表。
     *
     * @return 服务代码列表，如果调用API失败则返回空列表。
     */
    public List<String> serviceCodes() {
        // 调用API获取所有服务信息
        CommonResult<List<ProductServiceVO>> listCommonResult = chargeProductServiceApi.allService();
        // 判断API调用是否成功
        if (listCommonResult.isSuccess()) {
            return listCommonResult.getData().stream()
                    .map(ProductServiceVO::getServiceCode).filter(StringUtils::isNotBlank).distinct().toList();
        }
        // 如果API调用失败，则返回空列表
        return new ArrayList<>();
    }

    static final Pattern TABLE_NAME_PATTERN = Pattern.compile("Table \\[(.*?)\\] does not exist");

    public static String extractTableName(String message) {
        Matcher matcher = TABLE_NAME_PATTERN.matcher(message);
        if (matcher.find()) {
            return matcher.group(1); // 返回捕获组1的内容，即表名称
        }
        return null; // 如果没有找到匹配项，则返回null
    }

    /**
     * 查询预付费收入账单详情分页信息
     *
     * @param reqDTO 请求参数
     * @return 预付费收入账单详情分页结果
     */
    @Override
    public PageResult<PrepaidIncomeBillPageVO> queryBillPage(BillQueryPageReqDTO reqDTO) {
        // 构建分页对象
        Page<?> page = MyBatisUtils.buildPage(reqDTO);
        initPageQueryTime(reqDTO);
        List<PrepaidIncomeBillDetailDO> list = null;
        try {
            // 执行查询，忽略异常
            list = executeIgnore(
                    reqDTO.getStartTimeLong(), reqDTO.getEndTimeLong()
                    , serviceCodes(), BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode()
                    , () -> prepaidIncomeBillDetailMapper.queryBillPage(page, reqDTO)
            );
        } catch (Exception e) {
            handleTableNotExist(e,true);
        }
        // 创建结果集
        List<PrepaidIncomeBillPageVO> vos = new ArrayList<>();
        // 判断查询结果是否为空
        if (CollectionUtils.isNotEmpty(list)) {
            // 将DO对象转换为VO对象
            vos = list.stream().map(PrepaidIncomeBillDetailConvert.INSTANCE::convert).toList();
            // 处理账单列
            handBillColumn(vos);
        }

        // 将分页对象转换为分页结果对象
        return MyBatisUtils.convert2PageResult(page, vos);
    }

    @Override
    public PageResult<PrepaidIncomeBillPageVO> queryBillPageByCustomerIds(BillQueryByCustomerIdsReqDTO pageReq) {

            // 构建分页对象
            Page<?> page = MyBatisUtils.buildPage(pageReq);
        List<Long> customerIds = processIds(pageReq.getCustomerIds());
        List<Long> accountIds = processIds(pageReq.getAccountIds());
            List<PrepaidIncomeBillDetailDO> list = null;
            // 执行查询并忽略执行中的异常
        try {
            // 执行查询，忽略异常
            list = executeIgnore(
                    0L, System.currentTimeMillis()
                    , serviceCodes(), BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode()
                    , () -> prepaidIncomeBillDetailMapper.queryBillPageByCustomerIds(page, customerIds,accountIds)
            );
        } catch (Exception e) {
            handleTableNotExist(e,true);
        }
        // 创建结果集
        List<PrepaidIncomeBillPageVO> vos = new ArrayList<>();
        // 判断查询结果是否为空
        if (CollectionUtils.isNotEmpty(list)) {
            // 将DO对象转换为VO对象
            vos = list.stream().map(PrepaidIncomeBillDetailConvert.INSTANCE::convert).toList();
            // 处理账单列
            handBillColumn(vos);
        }
        // 将分页对象转换为分页结果对象
        return MyBatisUtils.convert2PageResult(page, vos);

    }

    private void handleTableNotExist(Exception e,boolean throwException){
        String tableName = extractTableName(e.getMessage());
        if (StringUtils.isNotEmpty(tableName) && tableName.contains(BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode())) {
            String[] arr = tableName.split("_" + BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode() + "_", -1);
            String serviceCode = arr[0];
            String year = arr[1];
            log.info("预付费收入账单详情表不存在，serviceCode:{},year:{}", serviceCode, year);
            if (throwException) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_TABLE_NOT_EXIST, serviceCode, year);
            }
        }
        throw  new RuntimeException(e);
    }


    /**
     * 根据账单详情ID查询账单信息
     *
     * @param detailDto 账单详情ID
     * @return 返回查询到的账单信息，封装在 AccountPrepaidIncomeBillDetailShowVO 对象中
     */
    @Override
    public PrepaidIncomeBillDetailVO queryBill(PrepaidDetailReqDto detailDto) {
        // 执行查询账单详情操作
        PrepaidIncomeBillDetailDO bill = null;
        try {
            bill = executeIgnore(detailDto.getBillingTime(), StringUtils.isNotBlank(detailDto.getServiceCode())?List.of(detailDto.getServiceCode()):serviceCodes(),
                    BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(),
                    () -> prepaidIncomeBillDetailMapper.queryById(detailDto.getBillDetailId())
            );
        } catch (Exception e) {
            handleTableNotExist(e,false);
        }
        // 判断查询结果是否为空
        if (bill == null) {
            log.error("预付费收入账单详情不存在，billDetailId:{}", detailDto.getBillDetailId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PREPAID_INCOME_BILL_NOT_EXIST, detailDto.getBillDetailId());
        }

        // 将查询到的账单详情转换为 AccountPrepaidIncomeBillDetailShowVO 对象
        PrepaidIncomeBillDetailVO showVO = PrepaidIncomeBillDetailConvert.INSTANCE.convertDetail(bill);
        handSingleBillColumn(showVO);
        // 处理账单费率详情信息
        handleRateDetail(List.of(showVO),PrepaidIncomeBillDetailVO::getRateDetails,PrepaidIncomeBillDetailVO::getBillingType
                ,PrepaidIncomeBillDetailVO::getTaxRate
                ,PrepaidIncomeBillDetailVO::setFixedRateConfig,PrepaidIncomeBillDetailVO::setPackageRateConfig,
                PrepaidIncomeBillDetailVO::setTierRateConfig, PrepaidIncomeBillDetailVO::setUsageBasedRateConfig);
        // 根据账单的客户ID查询客户信息
        CommonResult<ChargeCustomerInfoVO> commonResult = customerApi.findById(bill.getCustomerId());
        // 如果查询成功
        if (commonResult.isSuccess()) {
            // 获取客户信息
            ChargeCustomerInfoVO customer = commonResult.getData();
            // 设置客户信息到 AccountPrepaidIncomeBillDetailShowVO 对象中
            showVO.setCustomer(customer);
        }

        // 根据账单的客户ID查询实体信息
        CommonResult<EntityDetailsVO> entityApiById = entityApi.findById(bill.getEntityId());
        // 如果查询成功
        if (entityApiById.isSuccess()) {
            // 获取实体信息
            EntityDetailsVO entity = entityApiById.getData();
            // 设置实体信息到 AccountPrepaidIncomeBillDetailShowVO 对象中
            showVO.setEntity(entity);
        }

        // 返回 AccountPrepaidIncomeBillDetailShowVO 对象
        processBillConfigDetail(showVO,PrepaidIncomeBillDetailVO::getEntityId, PrepaidIncomeBillDetailVO::setBillConfigDetails);
        processBillContentAndCoupons(showVO);
        return showVO;
    }


    private final BillContentHandler billContentHandler;


    private void processBillContentAndCoupons(PrepaidIncomeBillDetailVO showVO) {
        List<BillDiscountConfigDTO> billCoupons = showVO.getCoupons();
        List<BillCoupon> coupons = new ArrayList<>(billContentHandler.handleCoupons(billCoupons, showVO.getProductId(), showVO.getServiceId(), showVO.getCurrency()));
        BillContent productContent = new BillContent();
        productContent.setDescription(StringUtils.isNotEmpty(showVO.getProductName())?showVO.getProductName():"");
        billContentHandler.handleContent(productContent,showVO);
        showVO.setShowCoupons(coupons);
        showVO.setShowContents(List.of(productContent));
    }

    public<T> void processBillConfigDetail(T showVO, Function<T,Long> entityIdMapper, BiConsumer<T, InvoiceDetailsVO> handler){
        CommonResult<InvoiceDetailsVO> invoiceDetailsVOCommonResult = basicConfigApi.queryInvoice(entityIdMapper.apply(showVO), 1);
        if(invoiceDetailsVOCommonResult.isSuccess()){
            InvoiceDetailsVO invoiceDetailsVO = invoiceDetailsVOCommonResult.getData();
            if(invoiceDetailsVO!=null){
                handler.accept(showVO,invoiceDetailsVO);
            }
        }
    }

    @Override
    public void exportBill(BillQueryPageReqDTO reqDTO, HttpServletResponse response) {
        try {
            // ③ 将查询结果转换为导出所需的VO列表
            OnlineExportEnhanceDTO<PrepaidIncomeBillPageVO, PrepaidBillExportVO> online = new OnlineExportEnhanceDTO<>();
            online.pageSize(DownLoadEnum.PAGE_SIZE_1000)
                    .fileName("后付费收入账单导出")
                    .sheetName("账单列表")
                    .exportClass(PrepaidBillExportVO.class)
                    .response(response)
                    .pageParam(reqDTO)
                    .queryPageFunc(p -> queryBillPage((BillQueryPageReqDTO) p))
                    .convertFun(PrepaidBillExportVOConvert.INSTANCE::convert)
                    .handle(ts -> {});
            downLoadService.exportOnline(online);
        } catch (Exception e) {
            log.error("导出供应商信息异常", e);
            // ⑤ 如果发生IO异常，则抛出ServiceException异常
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_BILL_EXPORT_ERROR);
        }
    }


}
