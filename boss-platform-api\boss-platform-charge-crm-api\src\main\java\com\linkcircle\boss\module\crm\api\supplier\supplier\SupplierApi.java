package com.linkcircle.boss.module.crm.api.supplier.supplier;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.CommonApi;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.api.supplier.supplier.vo.SupplierListRespVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-24 15:00
 * @description 供应商账户API
 */
@FeignClient(name = ApiConstants.NAME, path = ApiConstants.PREFIX + "/supplier",fallback = SupplierApiFallback.class)
@Tag(name = "RPC 服务 - 供应商 信息")
public interface SupplierApi extends CommonApi {

    @PostMapping("/findNameByIds")
    @Operation(summary = "根据id 获取 名称 信息")
    CommonResult<List<CommonVO>> findNameByIds(@RequestBody CommonDTO commonDTO);

    @GetMapping("/listSuppliers")
    @Operation(summary = "列出所有供应商信息及其账户的信息")
    CommonResult<List<SupplierListRespVO>> listSuppliers();
}
