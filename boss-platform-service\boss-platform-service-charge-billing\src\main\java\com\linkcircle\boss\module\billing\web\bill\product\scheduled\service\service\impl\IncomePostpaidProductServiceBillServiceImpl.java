package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PostpaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.enums.BillingProcessStatusEnum;
import com.linkcircle.boss.module.billing.web.bill.product.mapper.PostpaidProductServiceIncomeBillMapper;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service.IncomePostpaidProductServiceBillService;
import com.linkcircle.boss.module.billing.web.detail.income.mapper.PostpaidIncomeBillDetailMapper;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-07 16:12
 * @description 后付费产品服务账单服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomePostpaidProductServiceBillServiceImpl implements IncomePostpaidProductServiceBillService {

    private final PostpaidProductServiceIncomeBillMapper postpaidProductServiceIncomeBillMapper;
    private final PostpaidIncomeBillDetailMapper postpaidIncomeBillDetailMapper;

    @Override
    public boolean saveServiceBill(PostpaidProductServiceIncomeBillDO bill) {
        if (bill == null) {
            log.warn("服务账单为空，跳过保存");
            return false;
        }

        try {
            int insertCount = postpaidProductServiceIncomeBillMapper.insert(bill);
            boolean success = insertCount > 0;
            log.info("保存单个服务账单, billId: {}, 结果: {}", bill.getProductServiceBillId(), success);
            return success;
        } catch (Exception e) {
            log.error("保存单个服务账单失败, billId: {}", bill.getProductServiceBillId(), e);
            throw e;
        }
    }

    @Override
    public boolean isBillExists(String serviceCode,
                                Long subscriptionId,
                                Long billingStartTime,
                                Long billingEndTime) {
        LambdaQueryWrapper<PostpaidProductServiceIncomeBillDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(PostpaidProductServiceIncomeBillDO::getProductServiceBillId);
        wrapper.eq(PostpaidProductServiceIncomeBillDO::getServiceCode, serviceCode)
                .eq(PostpaidProductServiceIncomeBillDO::getSubscribeId, subscriptionId)
                .ge(PostpaidProductServiceIncomeBillDO::getBillingStartTime, billingStartTime)
                .le(PostpaidProductServiceIncomeBillDO::getBillingEndTime, billingEndTime);
        PostpaidProductServiceIncomeBillDO bill = postpaidProductServiceIncomeBillMapper.selectOne(wrapper);
        return Objects.nonNull(bill);
    }

    @Override
    public boolean updateBillStatus(Long billId, BillingProcessStatusEnum status) {
        try {
            LambdaUpdateWrapper<PostpaidProductServiceIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, billId)
                    .set(PostpaidProductServiceIncomeBillDO::getBillingStatus, status.getStatus());
            int updateCount = postpaidProductServiceIncomeBillMapper.update(null, wrapper);
            boolean success = updateCount > 0;
            log.info("更新服务账单状态, billId: {}, status: {}, 结果: {}", billId, status, success);
            return success;
        } catch (Exception e) {
            log.error("更新服务账单状态异常, billId: {}, status: {}", billId, status, e);
            return false;
        }
    }

    @Override
    public PostpaidProductServiceIncomeBillDO getBillById(Long billId, Long startTime) {
        try {
            LambdaQueryWrapper<PostpaidProductServiceIncomeBillDO> wrapper = Wrappers.lambdaQuery(PostpaidProductServiceIncomeBillDO.class)
                    .eq(PostpaidProductServiceIncomeBillDO::getProductServiceBillId, billId)
                    .gt(PostpaidProductServiceIncomeBillDO::getBillingStartTime, startTime)
                    .last("limit 1");
            PostpaidProductServiceIncomeBillDO bill = postpaidProductServiceIncomeBillMapper.selectOne(wrapper);
            log.info("根据ID查询服务账单, billId: {}, 结果: {}", billId, bill != null ? "找到" : "未找到");
            return bill;
        } catch (Exception e) {
            log.error("根据ID查询服务账单异常, billId: {}", billId, e);
            return null;
        }
    }

    @Override
    public boolean updateServiceBillFeeInfo(PostpaidProductServiceIncomeBillDO serviceIncomeBillDO) {
        try {
            int updateCount = postpaidProductServiceIncomeBillMapper.updateById(serviceIncomeBillDO);
            boolean success = updateCount > 0;
            log.info("更新服务账单费用信息, billId: {}, usage: {}, originalPrice: {}, 结果: {}",
                    serviceIncomeBillDO.getProductServiceBillId(),
                    serviceIncomeBillDO.getUsageCount(),
                    serviceIncomeBillDO.getOriginalPrice(),
                    success);
            return success;
        } catch (Exception e) {
            log.error("更新服务账单费用信息异常, billId: {}", serviceIncomeBillDO.getProductServiceBillId(), e);
            return false;
        }
    }

    @Override
    public PostpaidIncomeBillDetailDO getTotalUsageByServiceAndTime(Integer billingType,
                                                                    Long subscriptionId,
                                                                    String serviceCode,
                                                                    Long billingStartTime,
                                                                    Long billingEndTime) {
        try {
            HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(
                    List.of(billingStartTime, billingEndTime),
                    List.of(getServiceCode(billingType, serviceCode))
            );

            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.addTableShardingValue(LogicTableConstant.POSTPAID_INCOME_BILL_DETAIL, businessTimeDTO);

                PostpaidIncomeBillDetailDO detailDO = postpaidIncomeBillDetailMapper.sumUsageByServiceAndTime(subscriptionId, serviceCode,
                        billingStartTime, billingEndTime);

                log.info("统计使用量完成, subscriptionId: {}, serviceCode: {}, 时间范围: {} - {}, totalUsage: {}",
                        subscriptionId, serviceCode, billingStartTime, billingEndTime, detailDO.getChargeUsageCount());
                return detailDO;
            }
        } catch (Exception e) {
            log.error("统计使用量异常, subscriptionId: {}, serviceCode: {}, 时间范围: {} - {}",
                    subscriptionId, serviceCode, billingStartTime, billingEndTime, e);
            return null;
        }
    }

    private String getServiceCode(Integer billingType, String serviceCode) {
        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(billingType);
        switch (rateTypeEnum) {
            case FIXED, TIERED -> {
                return rateTypeEnum.name().toLowerCase();
            }
            case USAGE, PACKAGE -> {
                return serviceCode;
            }
        }
        return "";
    }
}
