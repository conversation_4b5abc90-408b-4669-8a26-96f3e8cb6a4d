package com.linkcircle.boss.module.billing.web.bill.transaction.router;

import com.linkcircle.boss.module.billing.web.bill.transaction.enums.TransactionMessageTypeEnum;
import com.linkcircle.boss.module.billing.web.bill.transaction.processor.TransactionProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-29 10:50
 * @description 事务路由器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransactionRouter {

    private final List<TransactionProcessor> processors;
    private Map<TransactionMessageTypeEnum, TransactionProcessor> processorMap;

    /**
     * 初始化处理器映射
     */
    private void initProcessorMap() {
        if (processorMap == null) {
            processorMap = processors.stream()
                    .collect(Collectors.toMap(
                            TransactionProcessor::getSupportedType,
                            Function.identity(),
                            (existing, replacement) -> {
                                log.warn("发现重复的事务处理器类型: {}, 使用第一个处理器: {}",
                                        existing.getSupportedType(), existing.getClass().getSimpleName());
                                return existing;
                            }
                    ));
            log.info("初始化事务处理器映射完成，共注册 {} 个处理器", processorMap.size());
        }
    }

    /**
     * 根据事务类型获取对应的处理器
     *
     * @param type 事务消息类型
     * @return 事务处理器
     */
    public TransactionProcessor getProcessor(TransactionMessageTypeEnum type) {
        initProcessorMap();
        TransactionProcessor processor = processorMap.get(type);
        if (processor == null) {
            log.error("未找到事务类型 {} 对应的处理器", type);
            throw new IllegalArgumentException("未找到事务类型对应的处理器: " + type);
        }
        return processor;
    }

    /**
     * 根据消息头获取事务类型并返回对应处理器
     *
     * @param transactionType 事务类型字符串
     * @return 事务处理器
     */
    public TransactionProcessor getProcessor(String transactionType) {
        TransactionMessageTypeEnum type = TransactionMessageTypeEnum.getByCode(transactionType);
        if (type == null) {
            log.error("未知的事务类型: {}", transactionType);
            throw new IllegalArgumentException("未知的事务类型: " + transactionType);
        }
        return getProcessor(type);
    }
}
