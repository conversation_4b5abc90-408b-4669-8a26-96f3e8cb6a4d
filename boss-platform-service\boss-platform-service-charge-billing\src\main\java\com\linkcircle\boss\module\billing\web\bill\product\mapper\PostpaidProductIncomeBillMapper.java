package com.linkcircle.boss.module.billing.web.bill.product.mapper;

import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025-06-18 08:59
 * @description 后付费产品收入账单表 Mapper
 */
@Mapper
public interface PostpaidProductIncomeBillMapper extends BaseMapperX<PostpaidProductIncomeBillDO> {

    /**
     * 检查产品账单是否存在
     */
    PostpaidProductIncomeBillDO existsByProductAndPeriod(@Param("productId") Long productId,
                                                         @Param("subscriptionId") Long subscriptionId,
                                                         @Param("startTime") Long startTime,
                                                         @Param("endTime") Long endTime);
}
