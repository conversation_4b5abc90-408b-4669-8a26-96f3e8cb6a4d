-- drop table charge_product_service_price;
-- drop table charge_product;
-- drop table charge_product_service;
-- drop table charge_service_resources;

CREATE TABLE `charge_product`
(
    `id`            bigint(20)  NOT NULL COMMENT '主键id',
    `product_name`  varchar(64)          DEFAULT NULL COMMENT '产品名称',
    `product_code`  varchar(256)         DEFAULT NULL COMMENT '产品编码',
    `currency_code` varchar(64)          DEFAULT NULL COMMENT '货币编码',
    `remark`        varchar(255)         DEFAULT NULL COMMENT '备注信息',
    `status`        tinyint(1)           DEFAULT NULL COMMENT '状态，0：未激活，1：激活，2：存档',
    `creator`       VARCHAR(64) NOT NULL COMMENT '创建者',
    `create_time`   BIGINT(20)  NOT NULL COMMENT '创建时间',
    `updater`       VARCHAR(64) NOT NULL COMMENT '更新者',
    `update_time`   BIGINT(20)  NOT NULL COMMENT '更新时间',
    `deleted`       BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`     BIGINT(20)  NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `udx_product_name` (`product_name`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='产品信息管理';


-- 服务基本信息表
CREATE TABLE charge_product_service
(
    `id`          bigint(20)  NOT NULL COMMENT '主键id',
    `product_id`  bigint               DEFAULT NULL COMMENT '产品标识',
    `service_id`  bigint      NOT NULL COMMENT '服务名称',
    `creator`     VARCHAR(64) NOT NULL COMMENT '创建者',
    `create_time` BIGINT(20)  NOT NULL COMMENT '创建时间',
    `updater`     VARCHAR(64) NOT NULL COMMENT '更新者',
    `update_time` BIGINT(20)  NOT NULL COMMENT '更新时间',
    `deleted`     BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`   BIGINT(20)  NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='产品与服务关联表';


-- 服务和资源对应关系表
CREATE TABLE charge_service_resources
(
    `id`           BIGINT(20)  NOT NULL COMMENT '主键id',
    `service_id`   BIGINT(20)  NOT NULL COMMENT '关联产品服务ID',
    `resources_id` BIGINT      NOT NULL COMMENT '资源id',
    `creator`      VARCHAR(64) NOT NULL COMMENT '创建者',
    `create_time`  BIGINT(20)  NOT NULL COMMENT '创建时间',
    `updater`      VARCHAR(64) NOT NULL COMMENT '更新者',
    `update_time`  BIGINT(20)  NOT NULL COMMENT '更新时间',
    `deleted`      BIT(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`    BIGINT(20)  NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_service_id` (`service_id`),
    KEY `idx_resources_id` (`resources_id`)
) ENGINE = INNODB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT = '服务和资源对应关系表;可一对多';


-- 服务信息表
CREATE TABLE `charge_product_service_price`
(
    `id`                  BIGINT(20)   NOT NULL COMMENT '主键id',
    service_name          VARCHAR(256) NOT NULL COMMENT '服务名称',
    service_code          VARCHAR(256) NOT NULL COMMENT '服务编码',
    version_name          VARCHAR(24)  NOT NULL COMMENT '版本名称（如v1.0.0 满足规范便于校验）',
    version_order         INT          NOT NULL COMMENT '版本号排序使用',
    `description`         VARCHAR(255)          DEFAULT NULL COMMENT '服务描述',
    `charge_type`         TINYINT(255)          DEFAULT NULL COMMENT '0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费',
    payment_options       TINYINT(255)          DEFAULT NULL COMMENT '支付方式，0:现金，1：积分，2：全选',
    `unit_label`          VARCHAR(16)           DEFAULT NULL COMMENT '单位标签',
    `currency_price_json` LONGTEXT COMMENT '目录价格配置',
    `in_package`          TINYINT(1)            DEFAULT NULL COMMENT '是否含套餐外,0:不包含，1：包含(阶梯型), 2: 包含(固定型)',
    `period`              INT(11)               DEFAULT NULL COMMENT '间隔时长单位',
    `unit_period`         INT(11)               DEFAULT NULL COMMENT '间隔时长,0:日，1：星期，2：月，3：季度，4：一次性',
    currency_dict_ids     VARCHAR(255)          DEFAULT NULL COMMENT '货币ID串(来源字典);便于后续查询',
    `status`              TINYINT(1)            DEFAULT '0' COMMENT '状态，0：未激活，1：激活，2：存档',
    config_resource       TINYINT               DEFAULT 0 COMMENT '配置资源, 0：未配置，1：已配置',
    config_bus_proto      TINYINT               DEFAULT 0 COMMENT '配置量表(按量和套餐计费的套餐内配置才能激活), 0:不涉及, 1：未配置，2：已配置',
    `scale_id`            BIGINT                DEFAULT NULL COMMENT '服务和资源对应关系表;与量表(charge_scale_info)一对一',
    `creator`             VARCHAR(64)  NOT NULL COMMENT '创建者',
    `create_time`         BIGINT(20)   NOT NULL COMMENT '创建时间',
    `updater`             VARCHAR(64)  NOT NULL COMMENT '更新者',
    `update_time`         BIGINT(20)   NOT NULL COMMENT '更新时间',
    `deleted`             BIT(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id`           BIGINT(20)   NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_service_name` (`service_name`)
) ENGINE = INNODB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT = '服务信息表';

## 固定费率配置 ChargeFixRatePriceDTO
[
  {
    "currency": "币种 CNY",
    "fixCharge": "金额 100.00",
    "integralCharge": "积分 1000"
  },
  {
    "currency": "币种 USD",
    "fixCharge": "金额 15.00",
    "integralCharge": "积分 1500"
  }
]

##  阶梯费率配置 ChargeStairRatePriceDTO
[
  {
    "currency": "币种 CNY",
    "tierPrices": [
      {
        "tierLevel": "阶梯等级 1",
        "min": "从 1",
        "max": "到 100",
        "fixCharge": "价格 1.00",
        "integralCharge": "积分 100",
        "payUnit": "每 单位 1",
        "isAllPay": "全额支付0"
      }
    ]
  }
]

## 按量计费 ChargeUsageStairRatePriceDTO
 {
    "priceModel": "fixed-固定, level-阶梯",
    "measure": "用量",
    "measureUnit": "计量单位",
    "measureCeil": "计量单位是否向上取整 0-不向上取整, 1-向上取整",
    "fixRatePrices": [
      {
        "currency": "币种 CNY",
        "fixCharge": "金额 2.00",
        "integralCharge": "积分 200"
      },
      {
        "currency": "币种 USD",
        "fixCharge": "金额 0.15",
        "integralCharge": "积分 15"
      }
    ],
    "stairRatePrices": [
      {
        "currency": "币种 CNY",
        "tierPrices": [
          {
            "tierLevel": "阶梯等级 1",
            "min": "从 1",
            "max": "到 100",
            "fixCharge": "价格 1.00",
            "integralCharge": "积分 100",
            "payUnit": "每 单位 1",
            "isAllPay": "全额支付0"
          },
          {
            "tierLevel": "阶梯等级 2",
            "min": "从 1",
            "max": "到 100",
            "fixCharge": "价格 1.00",
            "integralCharge": "积分 100",
            "payUnit": "每 单位 1",
            "isAllPay": "全额支付0"
          }
        ]
      }
    ]
  }

## 套餐计费
{
  "packageInclude": "套餐包含量",
  "packageIncludeUnit": "套餐包含单位",
  "measure": "用量",
  "measureUnit": "计量单位",
  "measureCeil": "计量单位是否向上取整 0-不向上取整, 1-向上取整",
  "packages": [
    {
      "currency": "CNY",
      "inPackage": {
        "fixCharge": "金额80.00",
        "integralCharge": "积分800"
      },
      "outPackage": {
        "outPackagePriceModel": "fixed-固定, level-阶梯",
        "fixRatePricesOutPackage": {
          "fixCharge": "金额80.00",
          "integralCharge": "积分800"
        },
        "stairRatePricesOutPackage": [
          {
            "tierLevel": "阶梯级别 1",
            "min": "从 1",
            "max": "到 100",
            "fixCharge": "价格 1",
            "integralCharge": "积分 1",
            "payUnit": "每 单位",
            "isAllPay": "是否全额支付 0"
          },
          {
            "tierLevel": "阶梯级别 2",
            "min": "从 100",
            "max": "到 1000",
            "fixCharge": "价格 1",
            "integralCharge": "积分 1",
            "payUnit": "每 单位",
            "isAllPay": "是否全额支付 0"
          }
        ]
      }
    }
  ]
}