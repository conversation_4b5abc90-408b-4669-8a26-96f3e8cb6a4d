package com.linkcircle.boss.module.billing.web.bill.product.controller;

import com.linkcircle.boss.module.billing.web.bill.product.scheduled.product.IncomePostpaidProductBillingScheduledTask;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.IncomePostpaidServiceBillingScheduledTask;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025-07-09 15:25
 * @description
 */
@RestController
@RequestMapping("/product-test")
@RequiredArgsConstructor
public class ProductTestController {

    private final IncomePostpaidServiceBillingScheduledTask incomePostpaidServiceBillingScheduledTask;
    private final IncomePostpaidProductBillingScheduledTask incomePostpaidProductBillingScheduledTask;

    @PostMapping("/incomePostpaidServiceBillingHandler")
    public String incomePostpaidServiceBillingHandler() {
        incomePostpaidServiceBillingScheduledTask.incomePostpaidServiceBillingHandler();
        return "success";
    }

    @PostMapping("/incomePostpaidProductBillingHandler")
    public String incomePostpaidProductBillingHandler() {
        incomePostpaidProductBillingScheduledTask.incomePostpaidProductBillingHandler();
        return "success";
    }
}
