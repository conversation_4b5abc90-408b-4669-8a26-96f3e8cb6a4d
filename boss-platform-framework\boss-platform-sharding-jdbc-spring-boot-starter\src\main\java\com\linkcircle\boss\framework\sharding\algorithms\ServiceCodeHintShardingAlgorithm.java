package com.linkcircle.boss.framework.sharding.algorithms;

import cn.hutool.core.collection.CollUtil;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.cache.ActualTableNameCache;
import com.linkcircle.boss.framework.sharding.utils.ShardingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.sharding.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.hint.HintShardingValue;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2025-06-14 11:35:00
 * @description 根据业务时间 服务编码 按年分表
 */
@Slf4j
public class ServiceCodeHintShardingAlgorithm implements HintShardingAlgorithm<HitBusinessTimeDTO> {

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, HintShardingValue<HitBusinessTimeDTO> hintShardingValue) {
        String logicTableName = hintShardingValue.getLogicTableName();
        Collection<HitBusinessTimeDTO> values = hintShardingValue.getValues();
        Optional<HitBusinessTimeDTO> dtoOptional = values.stream().findFirst();
        if (dtoOptional.isEmpty()) {
            return List.of();
        }
        HitBusinessTimeDTO businessTimeDTO = dtoOptional.get();
        List<String> serviceCodes = businessTimeDTO.getServiceCodes();
        if (CollUtil.isEmpty(serviceCodes)) {
            return List.of();
        }
        List<String> tableNames = new ArrayList<>();

        // 单个时间
        if (Objects.nonNull(businessTimeDTO.getTimestamp())) {
            int year = ShardingUtils.getYear(businessTimeDTO.getTimestamp());
            for (String serviceCode : serviceCodes) {
                addTable(serviceCode, logicTableName, year, tableNames);
            }
            return tableNames;
        }

        // 时间范围
        List<Long> timestampRange = businessTimeDTO.getTimestampRange();
        if (timestampRange.isEmpty()) {
            return List.of();
        }
        if (timestampRange.size() != 2) {
            return List.of();
        }
        int lowerYear = ShardingUtils.getYear(timestampRange.getFirst());
        int upperYear = ShardingUtils.getYear(timestampRange.getLast());
        if (CollUtil.isEmpty(serviceCodes)) {
            return List.of();
        }
        for (String serviceCode : serviceCodes) {
            for (int year = lowerYear; year <= upperYear; year++) {
                addTable(serviceCode, logicTableName, year, tableNames);
            }
        }
        return tableNames;
    }

    private void addTable(String serviceCode, String logicTableName, int year, List<String> tableNames) {
        String tableName = serviceCode + "_" + logicTableName + "_" + year;
        if (ActualTableNameCache.contains(tableName)) {
            tableNames.add(tableName);
            return;
        }
        log.warn("tableName: {} not exists in cache", tableName);
    }

    @Override
    public String getType() {
        return "HINT_BUSINESS_CODE_AND_TIME";
    }

    @Override
    public void init(Properties props) {
        log.info("ServiceCodeHintShardingAlgorithm init");
        HintShardingAlgorithm.super.init(props);
    }

}
