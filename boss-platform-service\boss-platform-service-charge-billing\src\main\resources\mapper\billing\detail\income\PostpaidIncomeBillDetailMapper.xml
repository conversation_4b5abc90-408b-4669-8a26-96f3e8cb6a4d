<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.billing.web.detail.income.mapper.PostpaidIncomeBillDetailMapper">
    <!-- 根据订阅ID、服务Code和时间范围统计使用量（使用SQL SUM函数） -->
    <select id="sumUsageByServiceAndTime"
            resultType="com.linkcircle.boss.module.billing.api.detail.income.model.entity.PostpaidIncomeBillDetailDO">
        SELECT COALESCE(SUM(charge_usage_count), 0) AS charge_usage_count,
        COALESCE(SUM(usage_count), 0) AS usage_count
        FROM postpaid_income_bill_detail
        WHERE service_id = #{serviceId}
        AND subscribe_id = #{subscribeId}
        AND business_time between #{billingStartTime} and #{billingEndTime}
        and deleted = false
    </select>
</mapper>
