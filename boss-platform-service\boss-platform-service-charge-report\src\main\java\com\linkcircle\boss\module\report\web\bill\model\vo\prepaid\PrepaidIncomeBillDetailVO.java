package com.linkcircle.boss.module.report.web.bill.model.vo.prepaid;

import com.baomidou.mybatisplus.annotation.TableField;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.*;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillCoupon;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16 16:40
 */
@Schema(description = "账单-预付费-单个查询 Response DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrepaidIncomeBillDetailVO {
    /**
     * 收入账单明细id
     */

    @Schema(description = "收入账单明细id")
    private Long billDetailId;


    /**
     * 订阅id
     */

    @Schema(description = "订阅id")
    private Long subscribeId;




    @Schema(description = "订阅名称")
    private String subscribeName;

    /**
     * 服务id
     */

    @Schema(description = "服务id")
    private Long serviceId;

    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 服务编码
     */

    @Schema(description = "服务编码")
    private String serviceCode;

    /**
     * 客户id
     */

    @Schema(description = "客户id")
    private Long customerId;


    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 账户id
     */

    @Schema(description = "账户id")
    private Long accountId;

    /**
     * 账户id
     */

    @Schema(description = "账户名称")
    private String accountName;

    /**
     * 产品id
     */

    @Schema(description = "产品id")
    private Long productId;

    /**
     * 产品id
     */

    @Schema(description = "产品名称")
    private String productName;

    /**
     * 主体id
     */

    @Schema(description = "主体id")
    private Long entityId;

    @Schema(description = "主题名称")
    private String entityName;

    /**
     * 合同id
     */

    @Schema(description = "合同id")
    private Long contractId;
    @Schema(description = "合同名称")
    private String contractName;
    /**
     * 钱包id
     */

    @Schema(description = "钱包id")
    private Long walletId;

    @Schema(description = "钱包名称")
    private String walletName;

    /**
     * 计划id
     */

    @Schema(description = "计划id")
    private Long planId;

    @Schema(description = "计划名称")
    private String planName;

    /**
     * 优惠id
     */

    @Schema(description = "优惠id")
    private Long discountId;

    @Schema(description = "优惠名称")
    private String discountName;


    /**
     * 事件名称
     */

    @Schema(description = "事件名称")
    private String eventName;

    /**
     * 消耗量
     */

    @Schema(description = "消耗量")
    private BigDecimal usageCount;

    /**
     * 消耗量单位
     */

    @Schema(description = "消耗量单位")
    private String usageUnit;

    /**
     * 支付方式 0-现金, 1-积分
     */

    @Schema(description = "支付方式 0-现金, 1-积分")
    private Integer paymentMethod;

    /**
     * 计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费
     */

    @Schema(description = "计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费")
    private Integer billingType;

    /**
     * 是否在试用期内
     */

    @Schema(description = "是否在试用期内")
    private Boolean inTrial;

    /**
     * 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
     */

    @Schema(description = "账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清")
    private Integer billStatus;

    /**
     * 钱包扣款状态（0-未扣款、1-成功、2-失败、3-处理中, 4-退款）
     */

    @Schema(description = "钱包扣款状态（0-未扣款、1-成功、2-失败、3-处理中, 4-退款）")
    private Integer walletDeductStatus;

    /**
     * 积分
     */

    @Schema(description = "积分")
    private BigDecimal pointAmount;

    /**
     * 税率
     */

    @Schema(description = "税率")
    private BigDecimal taxRate;

    /**
     * 单价
     */

    @Schema(description = "单价")
    private BigDecimal unitPrice;

    /**
     * 目录价(原价)
     */

    @Schema(description = "目录价(原价)")
    private BigDecimal originalPrice;

    /**
     * 订阅价(优惠后的价格)
     */

    @Schema(description = "订阅价(优惠后的价格)")
    private BigDecimal discountedPrice;

    /**
     * 优惠的金额
     */

    @Schema(description = "优惠的金额")
    private BigDecimal discountAmount;

    /**
     * 已开票金额
     */

    @Schema(description = "已开票金额")
    private BigDecimal invoicedAmount;

    /**
     * 可开票金额(=优惠价-已开票金额)
     */

    @Schema(description = "可开票金额(=优惠价-已开票金额)")
    private BigDecimal availableInvoiceAmount;

    /**
     * 含税总金额
     */

    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */

    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */

    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 实际支付时间戳
     */

    @Schema(description = "实际支付时间戳")
    private Long paymentTime;

    /**
     * 出账时间戳
     */

    @Schema(description = "出账时间戳")
    private Long billingTime;

    /**
     * 数据创建时间戳
     */

    @Schema(description = "数据创建时间戳")
    private Long createTime;

    @Schema(description = "客户详细信息")
    private ChargeCustomerInfoVO customer;
    @Schema(description = "主体详细信息")
    private EntityDetailsVO entity;


    /**
     * 费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*
     */
    @TableField("rate_details")
    @Schema(description = "费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*")
    private String rateDetails;


    @Schema(description = "固定费率配置")
    private FixedRateConfigFeeDTO fixedRateConfig;

    @Schema(description = "套餐计费配置")
    private PackageRateConfigFeeDTO packageRateConfig;

    @Schema(description = "阶梯费率配置")
    private TierRateConfigFeeDTO tierRateConfig;


    @Schema(description = "按量计费配置")
    private UsageBasedRateConfigFeeDTO usageBasedRateConfig;

    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "产品优惠详情json")
    @TableField("discount_details")
    private String discountDetails;

    @Schema(description = "已支付金额")
    @TableField("paid_amount")
    private BigDecimal paidAmount;

    @Schema(description = "未支付金额")
    @TableField("unpaid_amount")
    private BigDecimal unpaidAmount;


    /**
     * 税率
     */
    @TableField("tax_amount")
    @Schema(description = "税率金额总计")
    private BigDecimal taxAmount;


    // 各个服务总价  = 各个服务原价  - 折扣  + 税率
    @TableField("sub_total_amount")
    @Schema(description = "总价=各个服务总价之和")
    private BigDecimal subTotalAmount;


    @Schema(description = "出账开始时间戳（毫秒）")
    private Long billingStartTime;

    @Schema(description = "出账结束时间戳（毫秒）")
    private Long billingEndTime;


    @Schema(description = "账单优惠配置列表")
    private List<BillDiscountConfigDTO> coupons;


    @Schema(description = "账单配置详情-(包括法律信息和附加信息)")
    private InvoiceDetailsVO billConfigDetails;


    @TableField("bill_no")
    @Schema(description = "账单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billNo;

    /**
     * 开票退款金额
     */
    @TableField("refund_invoice_amount")
    @Schema(description = "开票退款金额")
    private BigDecimal refundInvoiceAmount;

    @Schema(description = "账单所有内容")
    private List<BillContent> showContents;

    @Schema(description = "账单所有优惠")
    private List<BillCoupon> showCoupons;

    @Schema(description = "服务价格详情")
    private ChargeProductServicePriceVo servicePriceVo;
}
