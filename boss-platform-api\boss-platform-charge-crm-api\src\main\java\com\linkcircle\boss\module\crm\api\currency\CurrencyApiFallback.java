package com.linkcircle.boss.module.crm.api.currency;

import com.linkcircle.boss.framework.common.model.CommonResult;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 * @date 2025/7/30 15:16
 */
@org.springframework.stereotype.Component
@lombok.extern.slf4j.Slf4j
public class CurrencyApiFallback  implements FallbackFactory<CurrencyApi> {
    @Override
    public CurrencyApi create(Throwable cause) {
        return new CurrencyApi() {
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<java.util.List<com.linkcircle.boss.module.crm.api.currency.vo.CurrencyRespVO>> list(){
                log.error("调用资源管理API失败， 异常信息: ", cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
