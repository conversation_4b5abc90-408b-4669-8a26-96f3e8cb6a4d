package com.linkcircle.boss.module.billing.web.bill.resource.scheduled.listener;

import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.bill.resource.model.entity.PostpaidResourceServiceCostBillDO;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto.BillCallbackDTO;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.model.dto.CostPostpaidResourceBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.CostPostpaidResourceServiceBillService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQTransactionListener;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionListener;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionState;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-17
 * @description 资源服务成本账单事务消息监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
// @RocketMQTransactionListener // 已迁移到统一事务监听器，避免冲突
public class CostResourceBillingTransactionListener_Deprecated implements RocketMQLocalTransactionListener {

    private final CostPostpaidResourceServiceBillService costPostpaidResourceServiceBillService;

    /**
     * 执行本地事务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
        try {
            CostPostpaidResourceBillingMessageDTO context = (CostPostpaidResourceBillingMessageDTO) arg;
            PostpaidResourceServiceCostBillDO resourceServiceBill = context.getResourceServiceCostBillDO();

            // 执行本地事务：保存单个资源服务账单
            boolean saved = costPostpaidResourceServiceBillService.saveServiceBill(resourceServiceBill);

            if (saved) {
                log.info("资源服务成本账单本地事务执行成功, billId: {}", resourceServiceBill.getResourceServiceBillId());
                return RocketMQLocalTransactionState.COMMIT;
            }
            log.error("资源服务成本账单本地事务执行失败, billId: {}", resourceServiceBill.getResourceServiceBillId());
            return RocketMQLocalTransactionState.ROLLBACK;
        } catch (Exception e) {
            log.error("资源服务成本账单本地事务执行异常", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }

    /**
     * 检查本地事务状态（用于消息回查）
     */
    @Override
    public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
        try {
            // 从消息头中获取账单ID
            String billData = (String) msg.getHeaders().get("extra");
            if (StrUtil.isEmpty(billData)) {
                log.error("回查消息中未找到billId");
                return RocketMQLocalTransactionState.ROLLBACK;
            }
            BillCallbackDTO billCallbackDTO = JsonUtils.parseObject(billData, BillCallbackDTO.class);
            if (Objects.isNull(billCallbackDTO)) {
                log.error("回查消息中未找到billId");
                return RocketMQLocalTransactionState.ROLLBACK;
            }
            Long billId = billCallbackDTO.getBillId();
            Long startTime = billCallbackDTO.getStartTime();

            PostpaidResourceServiceCostBillDO bill = costPostpaidResourceServiceBillService.getBillById(billId, startTime);
            if (Objects.nonNull(bill)) {
                log.info("回查确认：资源服务成本账单本地事务已提交, billId: {}", billId);
                return RocketMQLocalTransactionState.COMMIT;
            }
            log.info("回查确认：资源服务成本账单本地事务未提交, billId: {}", billId);
            return RocketMQLocalTransactionState.ROLLBACK;
        } catch (Exception e) {
            log.error("检查资源服务成本账单本地事务状态异常", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }
}