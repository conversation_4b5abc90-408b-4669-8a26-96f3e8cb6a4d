package com.linkcircle.boss.module.fee.api.wallets;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.ChargeForOther;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletNameDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletsTransfer;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO;
import com.linkcircle.boss.module.fee.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-06-19 15:43
 * @description
 */
@FeignClient(name = ApiConstants.NAME,
        path = ApiConstants.PREFIX + "/wallet/",
        fallbackFactory = CustomerWalletApiFallback.class,
        dismiss404 = true)
@Tag(name = "RPC 服务 - 客户-账户-钱包 信息")
public interface CustomerWalletApi {


    @GetMapping("queryWalletOfAccount")
    CommonResult<List<ChargeCustomerAccountWalletsDO>> queryWallet(
            @RequestParam("accountIds") List<Long> accountIds
    );

    @GetMapping("/deleteWalletInfo")
    @Operation(summary = "钱包注销")
    CommonResult<?> deleteWalletInfo(@RequestParam("id") Long id);

    @GetMapping("/getWalletInfo")
    @Operation(summary = "查询钱包信息")
    CommonResult<ChargeCustomerAccountWalletsDO> getWalletInfo(@RequestParam("id") Long id);

    @GetMapping("/getWalletCashOnly")
    @Operation(summary = "查询钱包信息集合-仅现金")
    List<ChargeCustomerAccountWalletsDO> getWalletCashOnly(@RequestParam("id") Long id);

    @PostMapping("/updateWallet")
    @Operation(summary = "更新钱包信息")
    CommonResult<?> updateWallet(@RequestBody ChargeCustomerAccountWalletsDO dto);

    @PostMapping("/createWallet")
    @Operation(summary = "创建钱包")
    CommonResult<?> createWallet(@RequestBody ChargeCustomerAccountWalletsDO dto);

    @PostMapping("/chargeWallet")
    @Operation(summary = "代充值")
    CommonResult<?> chargeWallet(@RequestBody ChargeForOther dto);

    @PostMapping("/transfer")
    @Operation(summary = "划转")
    CommonResult<?> transfer(@RequestBody WalletsTransfer dto);

    @GetMapping("/batchGetWalletNames")
    Map<Long, WalletNameDTO> batchGetWalletNames(@RequestParam("walletIds") Set<Long> walletIds);
}
