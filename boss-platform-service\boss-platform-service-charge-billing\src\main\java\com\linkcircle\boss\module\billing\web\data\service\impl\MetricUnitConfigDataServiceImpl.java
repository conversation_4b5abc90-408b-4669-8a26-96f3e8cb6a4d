package com.linkcircle.boss.module.billing.web.data.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.common.util.cache.FunctionUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.api.cache.dto.CacheUpdateMessageDTO;
import com.linkcircle.boss.module.billing.web.cache.manager.LocalCacheManager;
import com.linkcircle.boss.module.billing.web.data.cache.MetricUnitConfigCache;
import com.linkcircle.boss.module.billing.web.data.service.MetricUnitConfigDataService;
import com.linkcircle.boss.module.crm.api.base.metric.MetricUnitConfigApi;
import com.linkcircle.boss.module.crm.api.base.metric.vo.MetricUnitConfigSimpleVO;
import io.github.kk01001.redis.RedissonUtil;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2025-07-01 15:40
 * @description 指标单位配置数据服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MetricUnitConfigDataServiceImpl implements MetricUnitConfigDataService {

    private final RedissonUtil redissonUtil;
    private final MetricUnitConfigApi metricUnitConfigApi;
    private final LocalCacheManager localCacheManager;

    private static final ReentrantLock LOCK = new ReentrantLock();
    /**
     * 缓存过期时间 - 8小时
     */
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(8);

    /**
     * 项目启动时初始化缓存
     * 每10分钟
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    @PostConstruct
    public void initCache() {
        try {
            refreshCache();
            log.debug("指标单位配置缓存初始化完成，共加载{}个配置", MetricUnitConfigCache.size());
        } catch (Exception e) {
            log.error("指标单位配置缓存初始化失败", e);
        }
    }

    @Override
    public List<MetricUnitConfigSimpleVO> getSimpleMetricUnitConfigList() {
        // 1. 先从本地缓存查询
        Optional<List<MetricUnitConfigSimpleVO>> localCached = localCacheManager.getList(
                CacheUpdateMessageDTO.Type.METRIC_UNIT,
                CacheUpdateMessageDTO.Type.METRIC_UNIT
        );
        if (localCached.isPresent()) {
            log.debug("从本地缓存获取指标单位配置列表");
            return localCached.get();
        }

        // 2. 本地缓存未命中，从Redis缓存查询
        String redisCacheKey = ChargeCacheUtils.getMetricUnitConfigSimpleListKey();
        Optional<List<MetricUnitConfigSimpleVO>> optional = FunctionUtil.getCachedOrLoadDb(redisCacheKey,
                () -> {
                    String data = redissonUtil.get(redisCacheKey);
                    if (StrUtil.isEmpty(data)) {
                        return Optional.empty();
                    }
                    return Optional.ofNullable(JsonUtils.parseObjectQuietly(data, new TypeReference<>() {
                    }));
                },
                () -> {
                    CommonResult<List<MetricUnitConfigSimpleVO>> result = metricUnitConfigApi.getSimpleMetricUnitConfigList();
                    return Optional.of(result.getCheckedData());
                },
                data -> redissonUtil.set(redisCacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME));

        List<MetricUnitConfigSimpleVO> configs = optional.orElse(null);

        // 3. 如果从Redis或API获取到数据，同时更新本地缓存
        if (configs != null) {
            localCacheManager.put(CacheUpdateMessageDTO.Type.METRIC_UNIT, CacheUpdateMessageDTO.Type.METRIC_UNIT, configs);
            log.debug("更新本地缓存指标单位配置列表");
        }

        return configs;
    }

    @Override
    public void refreshCache() {
        try {
            LOCK.lock();
            EnableLoginContext.setContext(false);
            List<MetricUnitConfigSimpleVO> configs = getSimpleMetricUnitConfigList();
            if (CollUtil.isEmpty(configs)) {
                log.warn("指标单位配置列表为空");
                return;
            }
            MetricUnitConfigCache.clear();

            for (MetricUnitConfigSimpleVO config : configs) {
                if (config.getCode() != null && config.getBaseFactor() != null) {
                    MetricUnitConfigCache.put(config.getCode(), config.getBaseFactor());
                }
            }

            log.info("刷新指标单位配置缓存完成，共加载{}个配置", MetricUnitConfigCache.size());
        } catch (Exception e) {
            log.error("刷新指标单位配置缓存失败", e);
        } finally {
            LOCK.unlock();
            EnableLoginContext.clearContext();
        }
    }
}
