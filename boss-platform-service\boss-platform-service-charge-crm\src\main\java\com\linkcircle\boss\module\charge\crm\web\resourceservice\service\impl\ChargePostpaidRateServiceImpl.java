package com.linkcircle.boss.module.charge.crm.web.resourceservice.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.crm.enums.ChargeTypeEnum;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceAddDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceEditDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceOperationBaseDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceOperationDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.entity.ChargeResourceServicePrice;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.service.IChargeResourceServicePriceService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.service.base.IChargeTypeService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import static com.linkcircle.boss.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants.RESOURCE_SERVICE_VERSION_DTO_EXCEPTION;

/**
 * <p>
 * 按量费率处理服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Service
@RequiredArgsConstructor
public class ChargePostpaidRateServiceImpl implements IChargeTypeService<ChargeResourceServiceOperationBaseDTO, ChargeResourceServicePrice> {

    private final IChargeResourceServicePriceService chargeResourceServicePriceService;

    @Override
    public CommonResult<?> add(ChargeResourceServiceOperationBaseDTO baseDTO, ChargeResourceServicePrice chargeResourceServicePrice) {
        ChargeResourceServiceAddDTO addDTO = convertToConcreteDTO(baseDTO, ChargeResourceServiceAddDTO.class, RESOURCE_SERVICE_VERSION_DTO_EXCEPTION);
        return getCommonResult(chargeResourceServicePrice, addDTO);
    }

    private CommonResult<?> checkParam(ChargeResourceServiceOperationDTO dto) {
        if (ObjectUtil.isEmpty(dto.getChargePostpaidRateDTOS())) {
            return CommonResult.error(BAD_REQUEST.getCode(), "请求参数不正确:按量费率参数为空");
        }
        return CommonResult.success();
    }

    @NotNull
    private CommonResult<?> getCommonResult(ChargeResourceServicePrice chargeResourceServicePrice, ChargeResourceServiceAddDTO addDTO) {
        CommonResult<?> commonResult = checkParam(addDTO);
        if (!commonResult.isSuccess()) {
            return commonResult;
        }
        chargeResourceServicePrice.setCurrencyPriceJson(JSONUtil.toJsonStr(addDTO.getChargePostpaidRateDTOS()));
        chargeResourceServicePriceService.save(chargeResourceServicePrice);
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> addVersion(ChargeResourceServiceOperationBaseDTO baseDTO, ChargeResourceServicePrice chargeResourceServicePrice) {
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> edit(ChargeResourceServiceOperationBaseDTO baseDTO, ChargeResourceServicePrice chargeResourceServicePrice) {
        ChargeResourceServiceEditDTO editDTO = convertToConcreteDTO(baseDTO, ChargeResourceServiceEditDTO.class, RESOURCE_SERVICE_VERSION_DTO_EXCEPTION);
        CommonResult<?> commonResult = checkParam(editDTO);
        if (!commonResult.isSuccess()) {
            return commonResult;
        }
        chargeResourceServicePrice.setCurrencyPriceJson(JSONUtil.toJsonStr(editDTO.getChargePostpaidRateDTOS()));
        chargeResourceServicePriceService.updateById(chargeResourceServicePrice);
        return CommonResult.success();
    }

    @Override
    public ChargeTypeEnum getType() {
        return ChargeTypeEnum.postpaidRate;
    }
}
