package com.linkcircle.boss.module.charge.crm.web.subscribe.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.crm.manager.ChargeCacheManager;
import com.linkcircle.boss.module.charge.crm.web.customer.model.entity.ChargeCustomerAccountsInfo;
import com.linkcircle.boss.module.charge.crm.web.customer.service.CustomerAccountService;
import com.linkcircle.boss.module.charge.crm.web.subscribe.convert.*;
import com.linkcircle.boss.module.charge.crm.web.subscribe.mapper.ChargeSubscriptionsMapper;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto.*;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.entity.*;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo.*;
import com.linkcircle.boss.module.charge.crm.web.subscribe.service.*;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import com.linkcircle.boss.module.crm.enums.ChargeSubscriptionStatusEnum;
import com.linkcircle.boss.module.fee.api.wallets.CustomerWalletApi;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订阅管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChargeSubscriptionsServiceImpl
        extends ServiceImpl<ChargeSubscriptionsMapper, ChargeSubscriptionsDO>
        implements ChargeSubscriptionsService {

    private final ChargeSubscriptionsTimeDetailsService subscriptionsTimeDetailsService;

    private final ChargeSubscriptionsProductServeService subscriptionsProductServeService;

    private final ChargeSubscriptionsProductService subscriptionsProductService;

    private final ChargeSubscriptionsServiceCouponService subscriptionsServiceCouponService;

    private final CustomerWalletApi customerWalletApi;

    private final CustomerAccountService customerAccountService;

    /**
     * 根据请求分页查询订阅记录
     *
     * @param dto 分页请求参数对象
     * @return 分页结果对象
     */
    @Override
    public PageResult<ChargeSubscriptionsPageVO> page(ChargeSubscriptionsPageReqDTO dto) {
        // 构建分页对象
        Page<ChargeSubscriptionsDO> page = MyBatisUtils.buildPage(dto);
        // 查询分页结果
        List<ChargeSubscriptionsPageVO> list = this.baseMapper.queryByPage(page, dto);
        // 遍历查询结果
        for (ChargeSubscriptionsPageVO vo : list) {
            List<ChargeSubscriptionsTimeDetailsDO> detailsDOS = subscriptionsTimeDetailsService
                    .lambdaQuery()
                    .eq(ChargeSubscriptionsTimeDetailsDO::getSubsId, vo.getId())
                    .orderByAsc(ChargeSubscriptionsTimeDetailsDO::getEndTime)
                    .list();
            vo.setStartTime(detailsDOS.getFirst().getStartTime());
            vo.setEndTime(detailsDOS.getLast().getEndTime());
            // 如果计费周期和计费日不为空
            if (vo.getBillingCycle() != null && vo.getBillingDay() != null) {
                // 设置下次账单时间
                vo.setNextBillTime(calculateNextBillTime(
                        vo.getBillingCycle(),
                        vo.getBillingDay(),
                        // 获取指定时区的当天零点时间
                        getStartOfDayTimestamp(vo.getTimezone())
                ));
            }
        }
        // 转换分页结果
        return MyBatisUtils.convert2PageResult(page, list);
    }


    /**
     * 获取指定时区当天的开始时间戳（以毫秒为单位）。
     *
     * @param timezone 时区字符串，例如 "Asia/Shanghai"
     * @return 返回当天开始时间的时间戳（以毫秒为单位）
     */
    public long getStartOfDayTimestamp(String timezone) {
        try {
            // 将时区字符串转换为 ZoneId 对象
            ZoneId zone = ZoneId.of(timezone);
            // 获取当前日期
            return LocalDate.now(zone)
                    // 设置时间为当天的开始时间
                    .atStartOfDay(zone)
                    // 将 LocalDateTime 转换为 Instant 对象
                    .toInstant()
                    // 将 Instant 对象转换为以毫秒为单位的时间戳
                    .toEpochMilli();
        } catch (Exception e) {
            log.warn("Invalid timezone: {}, using default timezone Asia/Shanghai", timezone);
            // 使用默认时区
            ZoneId defaultZone = ZoneId.of("Asia/Shanghai");
            return LocalDate.now(defaultZone)
                    .atStartOfDay(defaultZone)
                    .toInstant()
                    .toEpochMilli();
        }
    }

    /**
     * 计算下次出账时间
     *
     * @param billingCycle 出账周期类型 0：每月，1：每周，2：每季度，3：每年
     * @param billingDay   出账日 (每月第几天/每周第几天等)
     * @param startTime    开始时间(时间戳)
     * @return 下次出账时间(时间戳)
     */
    private Long calculateNextBillTime(Integer billingCycle, Integer billingDay, Long startTime) {
        if (billingCycle == null || billingDay == null || startTime == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startTime);

        switch (billingCycle) {
            case 0: // 每月
                calendar.set(Calendar.DAY_OF_MONTH, billingDay);
                break;
            case 1: // 每周
                // 注意: Calendar中周日=1, 周一=2,...,周六=7
                calendar.set(Calendar.DAY_OF_WEEK, billingDay + 1);
                break;
            case 2: // 每季度
                int currentMonth = calendar.get(Calendar.MONTH);
                // 当前季度的第一个月
                int quarterStartMonth = (currentMonth / 3) * 3;
                calendar.set(Calendar.MONTH, quarterStartMonth);
                calendar.set(Calendar.DAY_OF_MONTH, billingDay);
                break;
            case 3: // 每年
                // 重置为当年1月1日
                calendar.set(Calendar.MONTH, Calendar.JANUARY);
                calendar.set(Calendar.DAY_OF_MONTH, 1);

                // 添加指定的天数-1(因为1月1日已经是第1天)
                calendar.add(Calendar.DAY_OF_YEAR, billingDay - 1);
                break;
            default:
                return null;
        }

        // 如果计算出的日期已经过去，则计算下一个周期
        if (calendar.getTimeInMillis() < System.currentTimeMillis()) {
            switch (billingCycle) {
                case 0: // 每月
                    calendar.add(Calendar.MONTH, 1);
                    break;
                case 1: // 每周
                    calendar.add(Calendar.WEEK_OF_YEAR, 1);
                    break;
                case 2: // 每季度
                    calendar.add(Calendar.MONTH, 3);
                    break;
                case 3: // 每年
                    calendar.add(Calendar.YEAR, 1);
                    break;
            }
        }

        return calendar.getTimeInMillis();
    }

    /**
     * 获取订阅详情
     *
     * @param id 订阅ID
     * @return ChargeSubscriptionsDetailVO 包含订阅详情的对象
     */
    @Override
    public ChargeSubscriptionsDetailVO detail(Long id) {
        // 1. 获取基础订阅详情
        ChargeSubscriptionsDetailVO detailVO = this.baseMapper.detail(id);
        if (Objects.isNull(detailVO)) return null;

        // 2. 在主线程序列化获取钱包信息
        CommonResult<ChargeCustomerAccountWalletsDO> walletResult = customerWalletApi.getWalletInfo(detailVO.getWalletsId());
        if (walletResult != null && walletResult.getData() != null) {
            detailVO.setWalletsName(walletResult.getData().getWalletsName());
            detailVO.setWalletsCurrency(walletResult.getData().getCurrencyCode());
        }

        // 3. 序列化获取账户信息
        ChargeCustomerAccountsInfo accountsInfo = customerAccountService.lambdaQuery()
                .eq(ChargeCustomerAccountsInfo::getId, detailVO.getAccountId())
                .one();

        // 3. 批量查询所有层级数据
        List<ChargeSubscriptionsTimeDetailsDO> timeDetails = getTimeDetails(detailVO.getId());
        List<ChargeSubscriptionsTimeDetailsVO> timeDetailsVOS = ChargeSubscriptionsTimeDetailsConvert.INSTANCE.convert2VOS(timeDetails);
        detailVO.setSubscriptions(timeDetailsVOS);

        // 批量获取所有ID
        Set<Long> timeDetailIds = timeDetails.stream().map(ChargeSubscriptionsTimeDetailsDO::getId).collect(Collectors.toSet());
        Map<Long, List<ChargeSubscriptionsProductDO>> productsMap = getProductsMap(timeDetailIds);

        Set<Long> productIds = productsMap.values().stream()
                .flatMap(List::stream)
                .map(ChargeSubscriptionsProductDO::getId)
                .collect(Collectors.toSet());
        Map<Long, List<ChargeSubscriptionsProductServiceDO>> servicesMap = getServicesMap(productIds);

        Set<Long> serviceIds = servicesMap.values().stream()
                .flatMap(List::stream)
                .map(ChargeSubscriptionsProductServiceDO::getId)
                .collect(Collectors.toSet());
        Map<Long, List<ChargeSubscriptionsServiceCouponDO>> couponsMap = getCouponsMap(serviceIds);

        // 4. 内存中组装数据
        assembleData(timeDetailsVOS, productsMap, servicesMap, couponsMap);
        // 5. 设置账单时间
        detailVO.setNextBillTime(calculateNextBillTime(
                accountsInfo.getBillingCycle(),
                accountsInfo.getBillingDay(),
                getStartOfDayTimestamp(accountsInfo.getTimezone())
        ));

        return detailVO;
    }

    /**
     * 批量查询时间详情
     *
     * @param subsId 订阅ID
     * @return 时间详情列表
     */
    private List<ChargeSubscriptionsTimeDetailsDO> getTimeDetails(Long subsId) {
        return subscriptionsTimeDetailsService.lambdaQuery()
                .eq(ChargeSubscriptionsTimeDetailsDO::getSubsId, subsId)
                .orderByAsc(ChargeSubscriptionsTimeDetailsDO::getStartTime)
                .list();
    }

    /**
     * 批量查询产品并分组
     *
     * @param timeDetailIds 时间详情ID集合
     * @return 返回一个Map，其中键为时间详情ID，值为对应的产品列表
     */
    private Map<Long, List<ChargeSubscriptionsProductDO>> getProductsMap(Set<Long> timeDetailIds) {
        if (timeDetailIds.isEmpty()) return Collections.emptyMap();

        return subscriptionsProductService.lambdaQuery()
                .in(ChargeSubscriptionsProductDO::getSubsTimeDetailsId, timeDetailIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(ChargeSubscriptionsProductDO::getSubsTimeDetailsId));
    }

    /**
     * 批量查询服务并分组
     *
     * @param productIds 产品ID集合
     * @return 服务对象映射，键为产品ID，值为服务列表
     */
    private Map<Long, List<ChargeSubscriptionsProductServiceDO>> getServicesMap(Set<Long> productIds) {
        if (productIds.isEmpty()) return Collections.emptyMap();

        return subscriptionsProductServeService.lambdaQuery()
                .in(ChargeSubscriptionsProductServiceDO::getSubsProductId, productIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(ChargeSubscriptionsProductServiceDO::getSubsProductId));
    }

    /**
     * 批量查询优惠券并分组
     *
     * @param serviceIds 服务ID集合
     * @return 返回以服务ID为键，优惠券列表为值的映射表
     */
    private Map<Long, List<ChargeSubscriptionsServiceCouponDO>> getCouponsMap(Set<Long> serviceIds) {
        if (serviceIds.isEmpty()) return Collections.emptyMap();

        return subscriptionsServiceCouponService.lambdaQuery()
                .in(ChargeSubscriptionsServiceCouponDO::getSubsServiceId, serviceIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(ChargeSubscriptionsServiceCouponDO::getSubsServiceId));
    }

    /**
     * 在内存中组装层级数据。
     *
     * @param timeDetailsVOS 时间详情列表
     * @param productsMap    产品映射表，键为时间详情ID，值为产品列表
     * @param servicesMap    服务映射表，键为产品ID，值为服务列表
     * @param couponsMap     优惠券映射表，键为服务ID，值为优惠券列表
     */
    private void assembleData(List<ChargeSubscriptionsTimeDetailsVO> timeDetailsVOS,
                              Map<Long, List<ChargeSubscriptionsProductDO>> productsMap,
                              Map<Long, List<ChargeSubscriptionsProductServiceDO>> servicesMap,
                              Map<Long, List<ChargeSubscriptionsServiceCouponDO>> couponsMap) {

        for (ChargeSubscriptionsTimeDetailsVO timeDetailVO : timeDetailsVOS) {
            // 设置产品
            List<ChargeSubscriptionsProductDO> productDOs = productsMap.getOrDefault(timeDetailVO.getId(), Collections.emptyList());
            List<ChargeSubscriptionsProductVO> productVOs = ChargeSubscriptionsProductConvert.INSTANCE.convert2VOS(productDOs);
            timeDetailVO.setProducts(productVOs);

            for (ChargeSubscriptionsProductVO productVO : productVOs) {
                // 设置服务
                List<ChargeSubscriptionsProductServiceDO> serviceDOs = servicesMap.getOrDefault(productVO.getId(), Collections.emptyList());
                List<ChargeSubscriptionsProductServiceVO> serviceVOs = ChargeSubscriptionsProductServiceConvert.INSTANCE.convert2VOS(serviceDOs);
                productVO.setServices(serviceVOs);

                for (ChargeSubscriptionsProductServiceVO serviceVO : serviceVOs) {
                    // 设置优惠券
                    List<ChargeSubscriptionsServiceCouponDO> couponDOs = couponsMap.getOrDefault(serviceVO.getId(), Collections.emptyList());
                    List<ChargeSubscriptionsServiceCouponVO> couponVOs = ChargeSubscriptionsServiceCouponConvert.INSTANCE.convert2VOS(couponDOs);
                    serviceVO.setCoupons(couponVOs);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(ChargeSubscriptionsCreateReqDTO dto) {
        ChargeSubscriptionsDO chargeSubscriptionsDO = ChargeSubscriptionsConvert.INSTANCE.convert(dto);
        long subId = IdUtil.getSnowflakeNextId();
        chargeSubscriptionsDO.setId(subId);

        if (Objects.isNull(dto.getSubscriptions().getFirst().getFreeTryoutDays()) || dto.getSubscriptions().getFirst().getFreeTryoutDays() == 0) {
            chargeSubscriptionsDO.setStatus(ChargeSubscriptionStatusEnum.EFFECTIVE.getCode());
        } else {
            chargeSubscriptionsDO.setStatus(ChargeSubscriptionStatusEnum.TRYING.getCode());
        }

        List<ChargeSubscriptionsTimeDetailsDTO> subscriptions = dto.getSubscriptions();
        List<ChargeSubscriptionsTimeDetailsDO> subscriptionDOS = new ArrayList<>();
        List<ChargeSubscriptionsProductDO> productDOS = new ArrayList<>();
        List<ChargeSubscriptionsProductServiceDO> serviceDOS = new ArrayList<>();
        List<ChargeSubscriptionsServiceCouponDO> couponDOS = new ArrayList<>();

        for (ChargeSubscriptionsTimeDetailsDTO subscription : subscriptions) {
            ChargeSubscriptionsTimeDetailsDO detailsDO = ChargeSubscriptionsTimeDetailsConvert.INSTANCE.convert(subscription);
            long timeDetailId = IdUtil.getSnowflakeNextId();
            detailsDO.setId(timeDetailId);
            detailsDO.setSubsId(subId);
            subscriptionDOS.add(detailsDO);

            List<ChargeSubscriptionsProductDTO> products = subscription.getProducts();
            if (CollectionUtil.isEmpty(products)) continue;
            for (ChargeSubscriptionsProductDTO product : products) {
                ChargeSubscriptionsProductDO productDO = ChargeSubscriptionsProductConvert.INSTANCE.convert(product);
                long subProductId = IdUtil.getSnowflakeNextId();
                productDO.setId(subProductId);
                productDO.setSubsId(subId);
                productDO.setSubsTimeDetailsId(timeDetailId);
                productDOS.add(productDO);

                List<ChargeSubscriptionsProductServiceDTO> services = product.getServices();
                if (CollectionUtil.isEmpty(services)) continue;
                for (ChargeSubscriptionsProductServiceDTO service : services) {
                    ChargeSubscriptionsProductServiceDO serviceDO = ChargeSubscriptionsProductServiceConvert.INSTANCE.convert(service);
                    long productServiceId = IdUtil.getSnowflakeNextId();
                    serviceDO.setId(productServiceId);
                    serviceDO.setSubsId(subId);
                    serviceDO.setSubsProductId(subProductId);
                    serviceDO.setPaymentOptions(chargeSubscriptionsDO.getPaymentOptions());
                    serviceDOS.add(serviceDO);

                    List<ChargeSubscriptionsServiceCouponDTO> coupons = service.getCoupons();
                    if (CollectionUtil.isEmpty(coupons)) continue;
                    for (ChargeSubscriptionsServiceCouponDTO coupon : coupons) {
                        ChargeSubscriptionsServiceCouponDO couponDO = ChargeSubscriptionsServiceCouponConvert.INSTANCE.convert(coupon);
                        couponDO.setId(IdUtil.getSnowflakeNextId());
                        couponDO.setSubsId(subId);
                        couponDO.setSubsServiceId(productServiceId);
                        couponDO.setSubsTimeDetailsId(timeDetailId);
                        couponDOS.add(couponDO);
                    }
                }
            }
        }
        // 删除缓存，重新加载数据
        ChargeCacheManager.deleteAccountSubscriptionsCache(dto.getAccountId());
        save(chargeSubscriptionsDO);
        subscriptionsTimeDetailsService.saveBatch(subscriptionDOS);
        subscriptionsProductService.saveBatch(productDOS);
        subscriptionsProductServeService.saveBatch(serviceDOS);
        subscriptionsServiceCouponService.saveBatch(couponDOS);
        return subId;
    }

    @Override
    public boolean update(ChargeSubscriptionsUpdateReqDTO dto) {
        if (Objects.isNull(dto.getId())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUBSCRIBE_NOT_EXISTS);
        }
        List<ChargeSubscriptionsTimeDetailsUpdateDTO> subscriptions = dto.getSubscriptions();
        List<ChargeSubscriptionsTimeDetailsDO> subscriptionDOS =
                new ArrayList<>(ChargeSubscriptionsTimeDetailsConvert.INSTANCE.convertList(subscriptions));
        ChargeCacheManager.deleteSubscriptionsDetailCache(dto.getId());
        ChargeCacheManager.deleteAccountSubscriptionsCache(dto.getId());
        return subscriptionsTimeDetailsService.updateBatchById(subscriptionDOS);
    }

    @Override
    public boolean changeStatus(Long id, Integer status) {
        ChargeCacheManager.deleteSubscriptionsDetailCache(id);
        ChargeCacheManager.deleteAccountSubscriptionsCache(id);
        return lambdaUpdate().eq(ChargeSubscriptionsDO::getId, id).set(ChargeSubscriptionsDO::getStatus, status).update();
    }

    @Override
    public boolean delete(Long id) {
        ChargeCacheManager.deleteSubscriptionsDetailCache(id);
        ChargeCacheManager.deleteAccountSubscriptionsCache(id);
        return removeById(id);
    }

    /**
     * 获取指定账户的订阅列表
     *
     * @param accountId 账户ID
     * @return 包含账户订阅信息的列表
     */
    @Override
    public List<AccountSubscriptionsVO> getAccountSubscriptionsList(Long accountId) {
        // 通过账户ID查询订阅信息
        List<ChargeSubscriptionsDO> list = lambdaQuery()
                .eq(ChargeSubscriptionsDO::getAccountId, accountId)
                .in(ChargeSubscriptionsDO::getStatus,
                        ChargeSubscriptionStatusEnum.TRYING.getCode(),
                        ChargeSubscriptionStatusEnum.EFFECTIVE.getCode())
                .list();
        // 将查询到的订阅信息转换为API订阅视图对象列表
        List<AccountSubscriptionsVO> accountSubscriptionsVOS = ChargeSubscriptionsConvert.INSTANCE.convert2ApiSubVOS(list);
        // 为每个订阅视图对象填充详细信息
        accountSubscriptionsVOS.forEach(this::fillDetailInfo);
        // 返回包含账户订阅信息的列表
        return accountSubscriptionsVOS;
    }


    /**
     * 获取订阅详情
     *
     * @param subscriptionId 订阅ID
     * @return 订阅详情VO对象
     */
    @Override
    public AccountSubscriptionsVO getSubscriptionDetail(Long subscriptionId) {
        // 查询订阅详情数据对象
        ChargeSubscriptionsDO one = this.lambdaQuery().eq(ChargeSubscriptionsDO::getId, subscriptionId).one();

        // 将数据对象转换为订阅详情VO对象
        AccountSubscriptionsVO subscriptionsVO = ChargeSubscriptionsConvert.INSTANCE.convert2ApiSubVO(one);

        // 填充订阅详情VO对象的详细信息
        fillDetailInfo(subscriptionsVO);

        return subscriptionsVO;
    }


    /**
     * 获取所有订阅的账户ID列表
     *
     * @param paymentType 支付类型
     * @return 所有订阅的账户ID列表
     */
    @Override
    public List<Long> getAllSubscriptionAccountIds(Integer paymentType) {
        // 查询全部订阅的账户id列表
        return lambdaQuery()
                .select(ChargeSubscriptionsDO::getAccountId)
                .eq(ChargeSubscriptionsDO::getPaymentType, paymentType).list()
                .stream()
                .map(ChargeSubscriptionsDO::getAccountId)
                .distinct()
                .toList();
    }

    /**
     * 根据计费类型获取订阅列表
     *
     * @param billingType 计费类型
     * @return AccountSubscriptionsVO 对象列表
     */
    @Override
    public List<AccountSubscriptionsVO> getSubscriptionsListByBillingType(Integer billingType) {
        // 1. 直接使用Set去重
        Set<Long> subsIds = subscriptionsProductServeService
                .lambdaQuery()
                .select(ChargeSubscriptionsProductServiceDO::getSubsId)
                .eq(ChargeSubscriptionsProductServiceDO::getChargeType, billingType)
                .list()
                .stream()
                .map(ChargeSubscriptionsProductServiceDO::getSubsId)
                .collect(Collectors.toSet());
        // 空结果快速返回
        if (subsIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 批量查询主订阅信息
        List<ChargeSubscriptionsDO> subscriptions = lambdaQuery()
                .in(ChargeSubscriptionsDO::getId, subsIds)
                .in(ChargeSubscriptionsDO::getStatus, ChargeSubscriptionStatusEnum.TRYING, ChargeSubscriptionStatusEnum.EFFECTIVE)
                .list();

        // 3. 转换VO对象
        List<AccountSubscriptionsVO> vos = ChargeSubscriptionsConvert.INSTANCE.convert2ApiSubVOS(subscriptions);

        // 4. 填充详细信息
        vos.forEach(this::fillDetailInfo);

        return vos;
    }

    @Override
    public List<Coupon> getSubscriptionCouponList(Long subscriptionId, Long serviceId) {
        Map<Long, List<ChargeSubscriptionsServiceCouponDO>> couponMap = subscriptionsServiceCouponService
                .lambdaQuery()
                .in(ChargeSubscriptionsServiceCouponDO::getSubsServiceId, List.of(serviceId))
                .list()
                .stream()
                .collect(Collectors.groupingBy(ChargeSubscriptionsServiceCouponDO::getSubsServiceId));
        List<ChargeSubscriptionsServiceCouponDO> coupons = couponMap.getOrDefault(serviceId, Collections.emptyList());
        // 将coupons转换为Coupon对象列表
        return ChargeSubscriptionsServiceCouponConvert.INSTANCE.convert2ApiCouponVOS(coupons);
    }

    /**
     * 填充订阅信息的详细信息
     *
     * @param subscriptionsVO 订阅信息的VO对象
     */
    private void fillDetailInfo(AccountSubscriptionsVO subscriptionsVO) {
        // 1. 批量查询所有Detail
        // 查询与subscriptionsVO.getId()相等的ChargeSubscriptionsTimeDetailsDO对象列表
        List<ChargeSubscriptionsTimeDetailsDO> detailsDOS = subscriptionsTimeDetailsService
                .lambdaQuery()
                .eq(ChargeSubscriptionsTimeDetailsDO::getSubsId, subscriptionsVO.getId())
                .list();
        // 将detailsDOS转换为AccountSubscriptionsVO.Detail对象列表
        List<AccountSubscriptionsVO.Detail> detailVOS = ChargeSubscriptionsTimeDetailsConvert.INSTANCE.convert2ApiDetailVOS(detailsDOS);
        // 将detailVOS设置到subscriptionsVO的details属性中
        subscriptionsVO.setDetails(detailVOS);

        // 判断detailVOS是否为空
        if (CollectionUtils.isEmpty(detailVOS)) {
            return;
        }

        // 2. 批量查询所有Product
        // 从detailVOS中提取所有detail的id，并转换为List<Long>类型
        List<Long> detailIds = detailVOS.stream().map(AccountSubscriptionsVO.Detail::getId).collect(Collectors.toList());
        // 查询与detailIds中任一id相等的ChargeSubscriptionsProductDO对象列表，并按subsTimeDetailsId分组
        Map<Long, List<ChargeSubscriptionsProductDO>> productMap = subscriptionsProductService
                .lambdaQuery()
                .in(ChargeSubscriptionsProductDO::getSubsTimeDetailsId, detailIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(ChargeSubscriptionsProductDO::getSubsTimeDetailsId));

        // 3. 批量查询所有Service
        // 从productMap中提取所有product的id，并转换为List<Long>类型
        List<Long> productIds = productMap.values().stream()
                .flatMap(List::stream)
                .map(ChargeSubscriptionsProductDO::getId)
                .collect(Collectors.toList());
        // 查询与productIds中任一id相等的ChargeSubscriptionsProductServiceDO对象列表，并按subsProductId分组
        Map<Long, List<ChargeSubscriptionsProductServiceDO>> serviceMap = subscriptionsProductServeService
                .lambdaQuery()
                .in(ChargeSubscriptionsProductServiceDO::getSubsProductId, productIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(ChargeSubscriptionsProductServiceDO::getSubsProductId));

        // 4. 批量查询所有Coupon
        // 从serviceMap中提取所有service的id，并转换为List<Long>类型
        List<Long> serviceIds = serviceMap.values().stream()
                .flatMap(List::stream)
                .map(ChargeSubscriptionsProductServiceDO::getId)
                .collect(Collectors.toList());
        // 查询与serviceIds中任一id相等的ChargeSubscriptionsServiceCouponDO对象列表，并按subsServiceId分组
        Map<Long, List<ChargeSubscriptionsServiceCouponDO>> couponMap = subscriptionsServiceCouponService
                .lambdaQuery()
                .in(ChargeSubscriptionsServiceCouponDO::getSubsServiceId, serviceIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(ChargeSubscriptionsServiceCouponDO::getSubsServiceId));

        // 5. 内存中组装数据
        // 遍历detailVOS
        for (AccountSubscriptionsVO.Detail detail : detailVOS) {
            // 从productMap中获取与detail的id相等的ChargeSubscriptionsProductDO对象列表，默认为空列表
            List<ChargeSubscriptionsProductDO> products = productMap.getOrDefault(detail.getId(), Collections.emptyList());
            // 将products转换为AccountSubscriptionsVO.Product对象列表
            List<AccountSubscriptionsVO.Product> productVOS = ChargeSubscriptionsProductConvert.INSTANCE.convert2ApiProVOS(products);
            // 将productVOS设置到detail的products属性中
            detail.setProducts(productVOS);

            // 遍历productVOS
            for (AccountSubscriptionsVO.Product product : productVOS) {
                // 从serviceMap中获取与product的id相等的ChargeSubscriptionsProductServiceDO对象列表，默认为空列表
                List<ChargeSubscriptionsProductServiceDO> services = serviceMap.getOrDefault(product.getId(), Collections.emptyList());
                // 将services转换为AccountSubscriptionsVO.Service对象列表
                List<AccountSubscriptionsVO.Service> serviceVOS = ChargeSubscriptionsProductServiceConvert.INSTANCE.convert2ApiSerVOS(services);
                // 将serviceVOS设置到product的services属性中
                product.setServices(serviceVOS);

                // 遍历serviceVOS
                for (AccountSubscriptionsVO.Service service : serviceVOS) {
                    // 从couponMap中获取与service的id相等的ChargeSubscriptionsServiceCouponDO对象列表，默认为空列表
                    List<ChargeSubscriptionsServiceCouponDO> coupons = couponMap.getOrDefault(service.getId(), Collections.emptyList());
                    // 将coupons转换为Coupon对象列表
                    List<Coupon> couponVOS = ChargeSubscriptionsServiceCouponConvert.INSTANCE.convert2ApiCouponVOS(coupons);
                    // 将couponVOS设置到service的coupons属性中
                    service.setCoupons(couponVOS);
                }
            }
        }
    }


}