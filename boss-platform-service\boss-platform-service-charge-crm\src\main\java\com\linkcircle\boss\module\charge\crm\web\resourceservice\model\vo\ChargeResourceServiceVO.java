package com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.linkcircle.boss.framework.excel.core.annotations.DictFormat;
import com.linkcircle.boss.framework.excel.core.convert.DictConvert;
import com.linkcircle.boss.framework.excel.core.convert.TimestampConvert;
import com.linkcircle.boss.module.system.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 资源服务基本信息分页返回
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "资源服务基本信息分页返回")
public class ChargeResourceServiceVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "服务id")
    private Long serviceId;

    @Schema(description = "服务最大版本id")
    private Long versionId;

    @Schema(description = "服务名称")
    @ExcelProperty(value = "服务名称")
    private String serviceName;

    @Schema(description = "服务code")
    private String serviceCode;

    @Schema(description = "0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费")
    @ExcelProperty(value = "计费类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PRODUCT_SERVER_CHARGING)
    private Integer chargeType;

    @Schema(description = "量表表结构id")
    private Long scaleId;

    @Schema(description = "配置量表(按量和套餐计费的套餐内配置才能激活), 0:不涉及, 1：未配置，2：已配置")
    @ExcelProperty(value = "配置量表", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PRODUCT_SERVER_TABLE)
    private Integer configBusProto;


    @Schema(description = "描述")
    @TableField("description")
    private String description;

    @Schema(description = "版本名称（如v1.0）")
    @TableField("version_name")
    private String versionName;


    @Schema(description = "单位标签")
    @TableField("unit_label")
    private String unitLabel;

    @Schema(description = "支付方式，0:现金，1：积分")
    @TableField("payment_options")
    private Integer paymentOptions;

    @Schema(description = "事件名称")
    private String eventName;

    @Schema(description = "是否含套餐外,0:不包含，1：包含")
    @TableField("in_package")
    private Integer inPackage;


    @Schema(description = "间隔时长")
    @TableField("period")
    private Integer period;

    @Schema(description = "间隔时长单位 0：一次性, 1:日，2：周，3：月，4：季度，5：年")
    @TableField("unit_period")
    private Integer unitPeriod;

    @Schema(description = "租户编号")
    @TableField("tenant_id")
    private Long tenantId;

    @Schema(description = "状态，0：未激活，1：激活,2:存档")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PRODUCT_SERVER_STATUS)
    private Integer status;


    @Schema(description = "创建时间")
    private Long createTime;

    @ExcelProperty(value = "创建时间")
    @Schema(description = "创建时间(本地服务器格式化)")
    private String createTimeFormat;

    @Schema(description = "最后更新时间")
    @ExcelProperty(value = "更新时间", converter = TimestampConvert.class)
    private Long updateTime;

    @Schema(description = "更新时间")
    private String updateTimeFormat;

    @Schema(description = "创建者")
    @ExcelProperty(value = "创建人")
    private String creator;


}
