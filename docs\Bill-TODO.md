# TODO

- [ ] web
    - [x] 基础信息 - 指标单位配置管理接口 (0626)

- [ ] 接口流程测试
    - [x] 接口鉴权参数调整 (0626)

- [x] 业务平台详单接收接口
    - [x] 收入接口
    - [x] 成本接口
    - [x] 接口鉴权
    - [x] 详单数据 发送mq消息

- [ ] CRM接口
    - [x] 订阅信息查询 根据账户id
    - [x] 订阅信息查询 根据订阅id
    - [x] 客户账户信息查询 根据账户id
    - [ ] 采购信息查询 根据账户id
    - [ ] 供应商信息查询 根据账户id
    - [x] 查询全部订阅信息列表, 可用 条件 付费类型 预付费 后付费等
    - [ ] 查询全部采购信息列表
    - [ ] 采购信息查询 根据采购id
    - [x] 查询单位配置接口 (0703)
    - [x] 查询量表字段接口 (0703)
    - [x] 这些信息是否需要本地缓存??? (0724)

- [ ] 杂项
    - [ ] 收入批价根据账户查询不到订购的产品异常告警功能开发
    - [ ] 计算原价接口 传消耗量 单位 服务 id 订阅id 产品id
    - [ ] 订阅 - 出账生成草稿，0：否，1：是 在产品账单
    - [ ] 订阅 - 是否含套餐外,0:不包含，1：包含(阶梯型), 2: 包含(固定型)
    - [x] 订阅 - 优惠 优惠范围 0.全部 1.套餐内 2.套餐外 套餐外过滤匹配的优惠 (0714)
    - [ ] 取消订阅 立刻出账
    - [x] 配置量表时 创建对应表接口 待实现 (0718)
    - [x] redis缓存删除操作 (0707)
    - [x] mq消费幂等性处理,
        - [x] 收入消费 (0703)
        - [x] 成本消费 (0704)
    - [x] tranceId 设置 (0703)
    - [x] pref 收入 成本 费率计费 搞成模板方法
    - [ ] 订阅 周期 消耗量累计 入库 redis 是否需要redis? ? 不使用redis
        - [x] 收入
            - [x] 固定费率测试
            - [x] 阶梯费率测试
            - [x] 按量计费测试
            - [x] 套餐计费测试
        - [ ] 成本
            - [ ] 固定费率测试
            - [ ] 阶梯费率测试
            - [ ] 按量计费测试
            - [ ] 套餐计费测试

- [x] 4种费率原价计算
    - [x] 免费试用天数  (0626)
        - [x] 固定费率没处理! 不用处理
        - [x] 试用期内, 消耗量 不累计 数据库 redis不操作 (0703)
    - [x] 单位转化 kb -> gb
    - [x] 计量单位 向上取整? 例如计费单位为30s，31s则为2个计费单位，即1min   (0701)
    - [x] 当前费率 具体在哪个阶梯, 哪个套餐... 搞个json存 费用详情, 优惠详情json (0709)
        - [x] 费率实际消耗费用计算json (0709)
        - [x] 实际优惠金额计算json (0709)
    - [x] 后付费, 在定时任务处理, 按比例计算 订阅配置  (0716)
    - [x] 套餐计费 套餐内 单位, 计量单位 单位 单位转化  (0701)
    - [x] 按量计费 计量单位 单位 与消耗量 单位转化 (0701)
    - [x] 目录价 优惠价调整 (0714)

- [x] 收入详单消费mq顺序消息
    - [x] 调用crm 查询订阅信息
    - [x] 计算4种费率的价格 优惠 大概流程...
        - [x] 固定费率测试
        - [x] 阶梯费率测试
        - [x] 按量计费测试
        - [x] 套餐计费测试
    - [x] 预付费 账单发送钱包mq
    - [x] 价格处理完成入库, 业务字段 动态入库 基于mybatis拦截器, (0625)
        - [x] 现金金额, 积分金额 根据支付方式
        - [x] 增加账单费用明细信息和优惠详情json (0709)

- [ ] 成本详单消费mq顺序消息
    - [x] 调用crm 查询采购信息 (0625)
    - [x] 计算4种费率的价格 大概流程 ... (0625)
        - [ ] 固定费率测试
        - [ ] 阶梯费率测试
        - [ ] 按量计费测试
        - [ ] 套餐计费测试
    - [x] 价格处理完成入库, 业务字段 动态入库 基于mybatis拦截器  (0626)

- [ ] 定时任务
    - [x] 收入固定费率, 阶梯费率 根据间隔时长周期 定时模拟发送详单到mq (0710)
        - [x] 固定
            - [x] TODO 是否要提前判断是否当前周期已经计算过?? redis setnx 有效期 一个周期 再开始出账时查一下
        - [x] 阶梯
        - [x] 测试流程
    - [x] 成本固定费率, 阶梯费率 根据间隔时长周期 定时模拟发送详单到mq (0710)
        - [x] 固定
            - [ ] TODO 是否要提前判断是否当前周期已经计算过?? redis setnx 有效期 一个周期 再开始出账时查一下
        - [x] 阶梯
        - [ ] 测试流程
    - [x] 收入 后付费 按服务定时出账 (0707)
        - [x] TODO 是否要提前判断是否当前周期已经计算过??
            - 在出账完成之后 redis setnx 有效期 一个周期再开始出账时查一下 (0717)
        - [x] xxl-job 定时任务
        - [x] 查询全部 账户id 查询账户的订阅, 遍历全部服务, 一个一个出账
        - [x] 先保存服务账单到数据库, 发送事务消息,
        - [x] 消费事务消息, 计算原价 优惠, 更新数据库,
        - [x] 消耗量统计 sum
            - [x] 试用天数 是否要移除试用的消耗量? 不统计 计费消耗量设置为0  (0716)
            - [x] 按比例计算, 订阅开始时间和出账时间 订阅开始时间比周期开始时间大  (0716)
          ```
            固定费率：30元
            出账周期：7月1日～7月31日
            用户订阅时间：7月15日
            实际使用时间：7月15日至7月31日，共17天
            当月天数：31天
            占比：17 / 31 ≈ 0.548
            💰费用 = 30元 × 0.548 ≈ 16.44元
          ```
        - [x] 账单id以周期结束时间 生成  (0716)
        - [x] 测试流程
    - [x] 收入 后付费 按产品定时出账 (0716)
        - [x] xxl-job 定时任务 (0716)
        - [x] 账户出账周期 自然月 自然年处理 (0716)
        - [x] 汇总服务账单信息, 生成产品账单并入库 (0716)
        - [x] TODO 是否要提前判断是否当前周期已经计算过??
            - 在出账完成之后 redis setnx 有效期 一个周期 再开始出账时查一下 (0717)
        - [ ] 测试流程
    - [ ] 成本 后付费 按资源服务 定时出账
        - [x] 定时任务大概流程 (0717)
        - [ ] 测试流程
    - [ ] 客户 按日使用量统计 定时任务
      - 
    - [ ] 供应商 按日使用量统计 定时任务
      - 

- [ ] 兜底方案
    - [x] 失败数据写入本地文件, 通用解决方案  (0723)
    - [x] 接口->收入/成本详单->发送mq失败, 写入本地文件 (0626)
    - [x] 接口->收入/成本详单->发送mq->消费mq失败, (先通过 rocketmq重试16次之后, 写入死信队列主题, 再消费死信队列主题,
      若再消费失败->) 写入本地文件 (0627)
    - [x] 本地失败数据处理器, 分业务  (0723)
      -  
    - [x] 详单入库失败, 写入本地文件,
    - [x] 本地json文件, 定时任务扫描, 入库, 入库成功 删除文件, 搞成通用方案 (0723)
        - [x] 详单入库失败 分收入, 成本
        - [x] 发送钱包mq失败
        - [ x 写文件目录可配置

# QA

- 积分 预付费要算税率?
- 采购 出单周期 间隔时长周期
- 套餐计费 是否含套餐外 超过套餐的?

# 测试

- [ ] 预付费收入流程
    - 固定费率  (0725)
        - 定时任务 模拟发送详单到mq  (0728 完)
    - 阶梯费率  (0725)
        - 定时任务 模拟发送详单到mq  (0728 完)
    - 按量计费  (0728 完)
    - 套餐计费  (0728 完)

- [ ] 后付费收入流程
    - 服务出账任务  (0729 0730 完)
    - 产品出账任务  (0730 完)

- [ ] 成本详单流程
    - 固定费率
        - 定时任务 模拟发送详单到mq
    - 阶梯费率
        - 定时任务 模拟发送详单到mq
    - 按量计费
    - 套餐计费

- [ ] 成本服务账单出账

- [ ] 异常处理定时任务

- [ ] 客户账单定时任务

- [ ] 供应商账单定时任务