package com.linkcircle.boss.module.report.web.bill.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-07-02 20:27:00
 * @description 手工账单-收入-产品账单表实体类
 */
@TableName("makeup_product_income_bill")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "手工账单-收入-产品账单表")
public class MakeupProductIncomeBillDO {

    /**
     * 产品账单id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "产品账单id")
    private Long productBillId;

    /**
     * 账单ID
     */
    @TableField("bill_id")
    @Schema(description = "账单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long billId;

    /**
     * 客户id
     */
    @TableField("customer_id")
    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    /**
     * 账户id
     */
    @TableField("account_id")
    @Schema(description = "账户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

    /**
     * 主体id
     */
    @TableField("entity_id")
    @Schema(description = "主体id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long entityId;

    /**
     * 合同id
     */
    @TableField("contract_id")
    @Schema(description = "合同id")
    private Long contractId;

    /**
     * 钱包id
     */
    @TableField("wallet_id")
    @Schema(description = "钱包id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long walletId;

    /**
     * 产品id
     */
    @TableField("product_id")
    @Schema(description = "产品id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long productId;



    /**
     * 出账时间戳
     */
    @TableField("billing_time")
    @Schema(description = "出账时间戳")
    private Long billingTime;

    /**
     * 数据创建时间戳
     */
    @TableField("create_time")
    @Schema(description = "数据创建时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long createTime;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Integer deleted;


    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>")
    @TableField("discount_details")
    private String discountDetails;


    /**
     * 目录价(原价)
     */
    @TableField("original_price")
    @Schema(description = "目录价(原价)")
    private BigDecimal originalPrice;
    /**
     * 优惠的金额
     */
    @TableField("discount_amount")
    @Schema(description = "优惠的金额")
    private BigDecimal discountAmount;

    /**
     * 含税总金额
     */
    @TableField("amount_with_tax")
    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @TableField("amount_without_tax")
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */
    @TableField("currency")
    @Schema(description = "货币单位 CNY USD")
    private String currency;


    /**
     * 含税总金额
     */
    @TableField("tax_amount")
    @Schema(description = "税额")
    private BigDecimal taxAmount;

    /**
     * 含税总金额
     */
    @TableField("tax_rate")
    @Schema(description = "含税")
    private BigDecimal taxRate;
}
