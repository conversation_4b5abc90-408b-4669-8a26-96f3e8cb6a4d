package com.linkcircle.boss.module.billing.api.bill.product.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-07-15 15:36
 * @description 后付费产品服务收入账单表
 */
@TableName("postpaid_product_service_income_bill")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "后付费-收入-按产品收入-服务详细-账单表")
public class PostpaidProductServiceIncomeBillDO {

    /**
     * 产品服务账单id
     */
    @TableId(type = IdType.INPUT)
    @Schema(description = "产品服务账单id")
    private Long productServiceBillId;

    /**
     * 出账时间戳
     */
    @TableField("billing_time")
    @Schema(description = "出账时间戳")
    private Long billingTime;

    /**
     * 服务id
     */
    @TableField("service_id")
    @Schema(description = "服务id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long serviceId;

    /**
     * 服务编码
     */
    @TableField("service_code")
    @Schema(description = "服务编码")
    private String serviceCode;

    /**
     * 计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费
     */
    @TableField("billing_type")
    @Schema(description = "计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer billingType;

    /**
     * 产品账单id
     */
    @TableField("bill_id")
    @Schema(description = "产品账单id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long billId;

    /**
     * 主体id
     */
    @TableField("entity_id")
    @Schema(description = "主体id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long entityId;

    /**
     * 合同id
     */
    @TableField("contract_id")
    @Schema(description = "合同id")
    private Long contractId;

    /**
     * 客户id
     */
    @TableField("customer_id")
    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    /**
     * 账户id
     */
    @TableField("account_id")
    @Schema(description = "账户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

    /**
     * 钱包id
     */
    @TableField("wallet_id")
    @Schema(description = "钱包id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long walletId;

    /**
     * 计划id
     */
    @TableField("plan_id")
    @Schema(description = "计划id")
    private Long planId;

    /**
     * 产品id
     */
    @TableField("product_id")
    @Schema(description = "产品id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long productId;

    /**
     * 订阅id
     */
    @TableField("subscribe_id")
    @Schema(description = "订阅id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long subscribeId;

    /**
     * 支付方式 0-现金, 1-积分
     */
    @TableField("payment_method")
    @Schema(description = "支付方式 0-现金, 1-积分", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer paymentMethod;

    /**
     * 消耗量
     */
    @TableField("usage_count")
    @Schema(description = "消耗量", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal usageCount;

    /**
     * 消耗量单位
     */
    @TableField("usage_unit")
    @Schema(description = "消耗量单位")
    private String usageUnit;

    /**
     * 计费消耗量(measure*charge_unit_count)
     */
    @TableField("charge_usage_count")
    @Schema(description = "计费消耗量(measure*charge_unit_count)", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal chargeUsageCount;

    /**
     * 计费单位数
     */
    @TableField("charge_unit_count")
    @Schema(description = "计费单位数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal chargeUnitCount;

    /**
     * 计量单位数
     */
    @TableField("charge_measure")
    @Schema(description = "计量单位数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal chargeMeasure;

    /**
     * 计费计量单位
     */
    @TableField("charge_measure_unit")
    @Schema(description = "计费计量单位")
    private String chargeMeasureUnit;

    /**
     * 计量单位是否向上取整 0-不向上取整, 1-向上取整
     */
    @TableField("charge_measure_ceil")
    @Schema(description = "计量单位是否向上取整 0-不向上取整, 1-向上取整", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer chargeMeasureCeil;

    /**
     * 税率
     */
    @TableField("tax_rate")
    @Schema(description = "税率")
    private BigDecimal taxRate;

    /**
     * 目录价(原单价)
     */
    @TableField("original_unit_price")
    @Schema(description = "目录价(原单价)")
    private BigDecimal originalUnitPrice;

    /**
     * 订阅价(优惠的目录价)
     */
    @TableField("discounted_unit_price")
    @Schema(description = "订阅价(优惠的目录价)")
    private BigDecimal discountedUnitPrice;

    /**
     * 目录总价(原单价)
     */
    @TableField("original_price")
    @Schema(description = "目录总价(原单价)")
    private BigDecimal originalPrice;

    /**
     * 订阅总价(优惠的目录价)
     */
    @TableField("discounted_price")
    @Schema(description = "订阅总价(优惠的目录价)")
    private BigDecimal discountedPrice;

    /**
     * 优惠金额
     */
    @TableField("discount_amount")
    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    /**
     * 含税总金额
     */
    @TableField("amount_with_tax")
    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @TableField("amount_without_tax")
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 已支付金额
     */
    @TableField("paid_amount")
    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    /**
     * 未支付金额
     */
    @TableField("unpaid_amount")
    @Schema(description = "未支付金额")
    private BigDecimal unpaidAmount;

    /**
     * 货币单位 CNY USD
     */
    @TableField("currency")
    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 实际支付时间戳
     */
    @TableField("payment_time")
    @Schema(description = "实际支付时间戳")
    private Long paymentTime;

    /**
     * 出账开始时间戳（毫秒）
     */
    @TableField("billing_start_time")
    @Schema(description = "出账开始时间戳（毫秒）")
    private Long billingStartTime;

    /**
     * 出账结束时间戳（毫秒）
     */
    @TableField("billing_end_time")
    @Schema(description = "出账结束时间戳（毫秒）")
    private Long billingEndTime;

    /**
     * 出账周期类型，0：每月，1：每周，2：每季度，3：每年
     */
    @TableField("billing_cycle_type")
    @Schema(description = "出账周期类型，0：每月，1：每周，2：每季度，3：每年")
    private Integer billingCycleType;

    /**
     * 出账第几天
     */
    @TableField("billing_day")
    @Schema(description = "出账第几天")
    private Integer billingDay;

    /**
     * 出账周期标识（如202507-202509）
     */
    @TableField("billing_cycle")
    @Schema(description = "出账周期标识（如202507-202509）")
    private String billingCycle;

    /**
     * 时区
     */
    @TableField("timezone")
    @Schema(description = "时区")
    private String timezone;

    /**
     * 出账状态 0-待处理, 1-处理中, 2-已完成, 3-失败
     */
    @TableField("billing_status")
    @Schema(description = "出账状态 0-待处理, 1-处理中, 2-已完成, 3-失败")
    private Integer billingStatus;

    /**
     * 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
     */
    @TableField("bill_status")
    @Schema(description = "账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清")
    private Integer billStatus;

    /**
     * 数据创建时间戳
     */
    @TableField("create_time")
    @Schema(description = "数据创建时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long createTime;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Boolean deleted;

    /**
     * 费用详情json 见 com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO
     */
    @TableField("rate_details")
    @Schema(description = "费用详情json 见 com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO")
    private String rateDetails;

    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @TableField(value = "discount_details", jdbcType = JdbcType.OTHER)
    @Schema(description = "服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>")
    private String discountDetails;

    /**
     * 钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）
     */
    @TableField("wallet_deduct_status")
    @Schema(description = "钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）")
    private Integer walletDeductStatus;
}
