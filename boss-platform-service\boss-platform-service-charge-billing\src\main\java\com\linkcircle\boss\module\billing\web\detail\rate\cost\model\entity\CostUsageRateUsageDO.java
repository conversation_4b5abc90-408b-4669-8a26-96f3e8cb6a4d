package com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-25 10:31
 * @description 成本-按量费率用量表，记录账户出账周期内累计消耗量
 */
@TableName("cost_usage_rate_usage")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "成本-按量费率用量表，记录账户出账周期内累计消耗量")
public class CostUsageRateUsageDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 采购ID
     */
    @TableField("purchase_id")
    @Schema(description = "采购ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long purchaseId;

    /**
     * 账户ID，按量计费以账户为维度
     */
    @TableField("account_id")
    @Schema(description = "账户ID，按量计费以账户为维度", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

    /**
     * 服务ID
     */
    @TableField("service_id")
    @Schema(description = "服务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long serviceId;

    /**
     * 账户出账周期标识，如**************
     */
    @TableField("billing_cycle")
    @Schema(description = "账户出账周期标识，如**************", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billingCycle;

    /**
     * 出账周期开始时间戳
     */
    @TableField("billing_cycle_start")
    @Schema(description = "出账周期开始时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long billingCycleStart;

    /**
     * 出账周期结束时间戳
     */
    @TableField("billing_cycle_end")
    @Schema(description = "出账周期结束时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long billingCycleEnd;

    /**
     * 周期内累计消耗量
     */
    @TableField("total_usage")
    @Schema(description = "周期内累计消耗量", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal totalUsage;

    /**
     * 用量单位
     */
    @TableField("usage_unit")
    @Schema(description = "用量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    private String usageUnit;

    /**
     * 计费时间戳
     */
    @TableField("billing_time")
    @Schema(description = "计费时间戳")
    private Long billingTime;

    /**
     * 货币类型
     */
    @TableField("currency")
    @Schema(description = "货币类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currency;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long createTime;
}
