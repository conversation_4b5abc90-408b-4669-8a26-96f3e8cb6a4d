<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.linkcircle.boss</groupId>
        <artifactId>boss-platform-service</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>boss-platform-service-system</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等
    </description>

    <dependencies>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-system-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-sa-token-spring-boot-starter</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-excel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 验证码，一般用于登录使用 -->
        <dependency>
            <groupId>com.anji-plus</groupId>
            <artifactId>captcha-spring-boot-starter</artifactId>
        </dependency>

        <!-- 文件客户端：解决阿里云、腾讯云、minio 等 S3 连接 -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>url-connection-client</artifactId>
        </dependency>

        <!-- 文件客户端：文件类型的识别 -->
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.mouzt</groupId>
            <artifactId>bizlog-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.kk01001</groupId>
            <artifactId>netty-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eclipse.angus</groupId>
            <artifactId>angus-mail</artifactId>
            <version>2.0.2</version> <!-- 使用最新版 -->
        </dependency>

        <!-- 以下加了会有问题 -->
        <!--        <dependency>-->
        <!--            <groupId>jakarta.activation</groupId>-->
        <!--            <artifactId>jakarta.activation-api</artifactId>-->
        <!--            <version>2.1.2</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.eclipse.angus</groupId>-->
        <!--            <artifactId>angus-activation</artifactId>-->
        <!--            <version>2.0.1</version> &lt;!&ndash; 与 angus-mail 版本匹配 &ndash;&gt;-->
        <!--        </dependency>-->
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
