package com.linkcircle.boss.module.crm.api.entity;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:05
 */
@Component
@Slf4j
public class EntityApiFallback implements FallbackFactory<EntityApi> {
    @Override
    public EntityApi create(Throwable cause) {
        return new EntityApi() {
            @Override
            public CommonResult<List<CommonVO>> findNameByIds(CommonDTO commonDTO) {
                log.error("调用主体中心API失败API失败，accountId: {}, 异常信息: ", commonDTO, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<EntityDetailsVO> findById(Long id) {
                log.error("调用主体中心API失败API失败，accountId: {}, 异常信息: ", id, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
