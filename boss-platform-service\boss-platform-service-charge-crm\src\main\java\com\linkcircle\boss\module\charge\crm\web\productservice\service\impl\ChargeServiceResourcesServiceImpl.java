package com.linkcircle.boss.module.charge.crm.web.productservice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.crm.web.productservice.mapper.ChargeServiceResourcesMapper;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.dto.ChargeServiceResourcesAddDTO;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.dto.ChargeServiceResourcesUpdateDTO;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.entity.ChargeServiceResourcesDO;
import com.linkcircle.boss.module.charge.crm.web.productservice.service.IChargeProductServicePriceService;
import com.linkcircle.boss.module.charge.crm.web.productservice.service.IChargeServiceResourcesService;
import com.linkcircle.boss.module.charge.crm.web.resource.model.ChargeResource;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.mapper.ChargeResourceServiceMapper;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务和资源对应关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class ChargeServiceResourcesServiceImpl extends ServiceImpl<ChargeServiceResourcesMapper, ChargeServiceResourcesDO> implements IChargeServiceResourcesService {

    @Autowired
    private IChargeProductServicePriceService chargeProductServicePriceService;
    @Autowired
    private ChargeResourceServiceMapper chargeResourceServiceMapper;

    @Override
    @Transactional
    public CommonResult<Long> saveBatchServiceResources(ChargeServiceResourcesAddDTO param) throws Exception {
        List<Long> resourcesIds = param.getResourcesIds();
        Long serviceId = param.getServiceId();

        List<ChargeServiceResourcesDO> doSaveList = resourcesIds.stream().map(resourceId -> ChargeServiceResourcesDO.builder()
                .serviceId(serviceId)
                .resourcesId(resourceId).build()).toList();

        boolean savedBatch = this.saveBatch(doSaveList);

        if (!savedBatch) {
            throw new ServiceException(500, "保存失败");
        }

        // 保存后 修改服务配置资源状态 为已配置
        boolean updated = chargeProductServicePriceService.changeServiceResourceStatus(serviceId, 1);
        if (!updated) {
            throw new ServiceException(500, "保存失败");
        }

        return CommonResult.success();
    }

    @Override
    @Transactional
    public CommonResult<Long> updateBatchServiceResources(ChargeServiceResourcesUpdateDTO param) {
        List<Long> resourcesIds = param.getResourcesIds();
        Long serviceId = param.getServiceId();

        LambdaQueryWrapper<ChargeServiceResourcesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeServiceResourcesDO::getServiceId, serviceId);
        boolean removed = this.remove(queryWrapper);
        if (!removed) {
            throw new ServiceException(500, "更新失败");
        }

        List<ChargeServiceResourcesDO> doSaveList = resourcesIds.stream().map(resourceId -> ChargeServiceResourcesDO.builder()
                .serviceId(serviceId)
                .resourcesId(resourceId).build()).toList();

        boolean savedBatch = this.saveBatch(doSaveList);
        if (!savedBatch) {
            throw new ServiceException(500, "更新失败");
        }

        return CommonResult.success();
    }

    @Override
    @Transactional
    public CommonResult<Long> deleteAllServiceRelResources(Long serviceId) throws Exception {
        LambdaQueryWrapper<ChargeServiceResourcesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeServiceResourcesDO::getServiceId, serviceId);
        boolean removed = this.remove(queryWrapper);
        if (!removed) {
            throw new ServiceException(500, "删除失败");
        }

        // 资源全部被删除后 状态改未配置
        boolean updated = chargeProductServicePriceService.changeServiceResourceStatus(serviceId, 0);
        if (!updated) {
            throw new ServiceException(500, "删除失败");
        }
        return CommonResult.success();
    }

    @Override
    @Transactional
    public CommonResult<Long> removeServiceResource(Long id) throws Exception {
        ChargeServiceResourcesDO chargeServiceResourcesDO = this.getById(id);
        if (chargeServiceResourcesDO == null) {
            return CommonResult.error(400, "未找到关联资源");
        }
        boolean removed = this.removeById(id);
        if (!removed) {
            throw new ServiceException(500, "删除失败");
        }
        // 获取中间表中的 服务id, 排查该服务id下关联的资源还是否存在， 没有要重置服务状态为 未配置资源
        Long serviceId = chargeServiceResourcesDO.getServiceId();
        LambdaQueryWrapper<ChargeServiceResourcesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeServiceResourcesDO::getServiceId, serviceId);
        List<ChargeServiceResourcesDO> chargeServiceResourcesDOS = this.baseMapper.selectList(queryWrapper);
        if (chargeServiceResourcesDOS == null || chargeServiceResourcesDOS.isEmpty()) {
            // 该服务删除最后一个关联资源， 状态变更成未配置资源
            boolean updated = chargeProductServicePriceService.changeServiceResourceStatus(serviceId, 0);
            if (!updated) {
                throw new ServiceException(500, "删除失败");
            }
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<ChargeServiceResourcesDO>> getServiceResourcesListByServiceId(Long serviceId) {
        LambdaQueryWrapper<ChargeServiceResourcesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeServiceResourcesDO::getServiceId, serviceId);
        List<ChargeServiceResourcesDO> chargeServiceResourcesDOS = this.getBaseMapper().selectList(queryWrapper);

        if (CollectionUtil.isNotEmpty(chargeServiceResourcesDOS)) {
            List<Long> resourcesVersopmIds = chargeServiceResourcesDOS.stream().map(ChargeServiceResourcesDO::getResourcesId).toList();
            List<ChargeResourceServiceVersionInfoVO> chargeResourceServiceVersionVOS = chargeResourceServiceMapper.getVersionInfoById(null, resourcesVersopmIds);
            // 按 versionId 进行分组
            Map<Long, List<ChargeResourceServiceVersionInfoVO>> groupedByVersionId = chargeResourceServiceVersionVOS.stream()
                    .collect(Collectors.groupingBy(ChargeResourceServiceVersionInfoVO::getVersionId));

            chargeServiceResourcesDOS.forEach(resource -> {
                // 根据需要将分组信息设置到对应的 DO 对象中
                resource.setVersionInfoVO(groupedByVersionId.get(resource.getResourcesId()).getFirst());
            });
        }

        return CommonResult.success(chargeServiceResourcesDOS);
    }

    @Override
    public List<ChargeResource> getResourceListByServiceId(Long serviceId) {
        return this.baseMapper.getResourcesByServiceId(serviceId);
    }
}
