@SneakyThrows
    @Override
    public void onMessage(ReceiveIncomeBillMqDTO message) {
        handler(message);
    }

    public IncomeDetailBillErrorEnum handler(ReceiveIncomeBillMqDTO message) {
        try {
            // 添加空值检查，防止NullPointerException
            String businessId = message.getRequestParams() != null ? message.getRequestParams().getBusinessId() : null;
            String billId = message.getBillId();
            TraceIdUtil.buildAndSetTraceId(" ", billId, businessId);
            log.info("接收到收入账单MQ消息, message: {}", JsonUtils.toJsonString(message));
            EnableLoginContext.setContext(false);
            TenantContextHolder.setTenantId(message.getTenantId());

            if (!idempotentService.tryLockProcess(BillTypeEnum.INCOME.name(), billId, Duration.ofSeconds(10))) {
                log.warn("收入billId重复消费, billId: {}", billId);
                throw new IdempotentException("收入billId重复消费", Long.valueOf(billId));
            }