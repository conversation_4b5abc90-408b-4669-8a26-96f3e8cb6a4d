package com.linkcircle.boss.module.billing.web.detail.income.service.impl;

import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PostpaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.context.BusinessParamsContext;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.ScaleDataService;
import com.linkcircle.boss.module.billing.web.detail.income.mapper.PostpaidIncomeBillDetailMapper;
import com.linkcircle.boss.module.billing.web.detail.income.mapper.PrepaidIncomeBillDetailMapper;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.IncomeBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ServiceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.income.service.IncomeBillPersistenceService;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.chain.context.IncomeDetailBillRequestContext;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeResponse;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.BillStatusEnum;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import com.linkcircle.boss.module.crm.enums.PaymentMethodEnum;
import com.linkcircle.boss.module.crm.enums.WalletDeductStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-06-18 14:04
 * @description 账单持久化服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IncomeBillPersistenceServiceImpl implements IncomeBillPersistenceService {

    private final PrepaidIncomeBillDetailMapper prepaidIncomeBillDetailMapper;
    private final PostpaidIncomeBillDetailMapper postpaidIncomeBillDetailMapper;
    private final ScaleDataService scaleDataService;

    @Override
    public boolean saveIncomeDetailBill(String billId, IncomeDetailBillRequestContext requestContext) {
        log.info("保存预付费收入账单, billId: {}", billId);

        try {
            boolean prepared = requestContext.isPrepared();
            IncomeBillDetailRequestDTO requestParams = requestContext.getIncomeBillMqDTO().getRequestParams();
            Map<String, Object> businessData = requestParams.getData();
            // 量表id
            Long scaleId = requestContext.getServiceSubscriptionInfoDTO().getServiceConfig().getScaleId();

            Optional<Map<String, Object>> businessParamsOpt = scaleDataService.buildMatchBusinessParams(businessData, scaleId);

            // 如果有匹配的业务参数，设置到ThreadLocal上下文
            if (businessParamsOpt.isPresent() && !businessParamsOpt.get().isEmpty()) {
                BusinessParamsContext.setBusinessParams(businessParamsOpt.get());
                log.info("设置业务参数到ThreadLocal，参数数量: {}", businessParamsOpt.get().size());
            }
            Long businessTime = requestParams.getBusinessTime();
            String serviceCode = getServiceCode(requestContext);
            HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(businessTime, List.of(serviceCode));
            try (HintManager hintManager = HintManager.getInstance()) {
                // 保存预付费收入账单
                if (prepared) {
                    hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
                    PrepaidIncomeBillDetailDO detailDO = buildPrepaidIncomeBillDetailDO(billId, requestContext);
                    int insert = prepaidIncomeBillDetailMapper.insert(detailDO);
                    log.info("保存预付费收入账单成功, billId: {}, billDetailId: {}, insert: {}", billId, detailDO.getBillDetailId(), insert);
                    return insert > 0;
                }

                // 保存后付费收入账单
                hintManager.addTableShardingValue(LogicTableConstant.POSTPAID_INCOME_BILL_DETAIL, businessTimeDTO);
                PostpaidIncomeBillDetailDO detailDO = buildPostpaidIncomeBillDetailDO(billId, requestContext);
                int insert = postpaidIncomeBillDetailMapper.insert(detailDO);
                log.info("保存后付费收入账单成功, billId: {}, billDetailId: {}, insert: {}", billId, detailDO.getBillDetailId(), insert);
                return insert > 0;
            } catch (Exception e) {
                log.error("保存收入账单失败, billId: {}", billId, e);
                throw e;
            }
        } finally {
            BusinessParamsContext.clear();
        }
    }

    private String getServiceCode(IncomeDetailBillRequestContext requestContext) {
        AccountSubscriptionsVO.Service serviceConfig = requestContext.getServiceSubscriptionInfoDTO().getServiceConfig();
        Integer chargeType = serviceConfig.getChargeType();
        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(chargeType);
        switch (rateTypeEnum) {
            case FIXED, TIERED -> {
                return rateTypeEnum.name().toLowerCase();
            }
            case USAGE, PACKAGE -> {
                return requestContext.getServiceSubscriptionInfoDTO().getServiceConfig().getServiceCode();
            }
        }
        return "";
    }

    /**
     * 构建预付费收入账单详情DO
     *
     * @param billId         账单ID
     * @param requestContext 请求上下文
     * @return 预付费收入账单详情DO
     */
    private PrepaidIncomeBillDetailDO buildPrepaidIncomeBillDetailDO(String billId, IncomeDetailBillRequestContext requestContext) {
        IncomeBillDetailRequestDTO requestParams = requestContext.getIncomeBillMqDTO().getRequestParams();
        IncomeRateChargeResponse chargeResponse = requestContext.getIncomeRateChargeResponse();
        ServiceSubscriptionInfoDTO subscriptionInfo = requestContext.getServiceSubscriptionInfoDTO();
        CyclePeriodResultVO cyclePeriodResultVO = chargeResponse.getCyclePeriodResultVO();

        // 获取订阅信息
        AccountSubscriptionsVO subscriptionsVO = subscriptionInfo.getSubscriptionsVO();
        AccountSubscriptionsVO.Service serviceConfig = subscriptionInfo.getServiceConfig();

        // 当前时间戳
        long currentTime = System.currentTimeMillis();

        PrepaidIncomeBillDetailDO detailDO = PrepaidIncomeBillDetailDO.builder()
                .billDetailId(Long.valueOf(billId))
                .requestId(requestParams.getRequestId())
                .businessId(requestParams.getBusinessId())
                .businessTime(requestParams.getBusinessTime())
                .serviceId(requestParams.getServiceId())
                .serviceCode(serviceConfig.getServiceCode())
                .customerId(subscriptionsVO.getCustomerId())
                .accountId(subscriptionsVO.getAccountId())
                .productId(subscriptionInfo.getProductId())
                .entityId(subscriptionsVO.getEntityId())
                .contractId(subscriptionsVO.getContractId())
                .walletId(subscriptionsVO.getWalletsId())
                .planId(subscriptionsVO.getPlanId())
                .subscribeId(subscriptionsVO.getId())
                .billingType(serviceConfig.getChargeType())
                .paymentMethod(serviceConfig.getPaymentOptions())
                .inTrial(chargeResponse.getInTrial())
                .billStatus(BillStatusEnum.PENDING.getStatus())
                .walletDeductStatus(WalletDeductStatusEnum.NONE.getStatus())
                .pointAmount(chargeResponse.getPointAmount())
                .cashAmount(chargeResponse.getCashAmount())
                .taxRate(chargeResponse.getTaxRate())
                .usageCount(chargeResponse.getUsage())
                .usageUnit(chargeResponse.getUsageUnit())
                .chargeUsageCount(chargeResponse.getChargeUsageCount())
                .chargeMeasure(chargeResponse.getMeasure())
                .chargeMeasureUnit(chargeResponse.getMeasureUnit())
                .chargeMeasureCeil(chargeResponse.getMeasureCeil())
                .chargeUnitCount(chargeResponse.getChargeUnitCount())
                .originalUnitPrice(chargeResponse.getOriginalUnitPrice())
                .originalPrice(chargeResponse.getOriginalPrice())
                .discountedUnitPrice(chargeResponse.getDiscountedUnitPrice())
                .discountedPrice(chargeResponse.getDiscountedPrice())
                .availableInvoiceAmount(chargeResponse.getAmountWithTax())
                .amountWithTax(chargeResponse.getAmountWithTax())
                .amountWithoutTax(chargeResponse.getAmountWithoutTax())
                .currency(chargeResponse.getCurrency())
                .billingTime(currentTime)
                .createTime(currentTime)
                .deleted(false)
                .billingStartTime(cyclePeriodResultVO.getCycleStartTime())
                .billingEndTime(cyclePeriodResultVO.getCycleEndTime())
                .billingCycleType(cyclePeriodResultVO.getPeriodUnitEnum().getUnit())
                .billingDay(cyclePeriodResultVO.getPeriod())
                .billingCycle(cyclePeriodResultVO.getBillingCycle())
                // TODO 订阅开始结束时间
                .rateDetails(JsonUtils.toJsonString(chargeResponse.getRateConfig()))
                .discountDetails(JsonUtils.toJsonString(chargeResponse.getCouponList()))
                .build();
        Integer paymentMethod = detailDO.getPaymentMethod();
        if (PaymentMethodEnum.CASH.getMethod().equals(paymentMethod)) {
            detailDO.setCashAmount(detailDO.getAmountWithTax());
        } else {
            detailDO.setPointAmount(detailDO.getDiscountedPrice());
        }
        return detailDO;
    }

    /**
     * 构建后付费收入账单详情DO
     *
     * @param billId         账单ID
     * @param requestContext 请求上下文
     * @return 后付费收入账单详情DO
     */
    private PostpaidIncomeBillDetailDO buildPostpaidIncomeBillDetailDO(String billId, IncomeDetailBillRequestContext requestContext) {
        IncomeBillDetailRequestDTO requestParams = requestContext.getIncomeBillMqDTO().getRequestParams();
        IncomeRateChargeResponse chargeResponse = requestContext.getIncomeRateChargeResponse();
        ServiceSubscriptionInfoDTO subscriptionInfo = requestContext.getServiceSubscriptionInfoDTO();
        CyclePeriodResultVO cyclePeriodResultVO = chargeResponse.getCyclePeriodResultVO();

        // 获取订阅信息
        AccountSubscriptionsVO subscriptionsVO = subscriptionInfo.getSubscriptionsVO();
        AccountSubscriptionsVO.Service serviceConfig = subscriptionInfo.getServiceConfig();

        // 当前时间戳
        long currentTime = System.currentTimeMillis();

        PostpaidIncomeBillDetailDO detailDO = PostpaidIncomeBillDetailDO.builder()
                .billDetailId(Long.valueOf(billId))
                .requestId(requestParams.getRequestId())
                .businessId(requestParams.getBusinessId())
                .businessTime(requestParams.getBusinessTime())
                .serviceId(requestParams.getServiceId())
                .serviceCode(serviceConfig.getServiceCode())
                .customerId(subscriptionsVO.getCustomerId())
                .accountId(subscriptionsVO.getAccountId())
                .productId(subscriptionInfo.getProductId())
                .entityId(subscriptionsVO.getEntityId())
                .contractId(subscriptionsVO.getContractId())
                .walletId(subscriptionsVO.getWalletsId())
                .planId(subscriptionsVO.getPlanId())
                .subscribeId(subscriptionsVO.getId())
                .billingType(serviceConfig.getChargeType())
                .paymentMethod(serviceConfig.getPaymentOptions())
                .inTrial(chargeResponse.getInTrial())
                .billStatus(BillStatusEnum.DRAFT.getStatus())
                .walletDeductStatus(WalletDeductStatusEnum.NONE.getStatus())
                .pointAmount(chargeResponse.getPointAmount())
                .cashAmount(chargeResponse.getCashAmount())
                .taxRate(chargeResponse.getTaxRate())
                .usage(chargeResponse.getUsage())
                .usageUnit(chargeResponse.getUsageUnit())
                .chargeUsageCount(chargeResponse.getChargeUsageCount())
                .chargeMeasure(chargeResponse.getMeasure())
                .chargeMeasureUnit(chargeResponse.getMeasureUnit())
                .chargeMeasureCeil(chargeResponse.getMeasureCeil())
                .chargeUnitCount(chargeResponse.getChargeUnitCount())
                .originalUnitPrice(chargeResponse.getOriginalUnitPrice())
                .discountedUnitPrice(chargeResponse.getDiscountedUnitPrice())
                .originalPrice(chargeResponse.getOriginalPrice())
                .discountedPrice(chargeResponse.getDiscountedPrice())
                .availableInvoiceAmount(chargeResponse.getAmountWithTax())
                .amountWithTax(chargeResponse.getAmountWithTax())
                .amountWithoutTax(chargeResponse.getAmountWithoutTax())
                .currency(chargeResponse.getCurrency())
                .billingTime(currentTime)
                .createTime(currentTime)
                .deleted(false)
                .billingStartTime(cyclePeriodResultVO.getCycleStartTime())
                .billingEndTime(cyclePeriodResultVO.getCycleEndTime())
                .billingCycleType(cyclePeriodResultVO.getPeriodUnitEnum().getUnit())
                .billingDay(cyclePeriodResultVO.getPeriod())
                .billingCycle(cyclePeriodResultVO.getBillingCycle())
                // TODO 订阅开始结束时间
                .rateDetails(JsonUtils.toJsonString(chargeResponse.getRateConfig()))
                .discountDetails(JsonUtils.toJsonString(chargeResponse.getCouponList()))
                .build();
        Integer paymentMethod = detailDO.getPaymentMethod();
        if (PaymentMethodEnum.CASH.getMethod().equals(paymentMethod)) {
            detailDO.setCashAmount(detailDO.getDiscountedPrice());
        } else {
            detailDO.setPointAmount(detailDO.getDiscountedPrice());
        }
        return detailDO;
    }

}
