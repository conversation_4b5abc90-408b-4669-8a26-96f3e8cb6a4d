package com.linkcircle.boss.module.billing.web.bill.resource.scheduled.consumer;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.model.dto.CostPostpaidResourceBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.CostPostpaidResourceBillingCalculateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-17
 * @description 后付费资源服务出账消息消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = ChargeTopicConstant.CHARGE_NORMAL_TOPIC,
        selectorExpression = ChargeTopicConstant.TAG_COST_BILL_SERVICE_OUT,
        consumerGroup = ChargeTopicConstant.GROUP_COST_BILL_SERVICE_OUT,
        maxReconsumeTimes = 16
)
public class CostPostpaidResourceBillingConsumer implements RocketMQListener<CostPostpaidResourceBillingMessageDTO> {

    private final CostPostpaidResourceBillingCalculateService costPostpaidResourceBillingCalculateService;

    @Override
    public void onMessage(CostPostpaidResourceBillingMessageDTO message) {
        try {
            log.info("收到后付费资源服务出账消息: {}", message);
            costPostpaidResourceBillingCalculateService.processBillingCalculation(message);
        } catch (Exception e) {
            log.error("处理后付费资源服务出账消息异常: {}", message, e);
            throw e;
        }
    }
}