package com.linkcircle.boss.module.charge.crm.web.currency.api;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.crm.web.currency.service.ChargeCurrencyService;
import com.linkcircle.boss.module.crm.api.currency.CurrencyApi;
import com.linkcircle.boss.module.crm.api.currency.vo.CurrencyRespVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 15:16
 */
@Tag(name = "管理后台 - 货币管理")
@RestController
@RequestMapping("/charge-currency")
@Validated
@RequiredArgsConstructor
public class CurrencyApiController implements CurrencyApi {


    private final  ChargeCurrencyService chargeCurrencyService;
    @Override
    public CommonResult<List<CurrencyRespVO>> list() {
        return chargeCurrencyService.listCurrency();
    }
}
