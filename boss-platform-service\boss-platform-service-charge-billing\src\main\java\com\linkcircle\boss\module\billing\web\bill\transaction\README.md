# 统一事务处理框架

## 概述

为了解决RocketMQ事务监听器冲突问题（"rocketMQTemplate already exists RocketMQLocalTransactionListener"），我们设计了一个统一的事务处理框架。

## 问题背景

在原有架构中，系统存在多个直接实现`RocketMQLocalTransactionListener`接口的类：
- `IncomeServiceBillingTransactionListener` - 收入服务计费事务监听器
- `CostResourceBillingTransactionListener` - 成本资源计费事务监听器

这些类都使用`@RocketMQTransactionListener`注解，导致Spring启动时冲突。

## 解决方案

### 架构设计

1. **统一事务监听器** (`UnifiedTransactionListener`)
   - 唯一实现`RocketMQLocalTransactionListener`接口的类
   - 负责接收所有事务消息并路由到对应的处理器

2. **事务处理器接口** (`TransactionProcessor`)
   - 定义事务处理的标准接口
   - 各业务模块实现此接口来处理具体的事务逻辑

3. **事务路由器** (`TransactionRouter`)
   - 根据消息头中的事务类型标识路由到对应的处理器
   - 管理所有事务处理器的注册和查找

4. **事务消息类型枚举** (`TransactionMessageTypeEnum`)
   - 定义支持的事务消息类型
   - 提供类型编码和描述信息

### 核心组件

#### 1. 事务消息类型枚举
```java
public enum TransactionMessageTypeEnum {
    INCOME_SERVICE_BILLING("INCOME_SERVICE_BILLING", "收入服务计费事务"),
    COST_RESOURCE_BILLING("COST_RESOURCE_BILLING", "成本资源计费事务");
}
```

#### 2. 事务处理器接口
```java
public interface TransactionProcessor {
    TransactionMessageTypeEnum getSupportedType();
    RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg);
    RocketMQLocalTransactionState checkLocalTransaction(Message msg);
}
```

#### 3. 统一事务监听器
- 从消息头获取`transactionType`字段
- 通过`TransactionRouter`路由到对应的处理器
- 统一的异常处理和日志记录

### 实现的处理器

1. **收入服务计费事务处理器** (`IncomeServiceBillingTransactionProcessor`)
   - 处理收入服务计费相关的事务
   - 支持类型：`INCOME_SERVICE_BILLING`

2. **成本资源计费事务处理器** (`CostResourceBillingTransactionProcessor`)
   - 处理成本资源计费相关的事务
   - 支持类型：`COST_RESOURCE_BILLING`

## 使用方法

### 发送事务消息

在发送事务消息时，需要在消息头中添加`transactionType`字段：

```java
Message<MessageDTO> msg = MessageBuilder
    .withPayload(messageDTO)
    .setHeader("extra", JsonUtils.toJsonString(callbackDTO))
    .setHeader("transactionType", TransactionMessageTypeEnum.INCOME_SERVICE_BILLING.getCode())
    .build();
```

### 添加新的事务类型

1. 在`TransactionMessageTypeEnum`中添加新的枚举值
2. 实现`TransactionProcessor`接口
3. 使用`@Component`注解注册为Spring Bean
4. 在发送消息时设置对应的`transactionType`

## 迁移说明

### 已迁移的组件

- `IncomeServiceBillingTransactionListener` → `IncomeServiceBillingTransactionProcessor`
- `CostResourceBillingTransactionListener` → `CostResourceBillingTransactionProcessor`

### 原有监听器处理

原有的事务监听器已被重命名为`*_Deprecated`并注释掉`@RocketMQTransactionListener`注解，避免冲突。

## 优势

1. **解决冲突** - 只有一个类实现`RocketMQLocalTransactionListener`接口
2. **可扩展** - 新增事务类型无需修改核心框架
3. **统一管理** - 所有事务处理逻辑集中管理
4. **保持兼容** - 现有业务逻辑无需修改
5. **易于维护** - 清晰的架构分层和职责分离

## 注意事项

1. 发送事务消息时必须在消息头中设置`transactionType`字段
2. 新的事务处理器必须实现`TransactionProcessor`接口
3. 事务类型枚举值必须唯一
4. 原有的事务监听器已废弃，请使用新的处理器架构
