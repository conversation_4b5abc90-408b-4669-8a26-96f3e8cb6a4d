package com.linkcircle.boss.module.charge.fee.web.payment.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.fee.web.payment.model.entity.UnifiedRechargeOrderRq;

/**
 * <p>
 * 统一充值下单请求 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IUnifiedRechargeOrderRqService extends IService<UnifiedRechargeOrderRq> {

    CommonResult<JSONObject> unifiedOrder(Long customerId, Long amount,
                                          String clientIpm, Long walletId, String reqTimeZone) throws Exception;

}
