package com.linkcircle.boss.module.crm.api.customer.customer;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:07
 */
@org.springframework.stereotype.Component
@lombok.extern.slf4j.Slf4j
public class CustomerApiFallback implements FallbackFactory<CustomerApi> {
    @Override
    public CustomerApi create(Throwable cause) {

        return new CustomerApi() {
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO> findById(Long id) {
                log.error("调用客户账户信息API失败，id: {} 异常信息: ", id, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<java.util.List<com.linkcircle.boss.module.crm.api.common.vo.CommonVO>> findNameByIds(CommonDTO commonDTO) {
                log.error("调用客户账户信息API失败，commonDTO: {} 异常信息: ", commonDTO, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerAccountsInfo> getAccountInfo(Long id) {
                log.error("调用客户账户信息API失败，id: {} 异常信息: ", id, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
