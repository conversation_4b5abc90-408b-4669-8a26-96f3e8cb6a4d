package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票申请 Req DTO
 *
 * <AUTHOR> zyuan
 * @data : 2025-06-30
 */
@Data
@Schema(description = "发票审核 Request DTO")
@EqualsAndHashCode(callSuper = false)
public class InvoiceToExamineDTO {

    @Schema(description = "发票ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1092910901")
    private Long id;

    /**
     * 状态 2-通过 1-拒绝
     */
    @Schema(description = "状态：1-拒绝 2-通过", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;
}
