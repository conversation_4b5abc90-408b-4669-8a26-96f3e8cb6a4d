package com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.ProductServiceTieredPriceDTO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.*;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/2 17:12
 */
@Schema(description = "手工账单-产品服务账单详情对象 Response DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MakeupIncomeProductServiceBillDetailVo {

    /**
     * 产品服务账单id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "产品服务账单id")
    private Long productServiceBillId;



    /**
     * 产品账单id
     */
    @TableField("product_bill_id")
    @Schema(description = "产品账单id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long productBillId;

    /**
     * 账单id
     */
    @TableField("bill_id")
    @Schema(description = "账单id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long billId;

    /**
     * 主体id
     */
    @TableField("entity_id")
    @Schema(description = "主体id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long entityId;

    /**
     * 主体名称
     */
    @TableField("entity_name")
    @Schema(description = "主体名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String entityName;

    @Schema(description = "合同名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contractName;
    /**
     * 合同id
     */
    @TableField("contract_id")
    @Schema(description = "合同id")
    private Long contractId;

    /**
     * 客户名称
     */
    @TableField("customer_name")
    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerName;

    /**
     * 客户id
     */
    @TableField("customer_id")
    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    /**
     * 账户名称
     */
    @TableField("account_name")
    @Schema(description = "账户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accountName;

    /**
     * 账户id
     */
    @TableField("account_id")
    @Schema(description = "账户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

    /**
     * 钱包名称
     */
    @TableField("wallet_name")
    @Schema(description = "钱包名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String walletName;

    /**
     * 钱包id
     */
    @TableField("wallet_id")
    @Schema(description = "钱包id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long walletId;





    /**
     * 产品id
     */
    @TableField("product_id")
    @Schema(description = "产品id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long productId;

    /**
     * 产品名称
     */
    @TableField("product_name")
    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    /**
     * 服务id
     */
    @TableField("service_id")
    @Schema(description = "服务id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long serviceId;

    /**
     * 服务名称
     */
    @TableField("service_name")
    @Schema(description = "服务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serviceName;


    /**
     * 单价
     */
    @TableField("unit_price")
    @Schema(description = "单价")
    private BigDecimal unitPrice;

    /**
     * 消耗量
     */
    @TableField("usage")
    @Schema(description = "消耗量")
    private BigDecimal usageCount;

    /**
     * 消耗量单位
     */
    @TableField("usage_unit")
    @Schema(description = "消耗量单位")
    private String usageUnit;

    /**
     * 税率
     */
    @TableField("tax_rate")
    @Schema(description = "税率")
    private BigDecimal taxRate;

    /**
     * 目录价(原价)
     */
    @TableField("original_price")
    @Schema(description = "目录价(原价)")
    private BigDecimal originalPrice;

    /**
     * 含税总金额
     */
    @TableField("amount_with_tax")
    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @TableField("amount_without_tax")
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */
    @TableField("currency")
    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 出账时间戳
     */
    @TableField("billing_time")
    @Schema(description = "出账时间戳")
    private Long billingTime;

    /**
     * 数据创建时间戳
     */
    @TableField("create_time")
    @Schema(description = "数据创建时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long createTime;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Integer deleted;

    @Schema(description = "固定费率配置")
    private FixedRateConfigFeeDTO fixedRateConfig;

    @Schema(description = "套餐计费配置")
    private PackageRateConfigFeeDTO packageRateConfig;

    @Schema(description = "阶梯费率配置")
    private TierRateConfigFeeDTO tierRateConfig;


    @Schema(description = "按量计费配置")
    private UsageBasedRateConfigFeeDTO usageBasedRateConfig;

    /**
     * 费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*
     */
    @Schema(description = "费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*")
    @TableField("rate_details")
    private String rateDetails;


    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>")
    @TableField("discount_details")
    private String discountDetails;

    @Schema(description = "优惠配置")
    private List<BillDiscountConfigDTO> coupons;

    /**
     * 计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费
     */
    @TableField("billing_type")
    @Schema(description = "计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer billingType;

    @Schema(description = "服务价格详情")
    private ChargeProductServicePriceVo servicePriceVo;

}
