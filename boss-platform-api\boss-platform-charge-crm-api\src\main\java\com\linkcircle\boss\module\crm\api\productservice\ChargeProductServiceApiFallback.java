package com.linkcircle.boss.module.crm.api.productservice;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo;
import com.linkcircle.boss.module.crm.api.productservice.vo.ProductServiceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 13:59
 */
@Slf4j
@Component
public class ChargeProductServiceApiFallback implements FallbackFactory<ChargeProductServiceApi> {
    @Override
    public ChargeProductServiceApi create(Throwable cause) {
        return new ChargeProductServiceApi() {
            @Override
            public CommonResult<List<CommonVO>> findNameByIds(CommonDTO commonDTO) {
                log.error("调用产品服务中心API失败API失败，accountId: {}, 异常信息: ", commonDTO, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<List<ProductServiceVO>> allService() {
                log.error("调用产品服务中心API失败API失败，accountId: {}, 异常信息: ", "", cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<ChargeProductServicePriceVo> findById(Long serviceId) {
                log.error("调用产品服务中心API失败API失败，accountId: {}, 异常信息: ", serviceId, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }
}
