package com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid;

import com.baomidou.mybatisplus.annotation.TableField;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.ChargeCustomerInvoiceDTO;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/16 16:40
 */
@Schema(description = "账单-后付费-详情查询 Response DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostpaidIncomeProductBillDetailVO {


    @Schema(description = "服务列表")
    private List<PostpaidIncomeServiceVO> services;

    /**
     * 产品账单id
     */
    @Schema(description = "产品账单id")
    private Long productBillId;

    /**
     * 业务产生的时间戳
     */
    @Schema(description = "业务产生的时间戳")
    private Long businessTime;


    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customerId;

    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 账户id
     */
    @Schema(description = "账户id")
    private Long accountId;

    @Schema(description = "账户名称")
    private String accountName;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;
    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;
    /**
     * 主体id
     */
    @Schema(description = "主体id")
    private Long entityId;
    /**
     * 主体名称
     */
    @Schema(description = "主体名称")
    private String entityName;
    /**
     * 合同id
     */
    @Schema(description = "合同id")
    private Long contractId;
    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String contractName;
    /**
     * 钱包id
     */
    @Schema(description = "钱包id")
    private Long walletId;
    /**
     * 钱包名称
     */
    @Schema(description = "钱包名称")
    private String walletName;
    /**
     * 计划id
     */
    @Schema(description = "计划id")
    private Long planId;

    @Schema(description = "计划名称")
    private String planName;
    /**
     * 优惠id
     */
    @Schema(description = "优惠id")
    private Long discountId;

    /**
     * 订阅id
     */
    @Schema(description = "订阅id")
    private Long subscribeId;

    @Schema(description = "订阅名称")
    private String subscribeName;

    /**
     * 事件名称
     */
    @Schema(description = "事件名称")
    private String eventName;

    /**
     * 消耗量
     */
    @Schema(description = "消耗量")
    private BigDecimal usageCount;

    /**
     * 消耗量单位
     */
    @Schema(description = "消耗量单位")
    private String usageUnit;

    /**
     * 支付方式 0-现金, 1-积分
     */
    @Schema(description = "支付方式 0-现金, 1-积分")
    private Integer paymentMethod;

    /**
     * 计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费
     */
    @Schema(description = "计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费")
    private Integer billingType;

    /**
     * 在试用期内
     */
    @Schema(description = "在试用期内")
    private Boolean inTrial;

    /**
     * 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
     */
    @Schema(description = "账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清")
    private Integer billStatus;

    /**
     * 税率
     */
    @Schema(description = "税率")
    private BigDecimal taxRate;

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal unitPrice;

    /**
     * 目录价(原价)
     */
    @Schema(description = "目录价(原价)")
    private BigDecimal cataloguePrice;

    /**
     * 订阅价(优惠后的价格)
     */
    @Schema(description = "订阅价(优惠后的价格)")
    private BigDecimal originalPrice;

    /**
     * 订阅价(优惠后的价格)
     */
    @Schema(description = "订阅价(优惠后的价格)")
    private BigDecimal discountedPrice;

    /**
     * 税率
     */
    @TableField("tax_amount")
    @Schema(description = "税率金额总计")
    private BigDecimal taxAmount;


    // 各个服务总价  = 各个服务原价  - 折扣  + 税率
    @TableField("sub_total_amount")
    @Schema(description = "总价=各个服务总价之和")
    private BigDecimal subTotalAmount;


    /**
     * 优惠的金额
     */
    @Schema(description = "账单优惠的金额")
    private BigDecimal discountAmount;

    /**
     * 已开票金额
     */
    @Schema(description = "已开票金额")
    private BigDecimal invoicedAmount;

    /**
     * 可开票金额(=优惠价-已开票金额)
     */
    @Schema(description = "可开票金额(=优惠价-已开票金额)")
    private BigDecimal availableInvoiceAmount;

    /**
     * 含税总金额
     */
    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */
    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 实际支付时间戳
     */
    @Schema(description = "实际支付时间戳")
    private Long paymentTime;

    /**
     * 出账时间戳
     */
    @Schema(description = "出账时间戳")
    private Long billingTime;

    /**
     * 数据创建时间戳
     */
    @Schema(description = "数据创建时间戳")
    private Long createTime;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Boolean deleted;




    //@Schema(description = "客户详细信息")
    private ChargeCustomerInfoVO customer;

    @Schema(description = "发票客户账户详细信息")
    private ChargeCustomerInvoiceDTO customerInvoice;

    @Schema(description = "主体详细信息")
    private EntityDetailsVO entity;

    @Schema(description = "发票客户账户详细信息")
    private EntityDetailsVO entityInvoice;


    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "产品优惠详情json")
    @TableField("discount_details")
    private String discountDetails;

    /**
     * 账单优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "账单优惠详情json")
    @TableField("bill_discount_details")
    private String billDiscountDetails;

    @Schema(description = "账单优惠配置列表")
    private List<BillDiscountConfigDTO> billCoupons;

    @Schema(description = "产品优惠配置列表")
    private List<BillDiscountConfigDTO> productCoupons;

    @Schema(description = "出账开始时间戳（毫秒）")
    private Long billingStartTime;

    @Schema(description = "出账结束时间戳（毫秒）")
    private Long billingEndTime;


    /**
     * 已支付金额
     */
    @TableField("paid_amount")
    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    /**
     * 未支付金额
     */
    @TableField("unpaid_amount")
    @Schema(description = "未支付金额")
    private BigDecimal unpaidAmount;



    @TableField("bill_no")
    @Schema(description = "账单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billNo;

    /**
     * 开票退款金额
     */
    @TableField("refund_invoice_amount")
    @Schema(description = "开票退款金额")
    private BigDecimal refundInvoiceAmount;


    @Schema(description = "账单所有内容")
    private List<BillContent> showContents;

    @Schema(description = "账单所有优惠")
    private List<BillCoupon> showCoupons;
}
