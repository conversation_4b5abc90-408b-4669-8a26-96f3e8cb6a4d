package com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-23 09:02
 * @description 收入-固定费率周期状态表
 */
@TableName("income_fixed_rate_cycle_status")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "固定费率周期状态表")
public class IncomeFixedRateCycleStatusDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 订阅ID
     */
    @TableField("subscription_id")
    @Schema(description = "订阅ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long subscriptionId;

    /**
     * 账户I
     */
    @TableField("account_id")
    @Schema(description = "账户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

    /**
     * 服务ID
     */
    @TableField("service_id")
    @Schema(description = "服务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long serviceId;

    /**
     * 计费周期标识，如2025-01
     */
    @TableField("billing_cycle")
    @Schema(description = "计费周期标识，如2025-01", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billingCycle;

    /**
     * 计费周期开始时间戳
     */
    @TableField("billing_cycle_start")
    @Schema(description = "计费周期开始时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long billingCycleStart;

    /**
     * 计费周期结束时间戳
     */
    @TableField("billing_cycle_end")
    @Schema(description = "计费周期结束时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long billingCycleEnd;

    /**
     * 周期内累计消耗量
     */
    @TableField("total_usage")
    @Schema(description = "周期内累计消耗量", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal totalUsage;

    /**
     * 计费时间戳
     */
    @TableField("billing_time")
    @Schema(description = "计费时间戳")
    private Long billingTime;

    /**
     * 货币类型
     */
    @TableField("currency")
    @Schema(description = "货币类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currency;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long createTime;

}
