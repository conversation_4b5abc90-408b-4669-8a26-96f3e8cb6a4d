package com.linkcircle.boss.module.report.web.bill.service.impl;

import cn.hutool.core.text.StrFormatter;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.crm.api.productservice.ChargeProductServiceApi;
import com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.report.web.bill.model.vo.makeup.MakeupIncomeProductServiceBillDetailVo;
import com.linkcircle.boss.module.report.web.bill.model.vo.postpaid.PostpaidIncomeServiceVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.prepaid.PrepaidIncomeBillDetailVO;
import com.linkcircle.boss.module.report.web.bill.service.BillContentHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/7/28 10:06
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BillContentHandlerImpl implements BillContentHandler {

    private final ChargeProductServiceApi chargeProductServiceApi;


    static abstract  class   ServiceApi{

        private Object delegate;
        public ServiceApi(Object delegate){
            this.delegate = delegate;
        }
        public<T> T getDelegate(Class<T> clazz) {
            return (T) delegate;
        }
        @Schema(description = "固定费率配置")
        public  abstract FixedRateConfigFeeDTO getFixedRateConfig();

        @Schema(description = "套餐计费配置")
        public abstract PackageRateConfigFeeDTO getPackageRateConfig();

        @Schema(description = "阶梯费率配置")
        public abstract TierRateConfigFeeDTO getTierRateConfig();


        //@Schema(description = "0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费")
        @Schema(description = "计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费", requiredMode = Schema.RequiredMode.REQUIRED)
        public abstract Integer getBillingType();


        @Schema(description = "按量计费配置")
        public abstract UsageBasedRateConfigFeeDTO getUsageBasedRateConfig();



        public abstract String getCurrency();

        public abstract String getServiceName();

        public abstract Long getServiceId();

        public abstract  void setServicePriceVo(ChargeProductServicePriceVo servicePriceVo);
    }

    @Override
    public List<BillCoupon> handleCoupons(List<BillDiscountConfigDTO> billCoupons, Long productId, Long serviceId, String currencySymbol) {
        List<BillCoupon> coupons = new ArrayList<>();
        if (CollectionUtils.isEmpty(billCoupons)) {
            return coupons;
        }
        for (BillDiscountConfigDTO billCoupon : billCoupons) {
            log.info("handleCoupons: productId={}, serviceId={}, billCoupon={}", productId, serviceId, billCoupon);
            BillCoupon coupon = new BillCoupon();
            String couponName = billCoupon.getCouponName();
            if (Objects.equals(billCoupon.getCouponType(), 1)) {
                coupon.setDescription(couponName + "(" + billCoupon.getCouponPercentage() + ")");
            } else {
                coupon.setDescription(couponName + "(" + billCoupon.getCouponAmount() + ")");
            }
            coupon.setTotalAmount("-" + billCoupon.getDiscountAmount() + currencySymbol);
            coupon.setDiscountPrice(coupon.getTotalAmount());
        }
        return coupons;
    }


    public ServiceApi convertToServiceApi(MakeupIncomeProductServiceBillDetailVo service){
       return  new ServiceApi(service){
            @Override
            public FixedRateConfigFeeDTO getFixedRateConfig() {
                return getDelegate(MakeupIncomeProductServiceBillDetailVo.class).getFixedRateConfig();
            }

            @Override
            public PackageRateConfigFeeDTO getPackageRateConfig() {
                return getDelegate(MakeupIncomeProductServiceBillDetailVo.class).getPackageRateConfig();
            }

            @Override
            public TierRateConfigFeeDTO getTierRateConfig() {
                return getDelegate(MakeupIncomeProductServiceBillDetailVo.class).getTierRateConfig();
            }

           @Override
           public Integer getBillingType() {
               return getDelegate(MakeupIncomeProductServiceBillDetailVo.class).getBillingType();
           }

           @Override
            public UsageBasedRateConfigFeeDTO getUsageBasedRateConfig() {
                return getDelegate(MakeupIncomeProductServiceBillDetailVo.class).getUsageBasedRateConfig();
            }

           @Override
            public String getCurrency() {
                return getDelegate(MakeupIncomeProductServiceBillDetailVo.class).getCurrency();
            }

            @Override
            public String getServiceName() {
                return getDelegate(MakeupIncomeProductServiceBillDetailVo.class).getServiceName();
            }

           @Override
           public Long getServiceId() {
               return getDelegate(MakeupIncomeProductServiceBillDetailVo.class).getServiceId();
           }

           @Override
           public void setServicePriceVo(ChargeProductServicePriceVo servicePriceVo) {
               getDelegate(MakeupIncomeProductServiceBillDetailVo.class).setServicePriceVo(servicePriceVo);
           }
       };
    }

    public ServiceApi convertToServiceApi(PostpaidIncomeServiceVO service){
        return  new ServiceApi(service){
            @Override
            public FixedRateConfigFeeDTO getFixedRateConfig() {
                return getDelegate(PostpaidIncomeServiceVO.class).getFixedRateConfig();
            }

            @Override
            public PackageRateConfigFeeDTO getPackageRateConfig() {
                return getDelegate(PostpaidIncomeServiceVO.class).getPackageRateConfig();
            }

            @Override
            public TierRateConfigFeeDTO getTierRateConfig() {
                return getDelegate(PostpaidIncomeServiceVO.class).getTierRateConfig();
            }

            @Override
            public UsageBasedRateConfigFeeDTO getUsageBasedRateConfig() {
                return getDelegate(PostpaidIncomeServiceVO.class).getUsageBasedRateConfig();
            }

            @Override
            public String getCurrency() {
                return getDelegate(PostpaidIncomeServiceVO.class).getCurrency();
            }

            @Override
            public String getServiceName() {
                return getDelegate(PostpaidIncomeServiceVO.class).getServiceName();
            }

            @Override
            public Long getServiceId() {
                return getDelegate(PostpaidIncomeServiceVO.class).getServiceId();
            }

            @Override
            public void setServicePriceVo(ChargeProductServicePriceVo servicePriceVo) {
                getDelegate(PostpaidIncomeServiceVO.class).setServicePriceVo(servicePriceVo);
            }
            @Override
            public Integer getBillingType() {
                return getDelegate(PostpaidIncomeServiceVO.class).getBillingType();
            }
        };
    }


    public ServiceApi convertToServiceApi(PrepaidIncomeBillDetailVO service){
        return  new ServiceApi(service){
            @Override
            public FixedRateConfigFeeDTO getFixedRateConfig() {
                return getDelegate(PrepaidIncomeBillDetailVO.class).getFixedRateConfig();
            }

            @Override
            public PackageRateConfigFeeDTO getPackageRateConfig() {
                return getDelegate(PrepaidIncomeBillDetailVO.class).getPackageRateConfig();
            }

            @Override
            public TierRateConfigFeeDTO getTierRateConfig() {
                return getDelegate(PrepaidIncomeBillDetailVO.class).getTierRateConfig();
            }

            @Override
            public UsageBasedRateConfigFeeDTO getUsageBasedRateConfig() {
                return getDelegate(PrepaidIncomeBillDetailVO.class).getUsageBasedRateConfig();
            }

            @Override
            public String getCurrency() {
                return getDelegate(PrepaidIncomeBillDetailVO.class).getCurrency();
            }

            @Override
            public String getServiceName() {
                return getDelegate(PrepaidIncomeBillDetailVO.class).getServiceName();
            }

            @Override
            public Long getServiceId() {
                return getDelegate(PrepaidIncomeBillDetailVO.class).getServiceId();
            }
            @Override
            public void setServicePriceVo(ChargeProductServicePriceVo servicePriceVo) {
                getDelegate(PrepaidIncomeBillDetailVO.class).setServicePriceVo(servicePriceVo);
            }
            @Override
            public Integer getBillingType() {
                return getDelegate(PrepaidIncomeBillDetailVO.class).getBillingType();
            }
        };
    }


    @Override
    public void handleContent(BillContent productContent, MakeupIncomeProductServiceBillDetailVo service) {
        handleContentInternal(productContent, convertToServiceApi(service));
    }

    @Override
    public void handleContent(BillContent productContent, PostpaidIncomeServiceVO service) {
        handleContentInternal(productContent, convertToServiceApi(service));
    }

    @Override
    public void handleContent(BillContent productContent, PrepaidIncomeBillDetailVO service) {
        handleContentInternal(productContent, convertToServiceApi(service));
    }


    public void handleContentInternal(BillContent productContent, ServiceApi service){
        CommonResult<ChargeProductServicePriceVo> commonResult = chargeProductServiceApi.findById(service.getServiceId());
        ChargeProductServicePriceVo servicePriceVo = commonResult.getData();
        service.setServicePriceVo(servicePriceVo);
        switch (servicePriceVo.getChargeType()) {
            case 0:
                handleFixedContent(productContent, service,servicePriceVo);
                break;
            case 1:
                handleTierContent(productContent, service,servicePriceVo);
                break;
            case 2:
                handlePackageContent(productContent, service, servicePriceVo);
                break;
            case 3:
                handleUsageContent(productContent, service, servicePriceVo);
                break;
        }
    }

    private void handleUsageContent(BillContent productContent, ServiceApi service, ChargeProductServicePriceVo servicePriceVo) {
        UsageBasedRateConfigFeeDTO usageBasedRateConfig = service.getUsageBasedRateConfig();
        String priceModel = usageBasedRateConfig.getPriceModel();
        if(StringUtils.equalsIgnoreCase(priceModel,"fixed")){
            handleFixedContent(productContent, service,servicePriceVo);
        }else{
            handleTierContent(productContent, service,servicePriceVo);
        }
    }

    private void handleTierContent(BillContent productContent, ServiceApi service, ChargeProductServicePriceVo servicePriceVo) {


        if (Objects.equals(service.getBillingType(),3)) {
            List<UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO> stairRatePrices  = service.getUsageBasedRateConfig().getStairRatePrices();
            handleTierContent(servicePriceVo, productContent, service, stairRatePrices);
        }else{
            TierRateConfigFeeDTO tierRateConfig = service.getTierRateConfig();
            handleTierContent(servicePriceVo, productContent, service, tierRateConfig);
        }


    }
    private void handleTierContent(ChargeProductServicePriceVo servicePriceVo
            , BillContent parentContent
            ,ServiceApi service, List<UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO> stairRatePrices
    ) {
        BillContent serviceContent = new BillContent();
        serviceContent.setDescription(service.getServiceName());
        parentContent.getChildren().add(serviceContent);

        if (CollectionUtils.isNotEmpty(stairRatePrices)) {
            for (UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO stairRatePrice : stairRatePrices) {
                List<TierRateConfigFeeDTO.TierPriceFeeDTO> tierPrices = stairRatePrice.getTierPrices();
                boolean isLast = false;
                TierRateConfigFeeDTO.TierPriceFeeDTO lastStairRatePrice = tierPrices.getLast();
                for (TierRateConfigFeeDTO.TierPriceFeeDTO tierPrice : tierPrices) {
                        isLast = lastStairRatePrice.equals(tierPrice);
                        BillContent stairRateContent = new BillContent();
                        if (isLast) {
                            stairRateContent.setDescription(StrFormatter.format("超过 {} {}", empty(tierPrice.getMin()), servicePriceVo.getUnitLabel()));
                        }else {
                            stairRateContent.setDescription(StrFormatter.format("从 {} 至 {} {}", empty(tierPrice.getMin()), empty(tierPrice.getMax()), servicePriceVo.getUnitLabel()));
                        }
                        stairRateContent.setDiscountUnitPrice(StrFormatter.format("{}", empty(tierPrice.getDiscountedUnitPrice())));
                        stairRateContent.setDiscountPrice(StrFormatter.format("{}", empty(tierPrice.getDiscountedPrice())));
                        stairRateContent.setUsageCount(StrFormatter.format("{}", empty(tierPrice.getUsage())));
                        stairRateContent.setTaxRate(StrFormatter.format("{}%", empty(tierPrice.getTaxRate()).multiply(BigDecimal.valueOf(100))));
                        stairRateContent.setTotalAmount(StrFormatter.format("{}", empty(tierPrice.getTotalAmount())));
                        stairRateContent.setCurrency(service.getCurrency());
                        serviceContent.getChildren().add(stairRateContent);

                }

            }
        }
    }

    private void handleTierContent(ChargeProductServicePriceVo servicePriceVo
            , BillContent parentContent
            ,ServiceApi service, TierRateConfigFeeDTO tierRateConfig
    ) {
        BillContent serviceContent = new BillContent();
        serviceContent.setDescription(service.getServiceName());
        parentContent.getChildren().add(serviceContent);

        List<TierRateConfigFeeDTO.TierPriceFeeDTO> stairRatePricesOutPackage = tierRateConfig.getTierPrices();
        if (CollectionUtils.isNotEmpty(stairRatePricesOutPackage)) {
            boolean isLast = false;
            TierRateConfigFeeDTO.TierPriceFeeDTO lastStairRatePrice = stairRatePricesOutPackage.getLast();
            for (TierRateConfigFeeDTO.TierPriceFeeDTO stairRatePrice : stairRatePricesOutPackage) {

                isLast = lastStairRatePrice.equals(stairRatePrice);
                BillContent stairRateContent = new BillContent();
                if (isLast) {
                    stairRateContent.setDescription(StrFormatter.format("超过 {} {}", empty(stairRatePrice.getMin()), servicePriceVo.getUnitLabel()));
                }else {
                    stairRateContent.setDescription(StrFormatter.format("从 {} 至 {} {}", empty(stairRatePrice.getMin()), empty(stairRatePrice.getMax()), servicePriceVo.getUnitLabel()));
                }
                stairRateContent.setDiscountUnitPrice(StrFormatter.format("{}", empty(stairRatePrice.getDiscountedUnitPrice())));
                stairRateContent.setDiscountPrice(StrFormatter.format("{}", empty(stairRatePrice.getDiscountedPrice())));
                stairRateContent.setUsageCount(StrFormatter.format("{}", empty(stairRatePrice.getUsage())));
                stairRateContent.setTaxRate(StrFormatter.format("{}%", empty(stairRatePrice.getTaxRate()).multiply(BigDecimal.valueOf(100))));
                stairRateContent.setTotalAmount(StrFormatter.format("{}", empty(stairRatePrice.getTotalAmount())));
                stairRateContent.setCurrency(service.getCurrency());
                serviceContent.getChildren().add(stairRateContent);
            }
        }
    }

    private void handleFixedContent(BillContent productContent, ServiceApi service, ChargeProductServicePriceVo servicePriceVo) {
        String serviceName = service.getServiceName();
        BillContent serviceContent = new BillContent();



        FixedRateConfigFeeDTO fixedRateConfig = service.getFixedRateConfig();
        if(Objects.equals(service.getBillingType(), 3)){
            fixedRateConfig = service.getUsageBasedRateConfig().getFixRatePrices().getFirst();
        }
        String label  = combine(fixedRateConfig.getUsage(), servicePriceVo.getUnitLabel(), servicePriceVo.getPeriod(), servicePriceVo.getUnitPeriod());
        serviceContent.setDescription(StrFormatter.format("{}({})", empty(serviceName),label));
        serviceContent.setDiscountUnitPrice(StrFormatter.format("{}", empty(fixedRateConfig.getDiscountedUnitPrice())));
        serviceContent.setDiscountPrice(StrFormatter.format("{}", empty(fixedRateConfig.getDiscountedPrice())));
        serviceContent.setUsageCount(StrFormatter.format("{}", empty(fixedRateConfig.getUsage())));
        serviceContent.setTaxRate(StrFormatter.format("{}%", empty(fixedRateConfig.getTaxRate()).multiply(BigDecimal.valueOf(100))));
        serviceContent.setTotalAmount(StrFormatter.format("{}", empty(fixedRateConfig.getTotalAmount())));
        serviceContent.setCurrency(service.getCurrency());
        productContent.getChildren().add(serviceContent);
    }

    private void handlePackageContent(BillContent productContent, ServiceApi service, ChargeProductServicePriceVo servicePriceVo) {

        String serviceName = service.getServiceName();
        BillContent serviceContent = new BillContent();
        serviceContent.setDescription(empty(serviceName));
        productContent.getChildren().add(serviceContent);


        BillContent inPackageContent = new BillContent();
        PackageRateConfigFeeDTO allPackageConfig = service.getPackageRateConfig();
        Integer unitPeriod = servicePriceVo.getUnitPeriod();
        Integer period = servicePriceVo.getPeriod();
        PackageRateConfigFeeDTO.PackageConfigFeeDTO packageContent = allPackageConfig.getPackages().getFirst();
        // 套餐内
        inPackageContent.setDescription("套餐内(" + combine(allPackageConfig.getPackageInclude(), allPackageConfig.getPackageIncludeUnit(),period, unitPeriod) + ")");
        inPackageContent.setDiscountUnitPrice(StrFormatter.format("{}", empty(packageContent.getInPackage().getDiscountedUnitPrice())));
        inPackageContent.setDiscountPrice(StrFormatter.format("{}", empty(packageContent.getInPackage().getDiscountedPrice())));
        inPackageContent.setUsageCount(StrFormatter.format("{}", empty(packageContent.getInPackage().getUsage())));
        inPackageContent.setTaxRate(StrFormatter.format("{}%", empty(packageContent.getInPackage().getTaxRate()).multiply(BigDecimal.valueOf(100))));
        inPackageContent.setTotalAmount(StrFormatter.format("{}", empty(packageContent.getInPackage().getTotalAmount())));
        inPackageContent.setCurrency(service.getCurrency());
        serviceContent.getChildren().add(inPackageContent);
        // 套餐外
        PackageRateConfigFeeDTO.OutPackageRateFeeDTO outPackage = packageContent.getOutPackage();
        if (outPackage!= null) {
            String outPackagePriceModel = outPackage.getOutPackagePriceModel();
            if (StringUtils.equalsIgnoreCase(outPackagePriceModel, "fixed")) {
                PackageRateConfigFeeDTO.FixedOutPackageRateFeeDTO fixRatePricesOutPackage = outPackage.getFixRatePricesOutPackage();
                if (fixRatePricesOutPackage != null) {
                    BillContent outPackageFixedContent = new BillContent();
                    outPackageFixedContent.setDescription("套餐外(" + combine(BigDecimal.ONE, allPackageConfig.getPackageIncludeUnit(), unitPeriod, unitPeriod) + ")");
                    outPackageFixedContent.setDiscountUnitPrice(StrFormatter.format("{}", empty(fixRatePricesOutPackage.getDiscountedUnitPrice())));
                    outPackageFixedContent.setDiscountPrice(StrFormatter.format("{}", empty(fixRatePricesOutPackage.getDiscountedPrice())));
                    outPackageFixedContent.setUsageCount(StrFormatter.format("{}", empty(fixRatePricesOutPackage.getUsage())));
                    outPackageFixedContent.setTaxRate(StrFormatter.format("{}%", empty(fixRatePricesOutPackage.getTaxRate()).multiply(BigDecimal.valueOf(100))));
                    outPackageFixedContent.setTotalAmount(StrFormatter.format("{}", empty(fixRatePricesOutPackage.getTotalAmount())));
                    outPackageFixedContent.setCurrency(service.getCurrency());
                    serviceContent.getChildren().add(outPackageFixedContent);
                }
            }else{
                handleTierContent(servicePriceVo, serviceContent, service.getCurrency(), outPackage);
            }
        }


    }

    private void handleTierContent(ChargeProductServicePriceVo servicePriceVo
            , BillContent parentContent
           ,String currencySymbol, PackageRateConfigFeeDTO.OutPackageRateFeeDTO outPackage
    ) {
        Integer unitPeriod = servicePriceVo.getUnitPeriod();
        Integer period = servicePriceVo.getPeriod();
        String unitLabel = servicePriceVo.getUnitLabel();
        BillContent outPackageContent = new BillContent();
        outPackageContent.setDescription("套餐外(" + combine(BigDecimal.ONE, unitLabel, period, unitPeriod) + ")");
        parentContent.getChildren().add(outPackageContent);

        List<PackageRateConfigFeeDTO.StairOutPackageRateFeeDTO> stairRatePricesOutPackage = outPackage.getStairRatePricesOutPackage();
        if (CollectionUtils.isNotEmpty(stairRatePricesOutPackage)) {
            boolean isLast = false;
            PackageRateConfigFeeDTO.StairOutPackageRateFeeDTO lastStairRatePrice = stairRatePricesOutPackage.getLast();
            for (PackageRateConfigFeeDTO.StairOutPackageRateFeeDTO stairRatePrice : stairRatePricesOutPackage) {

                isLast = lastStairRatePrice.equals(stairRatePrice);
                BillContent stairRateContent = new BillContent();
                if (isLast) {
                    stairRateContent.setDescription(StrFormatter.format("超过 {} {}", empty(stairRatePrice.getMin()), servicePriceVo.getUnitLabel()));
                }else {
                    stairRateContent.setDescription(StrFormatter.format("从 {} 至 {} {}", empty(stairRatePrice.getMin()), empty(stairRatePrice.getMax()), servicePriceVo.getUnitLabel()));
                }
                stairRateContent.setDiscountUnitPrice(StrFormatter.format("{}", empty(stairRatePrice.getDiscountedUnitPrice())));
                stairRateContent.setDiscountPrice(StrFormatter.format("{}", empty(stairRatePrice.getDiscountedPrice())));
                stairRateContent.setUsageCount(StrFormatter.format("{}", empty(stairRatePrice.getUsage())));
                stairRateContent.setTaxRate(StrFormatter.format("{}%", empty(stairRatePrice.getTaxRate()).multiply(BigDecimal.valueOf(100))));
                stairRateContent.setTotalAmount(StrFormatter.format("{}", empty(stairRatePrice.getTotalAmount())));
                stairRateContent.setCurrency(currencySymbol);
                outPackageContent.getChildren().add(stairRateContent);
            }
        }
    }

    private String combine(BigDecimal value, String unit, Integer period, Integer unitPeriod) {
        switch (unitPeriod) {
            case 0:
                return StrFormatter.format("{}/{}", empty(value), empty(unit));
            case 1:
                return StrFormatter.format("{}{}/{}日", empty(value), empty(unit),period);
            case 2:
                return StrFormatter.format("{}{}/{}周", empty(value), empty(unit),period);
            case 3:
                return StrFormatter.format("{}{}/{}月", empty(value), empty(unit),period);
            case 4:
                return StrFormatter.format("{}{}/{}季", empty(value), empty(unit),period);
            case 5:
                return StrFormatter.format("{}{}/{}年", empty(value), empty(unit),period);
        }
        return "";
    }


}
