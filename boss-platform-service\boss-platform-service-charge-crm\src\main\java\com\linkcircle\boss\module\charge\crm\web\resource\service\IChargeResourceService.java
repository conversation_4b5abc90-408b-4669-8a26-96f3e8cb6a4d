package com.linkcircle.boss.module.charge.crm.web.resource.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.crm.web.resource.model.ChargeResource;
import com.linkcircle.boss.module.charge.crm.web.resource.model.dto.ChargeResourceAddDTO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.dto.ChargeResourceQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.vo.ChargeResourceVO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.vo.ChargeSupplierDropDownVO;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * 资源信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface IChargeResourceService extends IService<ChargeResource> {

    PageResult<ChargeResourceVO> pageQuery(@Valid ChargeResourceQueryDTO queryDTO);

    List<ChargeSupplierDropDownVO> supplierDropDownList();

    long add(@Valid ChargeResourceAddDTO chargeResourceAddDTO);

    CommonResult<?> edit(@Valid ChargeResourceAddDTO chargeResourceAddDTO);

    CommonResult<?> detail(Long id);

    CommonResult<?> activationBatch(List<Long> resourceIds);

    CommonResult<?> deactivateBatch(List<Long> resourceIds);

    CommonResult<?> archive(List<Long> resourceIds);

    CommonResult<?> cancelArchiveBatch(List<Long> resourceIds);

    CommonResult<?> deleteBatch(List<Long> resourceIds);

    CommonResult<?> copy(Long resourceId);

    CommonResult<List<CommonVO>> findNameByIds(CommonDTO commonDTO);
}
