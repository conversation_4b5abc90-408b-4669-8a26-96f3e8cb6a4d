<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.billing.web.bill.product.mapper.PostpaidProductIncomeBillMapper">
    <select id="existsByProductAndPeriod"
            resultType="com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO">
        SELECT product_bill_id FROM postpaid_product_income_bill
        WHERE product_id = #{productId}
        AND subscribe_id = #{subscriptionId}
        AND billing_start_time = #{startTime}
        AND billing_end_time = #{endTime}
        AND deleted = false
    </select>
</mapper>