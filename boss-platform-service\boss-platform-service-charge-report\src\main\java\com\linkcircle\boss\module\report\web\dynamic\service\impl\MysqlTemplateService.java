package com.linkcircle.boss.module.report.web.dynamic.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.report.web.dynamic.model.dto.FieldMetaDTO;
import com.linkcircle.boss.module.report.web.dynamic.model.dto.TableMetaDTO;
import com.linkcircle.boss.module.report.web.dynamic.service.DbTemplateService;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDynamicColumnDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.type.TypeHandler;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/11 10:19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class MysqlTemplateService extends DbTemplateService {

    private static final String QUERY_TABLES = "select TABLE_NAME tableName, TABLE_COMMENT tableComment from information_schema.TABLES where TABLE_SCHEMA = '%s'";
    private static final String QUERY_COLUMNS = "select COLUMN_NAME,COLUMN_COMMENT, DATA_TYPE,ORDINAL_POSITION from information_schema.COLUMNS where TABLE_SCHEMA = '%s' and TABLE_NAME = '%s'";



    @Override
    public void listTables() {
        long startTime = System.currentTimeMillis();
        log.info("开始加载系统的数据源配置,数据库名:{}", actualName);
        refreshDb();
        if (CollectionUtils.isNotEmpty(TABLES)) {
            log.info("加载系统的数据源配置成功,数据库名:{},表数量:{},表明:{}", actualName, TABLES.size(),TABLES.stream().map(TableMetaDTO::getTableName).collect(Collectors.joining(",")));
            countDownLatchService.executeVirtualTasks(TABLES.size(), 10, TABLES, this::refreshTable);
            countDownLatchService.executeVirtualTasks(TABLES.size(), 10, TABLES, this::checkTableFirst);
            countDownLatchService.executeVirtualTasks(TABLES.size(), 10, TABLES, this::checkTableSecond);
        }
        long endTime = System.currentTimeMillis();
        log.info("加载系统的数据源配置成功,数据库名:{},表数量:{},耗时:{}ms", actualName, TABLES.size(), (endTime - startTime));
    }

    private void refreshDb() {
        TABLES.clear();
        String sql = String.format(QUERY_TABLES, actualName);
        List<TableMetaDTO> tables = jdbcTemplate.query(sql, tableRowMapper);
        if (CollectionUtils.isNotEmpty(tables)) {
            TABLES.addAll(tables);
        }
    }
    public void checkTableFirst(TableMetaDTO table) {
        if(CollectionUtils.isEmpty(table.getColumns())){
            log.error("第一次检查============> 表:{}在数据库:{} 列为空 拉取为空 尝试重新拉取", table.getTableName(), actualName);
            refreshTable(table);
        }
    }

    public void checkTableSecond(TableMetaDTO table) {
        if(CollectionUtils.isEmpty(table.getColumns())){
            log.error("第二次检查============> 表:{}在数据库:{} 列为空 拉取为空 尝试重新拉取", table.getTableName(), actualName);
            refreshTable(table);
        }
    }

    public void refreshTable(TableMetaDTO table) {
        String columnSql = String.format(QUERY_COLUMNS, actualName, table.getTableName());
        List<FieldMetaDTO> columns = jdbcTemplate.query(columnSql, columnRowMapper);
        log.info("刷新系统的数据源配置成功,数据库名:{},表名:{},列明数量:{}", actualName, table.getTableName(),CollectionUtils.size(columns));
        if (CollectionUtils.isNotEmpty(columns)) {
            for (FieldMetaDTO column : columns) {
                column.setTable(table);
            }
            table.setColumns(columns.stream().sorted(Comparator.comparingInt(FieldMetaDTO::getColumnIndex)).toList());
        }else{
            // 重试三次
            refreshTableInternal(table,3);
        }
    }
    public void refreshTableInternal(TableMetaDTO table,int retryTimes) {
        if(retryTimes<=0){
            return;
        }
        String columnSql = String.format(QUERY_COLUMNS, actualName, table.getTableName());
        List<FieldMetaDTO> columns = jdbcTemplate.query(columnSql, columnRowMapper);
        if (CollectionUtils.isNotEmpty(columns)) {
            for (FieldMetaDTO column : columns) {
                column.setTable(table);
            }
            table.setColumns(columns.stream().sorted(Comparator.comparingInt(FieldMetaDTO::getColumnIndex)).toList());
        }else{
            refreshTableInternal(table,retryTimes-1);
        }
    }

    @Override
    public void refreshTableByName(String tableName) {
        Optional<TableMetaDTO> tableOpt = TABLES.stream().filter(tableMetaDTO -> tableMetaDTO.getTableName().equals(tableName)).findFirst();
        if (tableOpt.isPresent()) {
            refreshTable(tableOpt.get());
            log.info("刷新系统的数据源配置成功,数据库名:{},表名:{}", actualName, tableName);
        } else {
            log.warn("刷新系统的数据源配置失败,数据库名:{},表名:{}", actualName, tableName);
            refreshDb();
            tableOpt = TABLES.stream().filter(tableMetaDTO -> tableMetaDTO.getTableName().equals(tableName)).findFirst();
            if (tableOpt.isPresent()) {
                refreshTable(tableOpt.get());
                log.info("刷新系统的数据源配置成功,数据库名:{},表名:{}", actualName, tableName);
            } else {
                log.warn("刷新系统的数据源配置,仍然没有找到表名:{},数据库名:{},表名:{}", tableName, actualName, tableName);
            }
        }

    }

    @Override
    public List<Object> listValue(String tableName, String columnName) {
        String sql = String.format("select distinct %s from `%s`.`%s`", columnName, actualName, tableName);
        Optional<TableMetaDTO> tableMetaDTO = TABLES.stream().filter(t -> t.getTableName().equals(tableName)).findFirst();
        if (tableMetaDTO.isEmpty()) {
            log.warn("表名:{}在数据库:{}中不存在", tableName, actualName);
            return List.of();
        }
        if(CollectionUtils.isEmpty(tableMetaDTO.get().getColumns())){
            refreshTable(tableMetaDTO.get());
        }
        Optional<FieldMetaDTO> fieldMetaDTO = tableMetaDTO.get().getColumns().stream().filter(f -> f.getColumnName().equals(columnName)).findFirst();
        if (fieldMetaDTO.isEmpty()) {
            log.warn("字段名:{}在表:{}中不存在", columnName, tableName);
            return List.of();
        }
        return jdbcTemplate.query(sql, (rs, rowNum) -> fieldMetaDTO.get().getTypeHandler().getResult(rs, 1));
    }

    @Override
    public Map<Object, Object> listValue(String tableName, String columnName, String associationName) {
        String sql = String.format("select %s, %s from `%s`.`%s` group by %s,%s", associationName, columnName, actualName, tableName, associationName, columnName);
        Optional<TableMetaDTO> tableMetaDTO = TABLES.stream().filter(t -> t.getTableName().equals(tableName)).findFirst();
        if (tableMetaDTO.isEmpty()) {
            log.warn("表名:{}在数据库:{}中不存在", tableName, actualName);
            return Map.of();
        }
        if(CollectionUtils.isEmpty(tableMetaDTO.get().getColumns())){
            refreshTable(tableMetaDTO.get());
        }
        Optional<FieldMetaDTO> field = tableMetaDTO.get().getColumns().stream().filter(f -> f.getColumnName().equals(columnName)).findFirst();
        Optional<FieldMetaDTO> association = tableMetaDTO.get().getColumns().stream().filter(f -> f.getColumnName().equals(associationName)).findFirst();
        if (field.isEmpty() || association.isEmpty()) {
            log.warn("字段名:{}/{}在表:{}中不存在", columnName, associationName, tableName);
            return Map.of();
        }
        List<Map<String, Object>> maps = jdbcTemplate.query(sql, (rs, rowNum) -> {
            Object associationValue = association.get().getTypeHandler().getResult(rs, 1);
            Object fieldValue = field.get().getTypeHandler().getResult(rs, 2);
            return Map.of(associationName, associationValue, columnName, fieldValue);
        });
        Map<Object, Object> result = new HashMap<>();
        maps.forEach(map -> result.putIfAbsent(map.get(associationName), map.get(columnName)));
        return result;
    }

    //@Override
    public Map<Object, List<Object>> groupValue(String tableName, String columnName, String groupName) {
        String sql = String.format("select %s, %s from `%s`.`%s` group by %s,%s", groupName, columnName, actualName, tableName, groupName, columnName);
        Optional<TableMetaDTO> tableMetaDTO = TABLES.stream().filter(t -> t.getTableName().equals(tableName)).findFirst();
        if (tableMetaDTO.isEmpty()) {
            log.warn("表名:{}在数据库:{}中不存在", tableName, actualName);
            return Map.of();
        }
        if(CollectionUtils.isEmpty(tableMetaDTO.get().getColumns())){
            refreshTable(tableMetaDTO.get());
        }
        Optional<FieldMetaDTO> field = tableMetaDTO.get().getColumns().stream().filter(f -> f.getColumnName().equals(columnName)).findFirst();
        Optional<FieldMetaDTO> association = tableMetaDTO.get().getColumns().stream().filter(f -> f.getColumnName().equals(groupName)).findFirst();
        if (field.isEmpty() || association.isEmpty()) {
            log.warn("字段名:{}/{}在表:{}中不存在", columnName, groupName, tableName);
            return Map.of();
        }
        List<Map<String, Object>> maps = jdbcTemplate.query(sql, (rs, rowNum) -> {
            Object associationValue = association.get().getTypeHandler().getResult(rs, 1);
            Object fieldValue = field.get().getTypeHandler().getResult(rs, 2);
            return Map.of(groupName, associationValue, columnName, fieldValue);
        });
        Map<Object, List<Object>> result = new HashMap<>();
        for (Map<String, Object> map : maps) {
            Object groupValue = map.get(groupName);
            Object fieldValue = map.get(columnName);
            if (result.containsKey(groupValue)) {
                result.get(groupValue).add(fieldValue);
            } else {
                result.put(groupValue, new ArrayList<>(List.of(fieldValue)));
            }
        }
        return result;
    }


    @Data
    static class ParamHandler{
        private TypeHandler typeHandler;
        private Object value;
        private int index;
    }

    static class PreparedStatementSetterImpl implements PreparedStatementSetter {
        private List<ParamHandler> paramHandlers;

        public PreparedStatementSetterImpl(List<ParamHandler> paramHandlers) {
            this.paramHandlers = paramHandlers;
        }

        @Override
        public void setValues(PreparedStatement ps) throws SQLException {
            for (ParamHandler paramHandler : paramHandlers) {
                try {
                    //paramHandler.getTypeHandler().setParameter(ps, paramHandler.getIndex(), paramHandler.getValue(), null);
                    ps.setObject(paramHandler.getIndex(), paramHandler.getValue());
                } catch (Exception e) {
                    log.error("设置参数失败",e);
                }
            }
        }
    }

    static class RowMapperImpl implements RowMapper<Map<String, Object>> {
        private  List<FieldMetaDTO> resultHandlers;

        public RowMapperImpl(List<FieldMetaDTO> resultHandlers) {
            this.resultHandlers = resultHandlers;
        }


        @Override
        public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
            Map<String, Object> result =  new LinkedHashMap<>();
            for (FieldMetaDTO fieldMetaDTO : resultHandlers) {
                result.put(fieldMetaDTO.getColumnName(),fieldMetaDTO.getTypeHandler().getResult(rs, fieldMetaDTO.getColumnName()));
            }
            return result;
        }
    }

    public<T> String judgeAnd(List<T> list){
        return CollectionUtils.isEmpty(list)? "  ":" and ";
    }

    @Override
    public PageResult<Map<String, Object>> queryPage(String tableName, List<String> queryColumns, List<ChargeDynamicColumnDTO> conditions, Integer pageNo, Integer pageSize) {
        Optional<TableMetaDTO> tableMetaDTO = TABLES.stream().filter(t -> t.getTableName().equals(tableName)).findFirst();
        if (tableMetaDTO.isEmpty()) {
            log.warn("表名:{}在数据库:{}中不存在", tableName, actualName);

        }
        if(CollectionUtils.isEmpty(tableMetaDTO.get().getColumns())){
             refreshTable(tableMetaDTO.get());
        }
        Map<String, FieldMetaDTO> fieldMap = tableMetaDTO.get().getColumns().stream().collect(Collectors.toMap(FieldMetaDTO::getColumnName, t -> t));
        List<FieldMetaDTO> resultHandlers = new ArrayList<>();
        StringBuilder pageSqlBuilder = new StringBuilder("select ");
        StringBuilder countSqlBuilder = new StringBuilder("select count(1) ");
        for (int i = 0; i < queryColumns.size(); i++) {
            pageSqlBuilder.append("`").append(queryColumns.get(i)).append("`").append(",");
            resultHandlers.add(fieldMap.get(queryColumns.get(i)));
        }
        if (pageSqlBuilder.toString().endsWith(",")) {
            pageSqlBuilder.deleteCharAt(pageSqlBuilder.length() - 1);
        }
        pageSqlBuilder.append(" from `").append(actualName).append("`.`").append(tableName).append("`");
        countSqlBuilder.append(" from `").append(actualName).append("`.`").append(tableName).append("`");
        List<ParamHandler> paramHandlers = new ArrayList<>();
        if (conditions != null && !conditions.isEmpty()) {
            pageSqlBuilder.append(" where ");
            countSqlBuilder.append(" where ");
            int index = 1;
            for (int i = 0; i < conditions.size(); i++) {
                ChargeDynamicColumnDTO condition = conditions.get(i);
                if (Objects.equals(condition.getRangeType(),0)) {

                        pageSqlBuilder.append(judgeAnd(paramHandlers)).append("`").append(condition.getColumnName()).append("`  =  ? ");
                        countSqlBuilder.append(judgeAnd(paramHandlers)).append("`").append(condition.getColumnName()).append("`  =  ? ");
                        ParamHandler paramHandler = new ParamHandler();
                        paramHandler.setTypeHandler(fieldMap.get(condition.getColumnName()).getTypeHandler());
                        paramHandler.setValue( condition.getValue());
                        paramHandler.setIndex(index++);
                        paramHandlers.add(paramHandler);

                } else {
                    if (condition.getMinValue()!=null) {
                        pageSqlBuilder.append(judgeAnd(paramHandlers)).append("  `").append(condition.getColumnName()).append("`  >=  ? ");
                        countSqlBuilder.append(judgeAnd(paramHandlers)).append("  `").append(condition.getColumnName()).append("`  >=  ? ");
                        ParamHandler paramHandler = new ParamHandler();
                        paramHandler.setTypeHandler(fieldMap.get(condition.getColumnName()).getTypeHandler());
                        paramHandler.setValue(condition.getMinValue());
                        paramHandler.setIndex(index++);
                        paramHandlers.add(paramHandler);
                    }

                    if (condition.getMaxValue()!=null) {
                        pageSqlBuilder.append(judgeAnd(paramHandlers)).append("  `").append(condition.getColumnName()).append("`  <=  ? ");
                        countSqlBuilder.append(judgeAnd(paramHandlers)).append("  `").append(condition.getColumnName()).append("`  <=  ? ");
                        ParamHandler paramHandler = new ParamHandler();
                        paramHandler.setTypeHandler(fieldMap.get(condition.getColumnName()).getTypeHandler());
                        paramHandler.setValue(condition.getMaxValue());
                        paramHandler.setIndex(index++);
                        paramHandlers.add(paramHandler);
                    }
                }

            }
            if(paramHandlers.isEmpty()) {
                //where
                pageSqlBuilder.delete(pageSqlBuilder.length() - 6, pageSqlBuilder.length());
                countSqlBuilder.delete(countSqlBuilder.length() - 6, countSqlBuilder.length());
            }
        }
        String countSql = countSqlBuilder.toString();
        log.info("count查询sql:{}",countSql);
        List<Long> totalCounts = jdbcTemplate.query(countSql, new PreparedStatementSetterImpl(paramHandlers), (rs, rowNum) -> rs.getLong(1));
        Long totalCount = totalCounts.get(0);
        log.info("总记录数:{}",totalCount);
        if (totalCount == 0) {
            return new PageResult<>(0L);
        }
        String sql = pageSqlBuilder.append(" limit  ").append(pageSize).append(" offset ").append((pageNo - 1) * pageSize).toString();
        log.info("查询sql:{}",sql);
        List<Map<String, Object>> results = null;
        if(!paramHandlers.isEmpty()) {
            results =jdbcTemplate.query(sql, new PreparedStatementSetterImpl(paramHandlers), new RowMapperImpl(resultHandlers));
        }else{
            results = jdbcTemplate.query(sql, new RowMapperImpl(resultHandlers));
        }
        return new PageResult<>(results, totalCount);
    }



}
