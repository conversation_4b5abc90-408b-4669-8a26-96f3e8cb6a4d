package com.linkcircle.boss.module.billing.web.bill.product.scheduled.product.service;

import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-08 11:45
 * @description 后付费产品账单数据服务接口
 */
public interface IncomePostpaidProductBillService {

    /**
     * 保存产品账单
     */
    boolean saveProductBill(PostpaidProductIncomeBillDO productBill);

    /**
     * 是否已经出账过
     */
    boolean alreadyProductBill(Long productId, Long subscriptionId, Long startTime, Long endTime);

    /**
     * 保存出账标志redis
     */
    boolean saveProductBillFlag(Long productId, Long subscriptionId, Long startTime, Long endTime);

    /**
     * 检查产品账单是否存在
     */
    boolean existsByProductAndPeriod(Long productId, Long subscriptionId, Long startTime, Long endTime);

    /**
     * 根据服务ID列表查询服务账单
     */
    List<PostpaidProductServiceIncomeBillDO> getServiceBillsByIds(List<Long> serviceIds,
                                                                  Long subscriptionId,
                                                                  Long productId,
                                                                  Long startTime,
                                                                  Long endTime);

    /**
     * 批量更新服务账单的产品账单ID
     */
    boolean updateServiceBillProductId(List<PostpaidProductServiceIncomeBillDO> serviceBills, Long productBillId);
}