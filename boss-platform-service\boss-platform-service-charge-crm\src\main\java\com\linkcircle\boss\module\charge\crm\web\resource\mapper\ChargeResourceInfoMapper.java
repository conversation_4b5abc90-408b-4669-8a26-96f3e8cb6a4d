package com.linkcircle.boss.module.charge.crm.web.resource.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.module.charge.crm.web.resource.model.ChargeResource;
import com.linkcircle.boss.module.charge.crm.web.resource.model.dto.ChargeResourceQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.vo.ChargeResourceVO;
import com.linkcircle.boss.module.charge.crm.web.resource.model.vo.ChargeSupplierDropDownVO;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 资源信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Mapper
public interface ChargeResourceInfoMapper extends BaseMapper<ChargeResource> {

    List<ChargeResourceVO> pageQuery(Page<?> page, @Param("query") ChargeResourceQueryDTO queryDTO);

    List<ChargeSupplierDropDownVO> supplierDropDownList();

    default List<ChargeResource> queryNameByIds(CommonDTO commonDTO){
        LambdaQueryWrapper<ChargeResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ChargeResource::getId, ChargeResource::getResourceName);
        wrapper.in(ChargeResource::getId, commonDTO.getIds());
        return this.selectList(wrapper);
    };
}
