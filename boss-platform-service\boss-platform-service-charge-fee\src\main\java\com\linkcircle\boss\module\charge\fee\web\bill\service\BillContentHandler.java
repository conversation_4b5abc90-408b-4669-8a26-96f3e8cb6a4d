package com.linkcircle.boss.module.charge.fee.web.bill.service;

import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeProductServiceBillDetailVo;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid.PostpaidIncomeServiceVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.prepaid.PrepaidIncomeBillDetailVO;


import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28 10:06
 */
public interface BillContentHandler {
    List<BillCoupon> handleCoupons(List<BillDiscountConfigDTO> billCoupons, Long productId, Long serviceId, String currencySymbol);

    void handleContent(BillContent productContent, MakeupIncomeProductServiceBillDetailVo service);

    void handleContent(BillContent productContent, PostpaidIncomeServiceVO service);

    void handleContent(BillContent productContent, PrepaidIncomeBillDetailVO service);

    default String empty(String str) {
        if (str != null) {
            return str;
        }
        return "";
    }

    default BigDecimal empty(BigDecimal val) {
        if (val != null) {
            return val;
        }
        return BigDecimal.ZERO;
    }
}
