package com.linkcircle.boss.module.charge.fee.web.payment.controller;

import com.alibaba.fastjson2.JSONObject;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoMakeupUnPayDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoPostpaidUnPayDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoPrepaidUnPayDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.service.*;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletBalanceEditDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletBalanceToPayDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletTransactionsPageQueryDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletTransactionsQueryDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO;
import com.linkcircle.boss.module.fee.api.wallets.model.vo.WalletsTransactionsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Calendar;
import java.util.Map;

/**
 * 钱包支付接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 * <p>
 * 钱包
 * charge_customer_account_wallets
 * 钱包操作记录
 * charge_customer_account_wallets_transactions
 * 预付费
 * prepaid_income_bill_detail
 * 后付费
 * postpaid_product_income_bill_2025
 * postpaid_product_service_income_bill_2025
 */
@RestController
@RequestMapping("/wallet-balance")
@Slf4j
@Tag(name = "钱包余额接口")
@Validated
public class WalletBalanceController {

    @Autowired
    private IWalletPaymentService walletPaymentService;
    @Autowired
    private IUnpayBillSettleService unpayBillSettleService;
    @Autowired
    private IChargeCustomerAccountWalletsService chargeCustomerAccountWalletsService;
    @Autowired
    private IChargeCustomerAccountWalletsTransactionsService walletsTransactionsService;
    @Autowired
    private IUnifiedRechargeOrderRqService unifiedOrderService;

    @PostMapping("/trans/page")
    @Operation(summary = "钱包流水分页查询")
    public CommonResult<PageResult<WalletsTransactionsVO>> page(@Valid @RequestBody WalletTransactionsPageQueryDTO params) {
        return CommonResult.success(walletsTransactionsService.queryByPage(params));
    }

    @PostMapping("/edit")
    @Operation(summary = "钱包支付与充值")
    public CommonResult<Long> walletBalanceEdit(@Valid @RequestBody WalletBalanceEditDTO walletBalanceEditDTO) throws Exception {
        boolean rs = walletPaymentService.walletBalanceEdit(walletBalanceEditDTO);
        return rs ? CommonResult.success() : CommonResult.error(400, "钱包充值或者扣款失败");
    }

    @GetMapping("/info/{id}")
    @Operation(summary = "查询钱包详情")
    public CommonResult<ChargeCustomerAccountWalletsDO> getWalletsById(@PathVariable("id") Long id) {
        ChargeCustomerAccountWalletsDO walletsDO = chargeCustomerAccountWalletsService.getById(id);
        return CommonResult.success(walletsDO);
    }

    @PostMapping(value = "/export")
    @Operation(summary = "导出钱包流水")
    public void export(HttpServletResponse response, @Valid @RequestBody WalletTransactionsQueryDTO queryDTO) throws IOException {
        walletsTransactionsService.export(response, queryDTO);
    }

    @PostMapping("/buss-pay")
    @Operation(summary = "业务方发起手动钱包支付")
    public CommonResult<Long> walletBalanceToPay(@Valid @RequestBody WalletBalanceToPayDTO walletBalanceToPayDTO) throws Exception {
        return walletPaymentService.walletBalanceToPay(walletBalanceToPayDTO);
    }

    @PostMapping("/prepaid/un-pay")
    @Operation(summary = "平台手动发起结清预付费账单")
    public CommonResult<Map<Long, Boolean>> doUnPayPrepaidBill(@Valid @RequestBody WalletBalanceDoPrepaidUnPayDTO walletBalanceDoPrepaidUnPayDTO) throws Exception {
        return unpayBillSettleService.doUnPayPrepaidBill(walletBalanceDoPrepaidUnPayDTO);
    }

    @PostMapping("/postpaid/un-pay")
    @Operation(summary = "平台手动发起结清后付费账单")
    public CommonResult<Map<Long, Boolean>> doUnPayPostpaidBill(@Valid @RequestBody WalletBalanceDoPostpaidUnPayDTO walletBalanceDoPostpaidUnPayDTO) throws Exception {
        return unpayBillSettleService.doUnPayPostpaidBill(walletBalanceDoPostpaidUnPayDTO);
    }

    @PostMapping("/makeup/un-pay")
    @Operation(summary = "平台手动发起结清手工账单")
    public CommonResult<Map<Long, Boolean>> doUnPayMakeupBill(@Valid @RequestBody WalletBalanceDoMakeupUnPayDTO walletBalanceDoMakeupUnPayDTO) throws Exception {
        return unpayBillSettleService.doUnPayMakeupBill(walletBalanceDoMakeupUnPayDTO);
    }

    @GetMapping("/unified/recharge")
    @Operation(summary = "第三方统一充值")
    @Parameters({
            @Parameter(name = "customerId", description = "客户ID", example = "1", required = true),
            @Parameter(name = "amount", description = "充值金额(整数)", example = "1", required = true),
            @Parameter(name = "clientIp", description = "支付客户端IP", example = "127.0.0.1", required = false),
            @Parameter(name = "walletId", description = "钱包ID", example = "1", required = true),
            @Parameter(name = "reqTimeZone", description = "时区(例如东八区:8)", example = "8", required = true)
    })
    public CommonResult<JSONObject> unifiedPay(@RequestParam("customerId") Long customerId,
                                               @RequestParam("amount") Long amount,
                                               @RequestParam(value = "clientIp", required = false) String clientIp,
                                               @RequestParam("walletId") Long walletId,
                                               @RequestParam("reqTimeZone") String reqTimeZone) throws Exception {
        return unifiedOrderService.unifiedOrder(customerId, amount, clientIp, walletId, reqTimeZone);
    }

    @PostMapping("/unified/recharge/callback")
    @Operation(summary = "统一充值回调")
    public CommonResult<JSONObject> unifiedPayCallback(@RequestBody JSONObject jsonObject) throws Exception {
        return CommonResult.success();
    }

    //    @GetMapping("/test")
//    @Operation(summary = "测试")
    public CommonResult<PageResult<WalletsTransactionsVO>> test() throws Exception {
        /*ChargeCustomerAccountWalletsTransactionsDO transactionsPointDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                .accountId(1L)
                .walletsId(1L)
                .walletsEven(WalletsEventEnum.DEDUCTION.getCode())
                .customerId(1L)
                .pointsAmount(BigDecimal.ONE)
                .balancePoints(BigDecimal.TEN)
                .cashAmount(BigDecimal.ZERO) // 积分扣款不涉及现金
                .balanceCash(BigDecimal.TEN) // 现金余额不变
                .transactionTime(Calendar.getInstance().getTimeInMillis())
                .tenantId(1L)
                .build();
        transactionsPointDO.setCreateTime(Calendar.getInstance().getTimeInMillis());
        walletsTransactionsService.recordHistory(transactionsPointDO);*/
//        walletPaymentService.walletBalanceProxyPay(1944952425220771842L, BigDecimal.TWO, 1, 999L, 888L, Calendar.getInstance().getTimeInMillis());
//        walletPaymentService.transferFrom(0L, BigDecimal.TWO, 0, 888L, 1944952425220771842L, Calendar.getInstance().getTimeInMillis());
//        walletPaymentService.transferTo(1944952425220771842L, BigDecimal.TWO, 0, 999L, 0L, Calendar.getInstance().getTimeInMillis());

        // 测试划转
//        walletPaymentService.transferChargeOperation(0L, 1944952425220771842L, BigDecimal.TWO, 1, 999L, Calendar.getInstance().getTimeInMillis());
//        walletPaymentService.transferChargeOperation(1944952425220771842L, 0L, BigDecimal.TWO, 1, 999L, Calendar.getInstance().getTimeInMillis());
        Calendar instance = Calendar.getInstance();
        WalletTransactionsPageQueryDTO walletTransactionsPageQueryDTO = new WalletTransactionsPageQueryDTO();
        walletTransactionsPageQueryDTO.setWalletsEven(3);
        walletTransactionsPageQueryDTO.setEndTransactionTime(instance.getTimeInMillis());
        instance.add(Calendar.DAY_OF_MONTH, -1);
        walletTransactionsPageQueryDTO.setStartTransactionTime(instance.getTimeInMillis());
        walletTransactionsPageQueryDTO.setPageNo(1);
        walletTransactionsPageQueryDTO.setPageSize(10);
        PageResult<WalletsTransactionsVO> walletsTransactionsVOPageResult = walletsTransactionsService.queryByPage(walletTransactionsPageQueryDTO);
        return CommonResult.success(walletsTransactionsVOPageResult);
    }

}
