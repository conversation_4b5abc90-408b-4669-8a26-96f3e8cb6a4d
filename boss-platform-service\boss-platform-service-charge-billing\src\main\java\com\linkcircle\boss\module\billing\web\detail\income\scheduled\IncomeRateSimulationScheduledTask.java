package com.linkcircle.boss.module.billing.web.detail.income.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CustomerAccountDataService;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.IncomeBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ServiceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.income.model.vo.IncomeBillDetailResponseVO;
import com.linkcircle.boss.module.billing.web.detail.income.service.IncomeBillDetailService;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.IncomeRateUsageManageService;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-10 10:47
 * @description 收入固定费率, 阶梯费率 根据间隔时长周期 定时模拟发送详单到mq
 * 1. 查询有固定费率, 阶梯费率的订阅 @SubscriptionDataService#getSubscriptionsListByBillingType
 * 1.1 要判断订阅是否生效..
 * 2. 计算间隔时长 晚1小时 是否到期 @CyclePeriodCalculateService#calculateCyclePeriod
 * 3. 发送详单消息到mq @IncomeBillDetailService#createIncomeBillDetail
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IncomeRateSimulationScheduledTask {

    private final SubscriptionDataService subscriptionDataService;
    private final CustomerAccountDataService customerAccountDataService;
    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final IncomeBillDetailService incomeBillDetailService;
    private final IncomeRateUsageManageService incomeRateUsageManageService;
    private final RedissonUtil redissonUtil;

    /**
     * 固定费率模拟详单发送定时任务
     * 每小时执行一次
     */
    @XxlJob("incomeFixedRateSimulationHandler")
    @XxlJobRegister(
            cron = "0 20 * * * ?",
            jobDesc = "收入-固定费率-模拟账单发送",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.LEAST_FREQUENTLY_USED,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void incomeFixedRateSimulationHandler() {
        executeRateSimulation(ChargeRateTypeEnum.FIXED);
    }

    /**
     * 阶梯费率模拟详单发送定时任务
     * 每小时执行一次
     */
    @XxlJob("incomeTieredRateSimulationHandler")
    @XxlJobRegister(
            cron = "0 30 * * * ?",
            jobDesc = "收入-阶梯费率-模拟账单发送",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.LEAST_FREQUENTLY_USED,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void incomeTieredRateSimulationHandler() {
        executeRateSimulation(ChargeRateTypeEnum.TIERED);
    }

    /**
     * 执行费率模拟详单发送
     *
     * @param rateType 费率类型
     */
    private void executeRateSimulation(ChargeRateTypeEnum rateType) {
        String rateTypeName = rateType.getName();
        String traceId = IdUtil.fastSimpleUUID();
        TraceIdUtil.buildAndSetTraceId(" ", rateTypeName, traceId);

        try {
            log.info("开始执行{}模拟详单发送定时任务", rateTypeName);

            // 1. 查询有指定费率类型的订阅
            List<AccountSubscriptionsVO> subscriptions = subscriptionDataService.getSubscriptionsListByBillingType(rateType.getType());
            if (subscriptions.isEmpty()) {
                log.info("未找到{}类型的订阅，跳过执行", rateTypeName);
                return;
            }

            log.info("找到{}个{}类型的订阅", subscriptions.size(), rateTypeName);

            int successCount = 0;
            int failureCount = 0;

            for (AccountSubscriptionsVO subscription : subscriptions) {
                Long subscriptionId = subscription.getId();
                try {
                    // 1. 判断订阅是否生效
                    if (!isSubscriptionActive(subscription)) {
                        log.info("订阅: {}, 未生效，跳过处理", subscriptionId);
                        continue;
                    }

                    // 2. 根据rateType过滤出匹配的订阅服务
                    List<ServiceSubscriptionInfoDTO> matchedServices = filterServicesByRateType(subscription, rateType);
                    if (matchedServices.isEmpty()) {
                        log.info("订阅: {}, 未找到匹配的服务", subscriptionId);
                        continue;
                    }

                    Optional<CustomerAccountVO> accountInfoOpt = customerAccountDataService.getCustomerAccountInfo(subscription.getAccountId());
                    if (accountInfoOpt.isEmpty()) {
                        log.info("订阅: {}, 未找到账户信息，跳过处理", subscriptionId);
                        continue;
                    }
                    CustomerAccountVO customerAccountVO = accountInfoOpt.get();

                    // 3. 遍历匹配的服务，进行间隔时长判断和详单发送
                    for (ServiceSubscriptionInfoDTO serviceInfo : matchedServices) {
                        try {
                            boolean success = processServiceBillDetail(subscription, serviceInfo, customerAccountVO, rateType);
                            if (success) {
                                successCount++;
                            }
                        } catch (Exception e) {
                            failureCount++;
                            AccountSubscriptionsVO.Service service = serviceInfo.getServiceConfig();
                            log.error("处理订阅: {}, 服务: {},时发生异常", subscriptionId, service.getServiceId(), e);
                        }
                    }
                } catch (Exception e) {
                    failureCount++;
                    log.error("处理订阅: {}, 时发生异常", subscriptionId, e);
                }
            }

            log.info("模拟详单发送定时任务执行完成，成功: {}, 失败: {}", successCount, failureCount);
        } catch (Exception e) {
            log.error("模拟详单发送定时任务执行异常", e);
        } finally {
            TraceIdUtil.remove();
        }
    }

    /**
     * 检测是否已计费过
     */
    private boolean checkBilled(CyclePeriodResultVO cyclePeriodResultVO) {
        ChargeRateTypeEnum rateType = cyclePeriodResultVO.getRateTypeEnum();
        String billingCycle = cyclePeriodResultVO.getBillingCycle();
        Long subscriptionId = cyclePeriodResultVO.getSubscriptionId();
        Long serviceId = cyclePeriodResultVO.getServiceId();

        if (ChargeRateTypeEnum.FIXED.equals(rateType)) {
            return incomeRateUsageManageService.isFixedRateBilled(subscriptionId, serviceId, billingCycle);
        }
        if (ChargeRateTypeEnum.TIERED.equals(rateType)) {
            return incomeRateUsageManageService.isTieredRateBilled(subscriptionId, serviceId, billingCycle);
        }
        return false;
    }

    /**
     * 根据费率类型过滤出匹配的订阅服务
     * 需要遍历 details -> products -> services 的层级结构
     *
     * @param subscription 订阅信息
     * @param rateType     费率类型
     * @return 匹配的服务列表，包含详情和产品信息
     */
    private List<ServiceSubscriptionInfoDTO> filterServicesByRateType(AccountSubscriptionsVO subscription, ChargeRateTypeEnum rateType) {
        List<ServiceSubscriptionInfoDTO> matchedServices = new ArrayList<>();

        // 1. 检查 details
        List<AccountSubscriptionsVO.Detail> details = subscription.getDetails();
        if (CollUtil.isEmpty(details)) {
            return matchedServices;
        }

        // 2. 遍历每个 detail
        for (AccountSubscriptionsVO.Detail detail : details) {
            // 2.1 检查 detail 是否在有效时间范围内
            if (!isDetailActive(detail)) {
                continue;
            }

            // 2.2 检查 products
            List<AccountSubscriptionsVO.Product> products = detail.getProducts();
            if (CollUtil.isEmpty(products)) {
                continue;
            }

            // 2.3 遍历每个 product
            for (AccountSubscriptionsVO.Product product : products) {
                List<AccountSubscriptionsVO.Service> services = product.getServices();
                if (CollUtil.isEmpty(services)) {
                    continue;
                }

                // 2.4 过滤匹配的服务
                for (AccountSubscriptionsVO.Service service : services) {
                    if (Objects.equals(service.getChargeType(), rateType.getType())) {
                        ServiceSubscriptionInfoDTO serviceInfo = ServiceSubscriptionInfoDTO.builder()
                                .subscriptionsVO(subscription)
                                .detail(detail)
                                .product(product)
                                .serviceConfig(service)
                                .productId(product.getProductId())
                                .build();
                        matchedServices.add(serviceInfo);
                    }
                }
            }
        }

        return matchedServices;
    }

    /**
     * 检查详情是否在有效时间范围内
     */
    private boolean isDetailActive(AccountSubscriptionsVO.Detail detail) {
        if (detail == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        Long startTime = detail.getStartTime();
        Long endTime = detail.getEndTime();
        if (Objects.isNull(endTime) || Objects.isNull(startTime)) {
            return false;
        }

        return currentTime > startTime && currentTime <= endTime;
    }

    /**
     * 判断订阅是否生效
     * 使用 AccountSubscriptionsVO 自带的 hasValidSubscription 方法
     *
     * @param subscription 订阅信息
     * @return true-生效，false-未生效
     */
    private boolean isSubscriptionActive(AccountSubscriptionsVO subscription) {
        return subscription.hasValidSubscription();
    }

    /**
     * 判断服务是否应该发送详单（根据间隔时长周期的开始时间，晚1小时）
     *
     * @param serviceInfo 服务订阅信息
     * @return true-应该发送，false-不应该发送
     */
    private Optional<CyclePeriodResultVO> shouldSendBillDetailForService(ServiceSubscriptionInfoDTO serviceInfo,
                                                                         CustomerAccountVO customerAccountVO) {
        try {
            AccountSubscriptionsVO.Service service = serviceInfo.getServiceConfig();
            AccountSubscriptionsVO.Detail detail = serviceInfo.getDetail();

            // 获取服务的间隔时长配置
            Integer period = service.getPeriod();
            Integer unitPeriod = service.getUnitPeriod();
            Long startTime = detail.getStartTime();
            String timezone = customerAccountVO.getTimezone();

            long businessTime = System.currentTimeMillis();

            // 计算周期
            CyclePeriodResultVO cyclePeriodResultVO = cyclePeriodCalculateService.calculateCyclePeriod(
                    unitPeriod, timezone, businessTime, startTime, period);
            if (Objects.isNull(cyclePeriodResultVO) || !cyclePeriodResultVO.isSuccess()) {
                return Optional.empty();
            }
            cyclePeriodResultVO.setSubscriptionId(serviceInfo.getSubscriptionsVO().getId());
            cyclePeriodResultVO.setServiceId(service.getServiceId());
            cyclePeriodResultVO.setCurrency(customerAccountVO.getCurrency());
            return Optional.of(cyclePeriodResultVO);
        } catch (Exception e) {
            log.error("计算服务{}间隔时长时发生异常", serviceInfo.getServiceConfig().getServiceId(), e);
        }
        return Optional.empty();
    }

    /**
     * 处理单个服务的详单生成
     *
     * @param subscription      订阅信息
     * @param serviceInfo       服务信息
     * @param customerAccountVO 客户账户信息
     * @param rateType          费率类型
     * @return true-处理成功，false-处理失败或跳过
     */
    private boolean processServiceBillDetail(AccountSubscriptionsVO subscription,
                                             ServiceSubscriptionInfoDTO serviceInfo,
                                             CustomerAccountVO customerAccountVO,
                                             ChargeRateTypeEnum rateType) {
        AccountSubscriptionsVO.Service service = serviceInfo.getServiceConfig();
        Long subscriptionId = subscription.getId();
        Long serviceId = service.getServiceId();

        // 1. 在服务级别进行间隔时长判断
        Optional<CyclePeriodResultVO> cyclePeriodResultOpt = shouldSendBillDetailForService(serviceInfo, customerAccountVO);
        if (cyclePeriodResultOpt.isEmpty()) {
            log.info("订阅: {}, 服务: {}, 未到周期计算失败，跳过处理", subscriptionId, serviceId);
            return false;
        }

        CyclePeriodResultVO cyclePeriodResultVO = cyclePeriodResultOpt.get();
        cyclePeriodResultVO.setRateTypeEnum(rateType);

        // 2. 检查是否已计费
        boolean billed = checkBilled(cyclePeriodResultVO);
        if (billed) {
            log.info("订阅: {}, 服务: {}, 已生成详单，跳过处理", subscriptionId, serviceId);
            return false;
        }

        // 3. 构造发送MQ的对象
        IncomeBillDetailRequestDTO requestDTO = buildSimulationRequestForService(subscription, serviceInfo, cyclePeriodResultVO);
        if (requestDTO == null) {
            log.info("订阅: {}, 服务: {}, 构造详单请求失败，跳过处理", subscriptionId, serviceId);
            return false;
        }

        // 4. 调用接口发送详单
        IncomeBillDetailResponseVO responseVO = incomeBillDetailService.createIncomeBillDetail(requestDTO);
        log.info("订阅: {}, 服务: {},模拟详单发送成功 response: {}",
                subscriptionId, serviceId, JsonUtils.toJsonString(responseVO));
        return true;
    }

    /**
     * 构造服务级别的模拟详单请求
     *
     * @param subscription        订阅信息
     * @param serviceInfo         服务订阅信息
     * @param cyclePeriodResultVO 周期计算
     * @return 详单请求DTO
     */
    private IncomeBillDetailRequestDTO buildSimulationRequestForService(AccountSubscriptionsVO subscription,
                                                                        ServiceSubscriptionInfoDTO serviceInfo,
                                                                        CyclePeriodResultVO cyclePeriodResultVO) {
        IncomeBillDetailRequestDTO requestDTO = new IncomeBillDetailRequestDTO();

        // 获取服务和产品信息
        AccountSubscriptionsVO.Service service = serviceInfo.getServiceConfig();
        AccountSubscriptionsVO.Product product = serviceInfo.getProduct();

        // 基础信息
        requestDTO.setAccountId(subscription.getAccountId());
        requestDTO.setBusinessId(IdUtil.fastSimpleUUID());
        requestDTO.setRequestId(IdUtil.fastSimpleUUID());
        requestDTO.setBusinessTime(System.currentTimeMillis());
        requestDTO.setTenantId(subscription.getTenantId());
        requestDTO.setTimestamp(System.currentTimeMillis());
        requestDTO.setServiceId(service.getServiceId());
        requestDTO.setProductId(product.getProductId());

        Long purchaseCount = service.getPurchaseCount();
        String unitLabel = service.getUnitLabel();

        if (purchaseCount == null || purchaseCount <= 0) {
            log.info("服务: {}, 未配置purchaseCount或配置值无效，跳过处理", service.getServiceId());
            return null;
        }

        requestDTO.setUsage(BigDecimal.valueOf(purchaseCount));
        requestDTO.setUsageUnit(unitLabel);
        return requestDTO;
    }
}
