# 出账周期计算服务多周期回溯功能使用指南

## 功能概述

`BillingCycleCalculateService` 现已支持多周期回溯功能，可以计算任意数量的历史周期出账信息。同时优化了月结出账的日期处理，支持1-31号的出账日配置。

## 新增方法

### 主要接口方法
```java
/**
 * 计算账户出账周期信息（支持多周期回溯）
 * @param accountInfo        账户信息
 * @param previousCycleCount 回溯周期数量，0-当前周期，1-上一周期，2-上两个周期，以此类推
 * @return 出账周期计算结果，包含是否允许出账、出账时间范围等信息
 */
BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo, int previousCycleCount);
```

### 已废弃方法（保持兼容）
```java
/**
 * @deprecated 使用 calculateBillingCycle(CustomerAccountVO, int) 替代
 */
@Deprecated
BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo, boolean calculatePreviousCycle);
```

## 使用示例

### 基础用法

```java
@Resource
private BillingCycleCalculateService billingCycleCalculateService;

// 获取当前周期出账信息（现有用法）
BillingCycleResultVO currentCycle = billingCycleCalculateService.calculateBillingCycle(accountInfo);

// 等价于新方法
BillingCycleResultVO currentCycle2 = billingCycleCalculateService.calculateBillingCycle(accountInfo, 0);

// 获取上一周期出账信息
BillingCycleResultVO previousCycle = billingCycleCalculateService.calculateBillingCycle(accountInfo, 1);

// 获取上两个周期出账信息
BillingCycleResultVO twoMonthsAgo = billingCycleCalculateService.calculateBillingCycle(accountInfo, 2);

// 获取上三个周期出账信息
BillingCycleResultVO threeMonthsAgo = billingCycleCalculateService.calculateBillingCycle(accountInfo, 3);
```

### 月末日期智能处理

```java
// 配置31号出账的账户
CustomerAccountVO monthEndAccount = new CustomerAccountVO();
monthEndAccount.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
monthEndAccount.setBillingDay(31); // 配置31号出账

// 在2月份（只有28/29天）时，会自动使用月末最后一天作为出账日
BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(monthEndAccount, 0);
// 2月28日（非闰年）或2月29日（闰年）会被识别为出账日
```

### 实际业务场景

```java
/**
 * 补账场景：需要获取指定历史周期进行补账
 */
public void processSupplementaryBilling(Long accountId, int cycleCount) {
    // 获取账户信息
    CustomerAccountVO accountInfo = getAccountInfo(accountId);

    // 计算指定历史周期出账信息
    BillingCycleResultVO targetCycle = billingCycleCalculateService.calculateBillingCycle(
        accountInfo, cycleCount);

    if (targetCycle.getAllowBilling()) {
        // 使用目标周期信息进行补账
        processSupplementaryBilling(accountId, targetCycle);
        log.info("补账处理完成, accountId: {}, 补账周期: {}, 回溯数: {}",
                accountId, targetCycle.getBillingCycle(), cycleCount);
    } else {
        log.warn("无法进行补账: {}", targetCycle.getDescription());
    }
}

/**
 * 批量历史数据分析：获取最近N个周期的出账信息
 */
public List<BillingCycleResultVO> getHistoricalBillingCycles(Long accountId, int cycleCount) {
    CustomerAccountVO accountInfo = getAccountInfo(accountId);
    List<BillingCycleResultVO> historicalCycles = new ArrayList<>();

    // 获取最近N个周期的出账信息
    for (int i = 0; i < cycleCount; i++) {
        BillingCycleResultVO cycle = billingCycleCalculateService.calculateBillingCycle(
            accountInfo, i);
        if (cycle.getAllowBilling()) {
            historicalCycles.add(cycle);
        }
    }

    return historicalCycles;
}

/**
 * 对账场景：比较多个历史周期的出账信息
 */
public void reconcileMultipleBillingCycles(Long accountId) {
    CustomerAccountVO accountInfo = getAccountInfo(accountId);

    // 获取当前周期、上一周期、上两个周期
    BillingCycleResultVO currentCycle = billingCycleCalculateService.calculateBillingCycle(accountInfo, 0);
    BillingCycleResultVO previousCycle = billingCycleCalculateService.calculateBillingCycle(accountInfo, 1);
    BillingCycleResultVO twoMonthsAgo = billingCycleCalculateService.calculateBillingCycle(accountInfo, 2);

    // 进行多周期对账处理
    if (currentCycle.getAllowBilling() && previousCycle.getAllowBilling() && twoMonthsAgo.getAllowBilling()) {
        reconcileMultipleData(Arrays.asList(currentCycle, previousCycle, twoMonthsAgo));
    }
}
```

## 不同周期类型的多周期回溯计算逻辑

### 1. 月结出账 (MONTHLY) - 支持1-31号配置

```java
// 配置：每月15号出账（支持1-31号）
// previousCycleCount = 0：当前周期，出账上个月的账单
// previousCycleCount = 1：上一周期，出账上上个月的账单
// previousCycleCount = 2：上两个周期，出账上三个月的账单

CustomerAccountVO monthlyAccount = new CustomerAccountVO();
monthlyAccount.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
monthlyAccount.setBillingDay(15); // 每月15号出账

// 获取上一周期
BillingCycleResultVO previousMonth = billingCycleCalculateService.calculateBillingCycle(
    monthlyAccount, 1);

// 月末智能处理：配置31号在2月份自动使用28/29号
monthlyAccount.setBillingDay(31); // 配置31号出账
BillingCycleResultVO monthEndResult = billingCycleCalculateService.calculateBillingCycle(
    monthlyAccount, 0);
```

### 2. 周结出账 (WEEKLY)

```java
// 配置：每周一出账（1=周一，7=周日）
// previousCycleCount = 0：当前周期，出账上周的账单
// previousCycleCount = 1：上一周期，出账上上周的账单
// previousCycleCount = 2：上两个周期，出账上三周的账单

CustomerAccountVO weeklyAccount = new CustomerAccountVO();
weeklyAccount.setBillingCycle(BillingCycleTypeEnum.WEEKLY.getType());
weeklyAccount.setBillingDay(1); // 周一出账

// 获取上两个周期
BillingCycleResultVO twoWeeksAgo = billingCycleCalculateService.calculateBillingCycle(
    weeklyAccount, 2);
```

### 3. 季结出账 (QUARTERLY)

```java
// 配置：每季度第1天出账
// previousCycleCount = 0：当前周期，出账上季度的账单
// previousCycleCount = 1：上一周期，出账上上季度的账单
// previousCycleCount = 2：上两个周期，出账上三季度的账单

CustomerAccountVO quarterlyAccount = new CustomerAccountVO();
quarterlyAccount.setBillingCycle(BillingCycleTypeEnum.QUARTERLY.getType());
quarterlyAccount.setBillingDay(1); // 季度第1天出账

// 获取上三个周期
BillingCycleResultVO threeQuartersAgo = billingCycleCalculateService.calculateBillingCycle(
    quarterlyAccount, 3);
```

### 4. 年结出账 (YEARLY)

```java
// 配置：每年第1天出账
// previousCycleCount = 0：当前周期，出账上年的账单
// previousCycleCount = 1：上一周期，出账上上年的账单
// previousCycleCount = 2：上两个周期，出账上三年的账单

CustomerAccountVO yearlyAccount = new CustomerAccountVO();
yearlyAccount.setBillingCycle(BillingCycleTypeEnum.YEARLY.getType());
yearlyAccount.setBillingDay(1); // 年第1天出账

// 获取上五个周期
BillingCycleResultVO fiveYearsAgo = billingCycleCalculateService.calculateBillingCycle(
    yearlyAccount, 5);
```

## 关键特性

### 1. 多周期回溯功能
- **灵活回溯**：支持回溯任意数量的历史周期
- **参数简单**：使用整数参数，0表示当前周期，1表示上一周期，以此类推
- **无限制**：理论上可以回溯任意远的历史周期

### 2. 月末日期智能处理
- **扩展范围**：出账日配置从1-28扩展到1-31
- **智能适配**：当配置日期超过当月天数时，自动使用月末最后一天
- **闰年处理**：自动处理闰年2月29日的情况

### 3. 时间验证逻辑优化
- **当前周期计算**：严格验证当前时间是否为出账日
- **历史周期计算**：跳过出账日验证，允许任何时间计算历史周期

### 4. 智能周期推算
- **出账日逻辑**：如果当前是出账日，历史周期指向更早的周期
- **非出账日逻辑**：如果当前不是出账日，历史周期指向最近的完整周期
- **一致性保证**：确保周期计算的逻辑一致性

### 5. 完整的时间范围
- **精确时间戳**：返回准确的开始时间戳和结束时间戳
- **时区支持**：支持不同时区的时间计算
- **标准格式**：提供标准的出账周期标识格式（yyyyMMdd-yyyyMMdd）

## 返回结果说明

### BillingCycleResultVO 字段说明
```java
{
    "accountId": 12345,                    // 账户ID
    "allowBilling": true,                  // 是否允许出账
    "billingStartTime": *************,     // 出账开始时间戳（毫秒）
    "billingEndTime": *************,       // 出账结束时间戳（毫秒）
    "billingCycleType": 0,                 // 出账周期类型（0:月,1:周,2:季,3:年）
    "billingDay": 1,                       // 出账日配置
    "currentTime": *************,          // 当前时间戳
    "billingCycle": "********-********",   // 出账周期标识
    "timezone": "Asia/Shanghai",           // 时区
    "currency": "CNY",                     // 币种
    "description": "上一周期月结出账（自然月）：2023-01-01 00:00:00 至 2023-01-31 23:59:59"
}
```

## 向后兼容性

所有现有的方法调用都保持不变，新功能通过方法重载实现：

```java
// 现有调用方式（计算当前周期）- 完全兼容
BillingCycleResultVO current1 = billingCycleCalculateService.calculateBillingCycle(accountInfo);

// 新方式（推荐使用）
BillingCycleResultVO current2 = billingCycleCalculateService.calculateBillingCycle(accountInfo, 0);

// 已废弃方式（仍然可用，但建议迁移）
BillingCycleResultVO current3 = billingCycleCalculateService.calculateBillingCycle(accountInfo, false);

// current1、current2、current3 返回完全相同的结果
```

### 迁移建议

```java
// 旧代码
BillingCycleResultVO previous = service.calculateBillingCycle(accountInfo, true);

// 新代码（推荐）
BillingCycleResultVO previous = service.calculateBillingCycle(accountInfo, 1);

// 获取更多历史周期（新功能）
BillingCycleResultVO twoMonthsAgo = service.calculateBillingCycle(accountInfo, 2);
BillingCycleResultVO threeMonthsAgo = service.calculateBillingCycle(accountInfo, 3);
```

## 错误处理

### 常见错误情况

1. **账户信息为空**：返回错误信息
2. **出账周期配置无效**：返回配置错误信息
3. **时区配置错误**：返回时区错误信息
4. **回溯周期数为负数**：返回参数错误信息
5. **月结出账日超出范围**：返回日期配置错误信息

### 错误处理示例

```java
BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo, 2);

if (!result.getAllowBilling()) {
    // 处理错误情况
    log.warn("无法计算历史周期出账信息: {}", result.getDescription());
    return;
}

// 正常处理出账信息
processBilling(result);
```

### 新增错误类型处理

```java
// 处理负数回溯周期
BillingCycleResultVO negativeResult = service.calculateBillingCycle(accountInfo, -1);
if (!negativeResult.getAllowBilling()) {
    // 错误信息：回溯周期数量不能为负数: -1
}

// 处理无效月结出账日
CustomerAccountVO invalidAccount = new CustomerAccountVO();
invalidAccount.setBillingDay(32); // 超出1-31范围
BillingCycleResultVO invalidResult = service.calculateBillingCycle(invalidAccount, 0);
if (!invalidResult.getAllowBilling()) {
    // 错误信息：月结出账日配置无效，应为1-31: 32
}
```

## 性能说明

- **无性能影响**：新功能对现有性能无影响
- **算法复用**：多周期回溯复用现有算法，只是调整时间计算逻辑
- **时间复杂度**：保持 O(1) 时间复杂度
- **并发支持**：支持高并发调用，所有方法都是无状态的

## 注意事项

1. **时区处理**：确保账户时区配置正确，支持全球时区
2. **闰年处理**：年结出账自动处理闰年情况，2月29日智能识别
3. **边界情况**：月末、季末、年末的边界计算已优化
4. **线程安全**：所有方法都是线程安全的，支持并发调用
5. **月末配置**：配置31号出账在小月份会自动调整到月末最后一天
6. **回溯限制**：虽然理论上支持无限回溯，但建议合理控制回溯数量以避免业务逻辑复杂化

## 测试建议

建议在使用前进行充分测试，特别关注：

- **多周期回溯**：测试不同回溯数量的计算结果
- **月末日期处理**：测试31号配置在不同月份的处理
- **跨月、跨年边界**：测试边界情况的计算准确性
- **不同时区处理**：测试各种时区的计算结果
- **向后兼容性**：确保现有代码无需修改
- **参数验证**：测试各种无效参数的错误处理
- **性能测试**：验证大量历史周期查询的性能表现

### 推荐测试用例

```java
// 测试月末日期处理
@Test
void testMonthEndDateHandling() {
    // 配置31号出账，测试在2月份的处理
    CustomerAccountVO account = createAccount(31);
    // 验证2月28日/29日被识别为出账日
}

// 测试多周期回溯
@Test
void testMultipleCycleRetrieval() {
    // 测试回溯0-5个周期的结果
    for (int i = 0; i <= 5; i++) {
        BillingCycleResultVO result = service.calculateBillingCycle(account, i);
        // 验证时间顺序和周期标识
    }
}

// 测试参数验证
@Test
void testParameterValidation() {
    // 测试负数回溯周期
    // 测试无效出账日配置
    // 测试空账户信息
}
```

参考测试类：`BillingCycleCalculateServicePreviousCycleTest`（已使用Mockito重构）
