# 出账周期计算服务上一周期功能使用指南

## 功能概述

`BillingCycleCalculateService` 现已支持计算上一周期的出账信息。通过新增的 `boolean calculatePreviousCycle` 参数，您可以选择计算当前周期或上一周期的出账信息。

## 新增方法

### 主要接口方法
```java
/**
 * 计算账户出账周期信息（支持计算上一周期）
 * @param accountInfo           账户信息
 * @param calculatePreviousCycle true-计算上一周期，false-计算当前周期
 * @return 出账周期计算结果，包含是否允许出账、出账时间范围等信息
 */
BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo, boolean calculatePreviousCycle);
```

## 使用示例

### 基础用法

```java
@Resource
private BillingCycleCalculateService billingCycleCalculateService;

// 获取当前周期出账信息（现有用法）
BillingCycleResultVO currentCycle = billingCycleCalculateService.calculateBillingCycle(accountInfo);

// 等价于
BillingCycleResultVO currentCycle2 = billingCycleCalculateService.calculateBillingCycle(accountInfo, false);

// 获取上一周期出账信息（新功能）
BillingCycleResultVO previousCycle = billingCycleCalculateService.calculateBillingCycle(accountInfo, true);
```

### 实际业务场景

```java
/**
 * 补账场景：需要获取上一周期进行补账
 */
public void processSupplementaryBilling(Long accountId) {
    // 获取账户信息
    CustomerAccountVO accountInfo = getAccountInfo(accountId);
    
    // 计算上一周期出账信息
    BillingCycleResultVO previousCycle = billingCycleCalculateService.calculateBillingCycle(
        accountInfo, true);
    
    if (previousCycle.getAllowBilling()) {
        // 使用上一周期信息进行补账
        processSupplementaryBilling(accountId, previousCycle);
        log.info("补账处理完成, accountId: {}, 补账周期: {}", 
                accountId, previousCycle.getBillingCycle());
    } else {
        log.warn("无法进行补账: {}", previousCycle.getDescription());
    }
}

/**
 * 对账场景：比较当前周期和上一周期的出账信息
 */
public void reconcileBilling(Long accountId) {
    CustomerAccountVO accountInfo = getAccountInfo(accountId);
    
    // 获取当前周期和上一周期
    BillingCycleResultVO currentCycle = billingCycleCalculateService.calculateBillingCycle(
        accountInfo, false);
    BillingCycleResultVO previousCycle = billingCycleCalculateService.calculateBillingCycle(
        accountInfo, true);
    
    if (currentCycle.getAllowBilling() && previousCycle.getAllowBilling()) {
        // 进行对账处理
        reconcileData(currentCycle, previousCycle);
    }
}
```

## 不同周期类型的上一周期计算逻辑

### 1. 月结出账 (MONTHLY)
```java
// 配置：每月1号出账
// 当前周期：如果今天是1号，出账上个月的账单
// 上一周期：如果今天是1号，出账上上个月的账单；否则出账上个月的账单

CustomerAccountVO monthlyAccount = new CustomerAccountVO();
monthlyAccount.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
monthlyAccount.setBillingDay(1); // 每月1号出账

BillingCycleResultVO previousMonth = billingCycleCalculateService.calculateBillingCycle(
    monthlyAccount, true);
```

### 2. 周结出账 (WEEKLY)
```java
// 配置：每周一出账
// 当前周期：如果今天是周一，出账上周的账单
// 上一周期：如果今天是周一，出账上上周的账单；否则出账上周的账单

CustomerAccountVO weeklyAccount = new CustomerAccountVO();
weeklyAccount.setBillingCycle(BillingCycleTypeEnum.WEEKLY.getType());
weeklyAccount.setBillingDay(1); // 周一出账

BillingCycleResultVO previousWeek = billingCycleCalculateService.calculateBillingCycle(
    weeklyAccount, true);
```

### 3. 季结出账 (QUARTERLY)
```java
// 配置：每季度第1天出账
// 当前周期：如果今天是季度第1天，出账上季度的账单
// 上一周期：如果今天是季度第1天，出账上上季度的账单；否则出账上季度的账单

CustomerAccountVO quarterlyAccount = new CustomerAccountVO();
quarterlyAccount.setBillingCycle(BillingCycleTypeEnum.QUARTERLY.getType());
quarterlyAccount.setBillingDay(1); // 季度第1天出账

BillingCycleResultVO previousQuarter = billingCycleCalculateService.calculateBillingCycle(
    quarterlyAccount, true);
```

### 4. 年结出账 (YEARLY)
```java
// 配置：每年第1天出账
// 当前周期：如果今天是年第1天，出账上年的账单
// 上一周期：如果今天是年第1天，出账上上年的账单；否则出账上年的账单

CustomerAccountVO yearlyAccount = new CustomerAccountVO();
yearlyAccount.setBillingCycle(BillingCycleTypeEnum.YEARLY.getType());
yearlyAccount.setBillingDay(1); // 年第1天出账

BillingCycleResultVO previousYear = billingCycleCalculateService.calculateBillingCycle(
    yearlyAccount, true);
```

## 关键特性

### 1. 时间验证逻辑优化
- **当前周期计算**：严格验证当前时间是否为出账日
- **上一周期计算**：跳过出账日验证，允许任何时间计算上一周期

### 2. 智能周期推算
- 如果当前是出账日，上一周期指向更早的周期
- 如果当前不是出账日，上一周期指向最近的完整周期

### 3. 完整的时间范围
- 返回准确的开始时间戳和结束时间戳
- 支持不同时区的时间计算
- 提供标准的出账周期标识格式

## 返回结果说明

### BillingCycleResultVO 字段说明
```java
{
    "accountId": 12345,                    // 账户ID
    "allowBilling": true,                  // 是否允许出账
    "billingStartTime": *************,     // 出账开始时间戳（毫秒）
    "billingEndTime": *************,       // 出账结束时间戳（毫秒）
    "billingCycleType": 0,                 // 出账周期类型（0:月,1:周,2:季,3:年）
    "billingDay": 1,                       // 出账日配置
    "currentTime": *************,          // 当前时间戳
    "billingCycle": "********-********",   // 出账周期标识
    "timezone": "Asia/Shanghai",           // 时区
    "currency": "CNY",                     // 币种
    "description": "上一周期月结出账（自然月）：2023-01-01 00:00:00 至 2023-01-31 23:59:59"
}
```

## 向后兼容性

所有现有的方法调用都保持不变，新功能通过方法重载实现：

```java
// 现有调用方式（计算当前周期）
BillingCycleResultVO current1 = billingCycleCalculateService.calculateBillingCycle(accountInfo);

// 等价于新方式
BillingCycleResultVO current2 = billingCycleCalculateService.calculateBillingCycle(accountInfo, false);

// current1 和 current2 返回完全相同的结果
```

## 错误处理

### 常见错误情况
1. **账户信息为空**：返回错误信息
2. **出账周期配置无效**：返回配置错误信息
3. **时区配置错误**：返回时区错误信息

### 错误处理示例
```java
BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo, true);

if (!result.getAllowBilling()) {
    // 处理错误情况
    log.warn("无法计算上一周期出账信息: {}", result.getDescription());
    return;
}

// 正常处理出账信息
processBilling(result);
```

## 性能说明

- 新功能对现有性能无影响
- 上一周期计算复用现有算法，只是调整时间计算逻辑
- 时间复杂度保持 O(1)
- 支持高并发调用

## 注意事项

1. **时区处理**：确保账户时区配置正确
2. **闰年处理**：年结出账自动处理闰年情况
3. **边界情况**：月末、季末、年末的边界计算已优化
4. **线程安全**：所有方法都是线程安全的

## 测试建议

建议在使用前进行充分测试，特别关注：
- 不同周期类型的上一周期计算
- 跨月、跨年的边界情况
- 不同时区的处理
- 与现有业务逻辑的兼容性

参考测试类：`BillingCycleCalculateServicePreviousCycleTest`
