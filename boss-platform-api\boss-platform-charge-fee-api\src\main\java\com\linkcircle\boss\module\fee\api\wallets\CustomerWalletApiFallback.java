package com.linkcircle.boss.module.fee.api.wallets;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.ChargeForOther;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletsTransfer;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/7/30 15:06
 */
@org.springframework.stereotype.Component
@lombok.extern.slf4j.Slf4j
public class CustomerWalletApiFallback  implements FallbackFactory<CustomerWalletApi> {
    @Override
    public CustomerWalletApi create(Throwable cause) {
        return new CustomerWalletApi() {
            @Override
            public java.util.Map<java.lang.Long, com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletNameDTO> batchGetWalletNames(Set<Long> walletIds){
                log.error("调用客户-账户-钱包API失败，walletIds: {} 异常信息: ", walletIds, cause);
                return Map.of();
            }
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<?> transfer(WalletsTransfer dto){
                log.error("调用客户-账户-钱包API失败，dto: {} 异常信息: ", dto, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<java.util.List<com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO>> queryWallet(List accountIds){
                log.error("调用客户-账户-钱包API失败，accountIds: {} 异常信息: ", accountIds, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO> getWalletInfo(Long id){
                log.error("调用客户-账户-钱包API失败，id: {} 异常信息: ", id, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<?> createWallet(ChargeCustomerAccountWalletsDO dto){
                log.error("调用客户-账户-钱包API失败，dto: {} 异常信息: ", dto, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<?> updateWallet(ChargeCustomerAccountWalletsDO dto){
                log.error("调用客户-账户-钱包API失败，dto: {} 异常信息: ", dto, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<?> deleteWalletInfo(Long id){
                log.error("调用客户-账户-钱包API失败，id: {} 异常信息: ", id, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
            @Override
            public java.util.List<com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO> getWalletCashOnly(Long id){
                log.error("调用客户-账户-钱包API失败，id: {} 异常信息: ", id, cause);
                return List.of();
            }
            @Override
            public com.linkcircle.boss.framework.common.model.CommonResult<?> chargeWallet(ChargeForOther dto){
                log.error("调用客户-账户-钱包API失败，dto: {} 异常信息: ", dto, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }

}
