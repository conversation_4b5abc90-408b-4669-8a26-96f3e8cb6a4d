package com.linkcircle.boss.module.crm.api.productservice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChargeProductServicePriceVo  {
    /**
     * 主键id
     */
    //@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 根据服务名称生成编码
     */
    @Schema(description = "服务编码")
    private String serviceCode;

    /**
     * 版本名称（如v1.0 满足规范便于校验）
     */
    @Schema(description = "版本名称")
    private String versionName;

    /**
     * 版本号排序使用
     */
    @Schema(description = "版本号排序使用")
    private Integer versionOrder;

    /**
     * 服务描述
     */
    @Schema(description = "服务描述")
    private String description;

    /**
     * 0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费
     */
    @Schema(description = "0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费")
    private Integer chargeType;

    /**
     * 支付方式，0:现金，1：积分，2：全选
     */
    @Schema(description = "支付方式，0:现金，1：积分，2：全选")
    private Integer paymentOptions;

    /**
     * 单位标签
     */
    @Schema(description = "单位标签")
    private String unitLabel;

    /**
     * 目录价格配置
     */
    @Schema(description = "目录价格配置")
    private String currencyPriceJson;

    /**
     * 是否含套餐外,0:不包含，1：包含(阶梯型), 2: 包含(固定型)
     */
    @Schema(description = "是否含套餐外,0:不包含，1：包含(阶梯型), 2: 包含(固定型)")
    private Integer inPackage;

    /**
     * 按量付费计费类型,0:固定，1：阶梯

     @Schema(description = "按量付费计费类型,0:固定，1：阶梯")
     private Integer usagePriceType; */

    /**
     * 套餐内价格配置

     @Schema(description = "套餐内价格配置")
     private String inPackagePriceAllocation; */

    /**
     * 套餐外价格配置

     @Schema(description = "套餐外价格配置")
     private String outPackagePriceAllocation; */

    /**
     * 间隔时长单位
     */
    @Schema(description = "间隔时长单位")
    private Integer period;

    /**
     * 间隔时长,间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年
     */
    @Schema(description = "间隔时长,间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年")
    private Integer unitPeriod;

    /**
     * 服务状态, 0：未激活，1：激活，2：存档
     */
    @Schema(description = "服务状态, 0：未激活，1：激活，2：存档")
    private Integer status;

    /**
     * 配置资源, 0：未配置，1：已配置
     * 供应商提供资源
     */
    @Schema(description = "配置资源, 0：未配置，1：已配置")
    private Integer configResource;

    /**
     * 配置量表(按量和套餐计费的套餐内配置才能激活), 0:不涉及, 1：未配置，2：已配置
     * 与业务侧对接协定
     */
    @Schema(description = "配置量表(按量和套餐计费的套餐内配置才能激活), 0:不涉及, 1：未配置，2：已配置")
    private Integer configBusProto;

    /**
     * 关联量表ID
     */
    @Schema(description = "关联量表ID")
    private Long scaleId;

    /**
     * 货币类型字典ID串
     */
    @Schema(description = "货币ID串(来源字典);便于后续查询")
    private String currencyDictIds;

    /**
     * 租户编号
     */
    @Schema(description = "租户编号")
    private Long tenantId;



}
