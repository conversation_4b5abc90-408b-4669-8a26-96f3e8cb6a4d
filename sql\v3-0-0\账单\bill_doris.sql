CREATE TABLE `${serviceCode}_prepaid_income_bill_detail_${year}`
(
    `bill_detail_id`           bigint(20)     NOT NULL COMMENT '收入账单明细id',
    `business_id`              varchar(128)   NOT NULL COMMENT '业务唯一id',
    `business_time`            bigint         NOT NULL COMMENT '业务产生的时间戳',
    `service_id`               bigint(20)     NOT NULL COMMENT '服务id',
    `service_code`             varchar(128)   NOT NULL COMMENT '服务编码',
    `customer_id`              bigint(20)     NOT NULL COMMENT '客户id',
    `account_id`               bigint(20)     NOT NULL COMMENT '账户id',
    `product_id`               bigint(20)     NOT NULL COMMENT '产品id',
    `entity_id`                bigint(20)     NOT NULL COMMENT '主体id',
    `contract_id`              bigint(20)     NULL COMMENT '合同id',
    `wallet_id`                bigint(20)     NOT NULL COMMENT '钱包id',
    `plan_id`                  bigint(20)     NULL COMMENT '计划id',
    `subscribe_id`             bigint(20)     NULL COMMENT '订阅id',
    `discount_id`              varchar(128)   NULL COMMENT '优惠id',
    `usage_count`              decimal(18, 6) NOT NULL COMMENT '消耗量',
    `usage_unit`               varchar(16)    NOT NULL COMMENT '消耗量单位',
    `payment_method`           tinyint        NOT NULL COMMENT '支付方式 0-现金, 1-积分',
    `billing_type`             tinyint        NOT NULL COMMENT '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费',
    `in_trial`                 boolean        NULL COMMENT '是否在计费类型试用期内',
    `bill_status`              tinyint        NOT NULL COMMENT '账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清',
    `wallet_deduct_status`     tinyint        NULL DEFAULT 0 COMMENT '钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）',
    `point_amount`             decimal(18, 0) NULL DEFAULT 0 COMMENT '积分',
    `cash_amount`              decimal(18, 6) NULL DEFAULT 0 COMMENT '现金',
    `tax_rate`                 decimal(5, 4)  NULL DEFAULT 0 COMMENT '税率',
    `unit_price`               decimal(18, 6) NULL DEFAULT 0 COMMENT '单价',
    `original_price`           decimal(18, 6) NULL DEFAULT 0 COMMENT '目录价(原价)',
    `discounted_price`         decimal(18, 6) NULL DEFAULT 0 COMMENT '订阅价(优惠后的价格)',
    `discount_amount`          decimal(18, 6) NULL DEFAULT 0 COMMENT '优惠的金额',
    `invoiced_amount`          decimal(18, 6) NULL DEFAULT 0 COMMENT '已开票金额',
    `available_invoice_amount` decimal(18, 6) NULL DEFAULT 0 COMMENT '可开票金额(=优惠价-已开票金额)',
    `amount_with_tax`          decimal(18, 6) NULL DEFAULT 0 COMMENT '含税总金额',
    `amount_without_tax`       decimal(18, 6) NULL DEFAULT 0 COMMENT '不含税金额',
    `paid_amount`              decimal(18, 6) NULL DEFAULT 0 COMMENT '已支付金额',
    `unpaid_amount`            decimal(18, 6) NULL DEFAULT 0 COMMENT '未支付金额',
    `currency`                 varchar(3)     NULL COMMENT '货币单位 CNY USD',
    `payment_time`             bigint         NULL COMMENT '实际支付时间戳',
    `billing_time`             bigint         NULL COMMENT '出账时间戳',
    `create_time`              bigint         NULL COMMENT '数据创建时间戳',
    `deleted`                  boolean        NULL COMMENT '是否删除',
    `request_id`               varchar(128)   NOT NULL COMMENT '接口请求id',
    `callback_url`             varchar(500)        DEFAULT NULL COMMENT '回调地址，异步通知使用',
    `callback_status`          tinyint(4)          DEFAULT '0' COMMENT '回调状态：0-未回调，1-回调成功，2-回调失败',
    `extend_data`              json                DEFAULT NULL COMMENT '扩展数据JSON，存储特殊业务数据',
    `rate_details`             json                DEFAULT NULL COMMENT '费用详情json',
    `discount_details`         json                DEFAULT NULL COMMENT '服务优惠详情json',
    -- 业务字段...
    `caller_number`            varchar(128)   NULL COMMENT '主叫号码',
    `callee_number`            varchar(128)   NULL COMMENT '被叫号码',
    bill_code                  varchar(50) comment '账单号码-唯一',
    refund_invoice_amount      decimal(18, 6) comment '开票退款金额',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_customer_id (`customer_id`) USING INVERTED COMMENT '客户ID的Bitmap索引',
    INDEX idx_product_id (`product_id`) USING INVERTED COMMENT '产品的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`bill_detail_id`, `business_id`)
COMMENT '预付费-收入账单-明细表 ${serviceCode} - ${year}'
DISTRIBUTED BY HASH(`bill_detail_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"

);

CREATE TABLE `${serviceCode}_postpaid_income_bill_detail_${year}`
(
    `bill_detail_id`           bigint(20)     NOT NULL COMMENT '收入账单明细id',
    `business_id`              varchar(128)   NOT NULL COMMENT '业务唯一id',
    `business_time`            bigint(20)     NOT NULL COMMENT '业务产生的时间戳',
    `service_id`               bigint(20)     NOT NULL COMMENT '服务id',
    `service_code`             varchar(128)   NOT NULL COMMENT '服务编码',
    `customer_id`              bigint(20)     NOT NULL COMMENT '客户id',
    `account_id`               bigint(20)     NOT NULL COMMENT '账户id',
    `product_id`               bigint(20)     NOT NULL COMMENT '产品id',
    `entity_id`                bigint(20)     NOT NULL COMMENT '主体id',
    `contract_id`              bigint(20)          DEFAULT NULL COMMENT '合同id',
    `wallet_id`                bigint(20)     NOT NULL COMMENT '钱包id',
    `plan_id`                  bigint(20)          DEFAULT NULL COMMENT '计划id',
    `discount_id`              varchar(128)        DEFAULT NULL COMMENT '优惠id',
    `subscribe_id`             bigint(20)     NULL COMMENT '订阅id',
    `usage_count`              DECIMAL(18, 6) NOT NULL COMMENT '消耗量',
    `usage_unit`               varchar(16)    NOT NULL COMMENT '消耗量单位',
    `payment_method`           tinyint(2)     NOT NULL COMMENT '支付方式 0-现金, 1-积分',
    `billing_type`             tinyint(2)     NOT NULL COMMENT '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费',
    `in_trial`                 boolean COMMENT '在试用期内',
    `bill_status`              tinyint(2)     NOT NULL COMMENT '账单状态	 0-草稿, 1-待支付, 2-已支付, 3-未结清',
    `wallet_deduct_status`     tinyint        NULL DEFAULT 0 COMMENT '钱包扣款状态（0-未扣款、1-成功、2-失败、3-处理中, 4-退款）',
    `point_amount`             decimal(18, 0) NULL DEFAULT 0 COMMENT '积分',
    `cash_amount`              decimal(18, 6) NULL DEFAULT 0 COMMENT '现金',
    `tax_rate`                 DECIMAL(5, 4)       DEFAULT 0 COMMENT '税率',
    `unit_price`               DECIMAL(18, 6)      DEFAULT 0 COMMENT '单价',
    `original_price`           DECIMAL(18, 6)      DEFAULT 0 COMMENT '目录价(原价)',
    `discounted_price`         DECIMAL(18, 6)      DEFAULT 0 COMMENT '订阅价(优惠后的价格)',
    `discount_amount`          DECIMAL(18, 6)      DEFAULT 0 COMMENT '优惠的金额',
    `invoiced_amount`          DECIMAL(18, 6)      DEFAULT 0 COMMENT '已开票金额',
    `available_invoice_amount` DECIMAL(18, 6)      DEFAULT 0 COMMENT '可开票金额(=优惠价-已开票金额)',
    `amount_with_tax`          DECIMAL(18, 6)      DEFAULT 0 COMMENT '含税总金额',
    `amount_without_tax`       DECIMAL(18, 6)      DEFAULT 0 COMMENT '不含税金额',
    `currency`                 varchar(3)          DEFAULT NULL COMMENT '货币单位 CNY USD ',
    `payment_time`             bigint(20)          DEFAULT NULL COMMENT '实际支付时间戳',
    `billing_time`             bigint(20)          DEFAULT NULL COMMENT '出账时间戳',
    `create_time`              bigint(20)     NOT NULL COMMENT '数据创建时间戳',
    `deleted`                  boolean COMMENT '是否删除',
    `request_id`               varchar(128)   NOT NULL COMMENT '接口请求id',
    `extend_data`              json                DEFAULT NULL COMMENT '扩展数据JSON，存储特殊业务数据',
    `rate_details`             json                DEFAULT NULL COMMENT '费用详情json',
    `discount_details`         json                DEFAULT NULL COMMENT '服务优惠详情json',
    -- 业务字段...
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_customer_id (`customer_id`) USING INVERTED COMMENT '客户ID的Bitmap索引',
    INDEX idx_product_id (`product_id`) USING INVERTED COMMENT '产品的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`bill_detail_id`, `business_id`)
COMMENT '后付费-收入账单-明细表 ${serviceCode} - ${year}'
DISTRIBUTED BY HASH(`bill_detail_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

CREATE TABLE `${serviceCode}_prepaid_cost_bill_detail_${year}`
(
    `bill_detail_id`      bigint(20)     NOT NULL COMMENT '成本账单明细id',
    `business_id`         varchar(128)   NOT NULL COMMENT '业务唯一id',
    `business_time`       bigint(20)     NOT NULL COMMENT '业务产生的时间戳',
    `resource_service_id` bigint(20)     NOT NULL COMMENT '资源服务id',
    `service_code`        varchar(128)   NOT NULL COMMENT '服务编码',
    `supplier_id`         bigint(20)     NOT NULL COMMENT '供应商id',
    `account_id`          bigint(20)     NOT NULL COMMENT '账户id',
    `resource_id`         bigint(20)     NOT NULL COMMENT '资源id',
    `entity_id`           bigint(20)     NOT NULL COMMENT '主体id',
    `purchase_id`         bigint(20)     NULL COMMENT '采购id',
    `usage_count`         DECIMAL(18, 6) NOT NULL COMMENT '消耗量',
    `usage_unit`          varchar(16)    NOT NULL COMMENT '消耗量单位',
    `payment_type`        tinyint(2)     NOT NULL COMMENT '支付类型 0-预付费, 1-后付费',
    `payment_method`      tinyint(2)     NOT NULL COMMENT '支付方式 0-现金, 1-积分',
    `billing_type`        tinyint(2)     NOT NULL COMMENT '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费',
    `in_trial`            boolean        null COMMENT '是否在试用期内',
    `bill_status`         tinyint(2)     NOT NULL COMMENT '账单状态	 0-草稿, 1-待支付, 2-已支付, 3-未结清',
    `tax_rate`            DECIMAL(5, 4)       DEFAULT 0 COMMENT '税率',
    `unit_price`          DECIMAL(18, 6)      DEFAULT 0 COMMENT '单价',
    `point_amount`        decimal(18, 0) NULL DEFAULT 0 COMMENT '积分',
    `cash_amount`         decimal(18, 6) NULL DEFAULT 0 COMMENT '现金',
    `original_price`      DECIMAL(18, 6)      DEFAULT 0 COMMENT '原价(目录价格)',
    `currency`            varchar(3)          DEFAULT NULL COMMENT '货币单位 CNY USD ',
    `payment_time`        bigint(20)          DEFAULT NULL COMMENT '实际支付时间戳',
    `billing_time`        bigint(20)          DEFAULT NULL COMMENT '出账时间戳',
    `create_time`         bigint(20)          DEFAULT NULL COMMENT '数据创建时间戳',
    `deleted`             boolean COMMENT '是否删除',
    `request_id`          varchar(128)   NOT NULL COMMENT '接口请求id',
    `extend_data`         json                DEFAULT NULL COMMENT '扩展数据JSON，存储特殊业务数据',
    `rate_details`        json                DEFAULT NULL COMMENT '费用详情json',
    -- 业务字段...
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_resource_id (`resource_id`) USING INVERTED COMMENT '资源ID的Bitmap索引',
    INDEX idx_supplier_id (`supplier_id`) USING INVERTED COMMENT '供应商ID的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`bill_detail_id`, `business_id`)
COMMENT '预付费-成本账单-明细表 ${serviceCode} - ${year}'
DISTRIBUTED BY HASH(`bill_detail_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

CREATE TABLE `${serviceCode}_postpaid_cost_bill_detail_${year}`
(
    `bill_detail_id`      bigint(20)     NOT NULL COMMENT '成本账单明细id',
    `business_id`         varchar(128)   NOT NULL COMMENT '业务唯一id',
    `business_time`       bigint(20)     NOT NULL COMMENT '业务产生的时间戳',
    `resource_service_id` bigint(20)     NOT NULL COMMENT '资源服务id',
    `service_code`        varchar(128)   NOT NULL COMMENT '服务编码',
    `supplier_id`         bigint(20)     NOT NULL COMMENT '供应商id',
    `account_id`          bigint(20)     NOT NULL COMMENT '账户id',
    `resource_id`         bigint(20)     NOT NULL COMMENT '资源id',
    `entity_id`           bigint(20)     NOT NULL COMMENT '主体id',
    `purchase_id`         bigint(20)     NULL COMMENT '采购id',
    `usage_count`         DECIMAL(18, 6) NOT NULL COMMENT '消耗量',
    `usage_unit`          varchar(16)    NOT NULL COMMENT '消耗量单位',
    `payment_type`        tinyint(2)     NOT NULL COMMENT '支付类型 0-预付费, 1-后付费',
    `payment_method`      tinyint(2)     NOT NULL COMMENT '支付方式 0-现金, 1-积分',
    `billing_type`        tinyint(2)     NOT NULL COMMENT '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费',
    `in_trial`            boolean        null COMMENT '在试用期内',
    `bill_status`         tinyint(2)     NOT NULL COMMENT '账单状态	 0-草稿, 1-待支付, 2-已支付, 3-未结清',
    `tax_rate`            DECIMAL(5, 4)       DEFAULT 0 COMMENT '税率',
    `unit_price`          DECIMAL(18, 6)      DEFAULT 0 COMMENT '单价',
    `point_amount`        decimal(18, 0) NULL DEFAULT 0 COMMENT '积分',
    `cash_amount`         decimal(18, 6) NULL DEFAULT 0 COMMENT '现金',
    `original_price`      DECIMAL(18, 6)      DEFAULT 0 COMMENT '原价(目录价格)',
    `currency`            varchar(3)          DEFAULT NULL COMMENT '货币单位 CNY USD ',
    `payment_time`        bigint(20)          DEFAULT NULL COMMENT '实际支付时间戳',
    `billing_time`        bigint(20)          DEFAULT NULL COMMENT '出账时间戳',
    `create_time`         bigint(20)     NOT NULL COMMENT '数据创建时间戳',
    `deleted`             boolean        null COMMENT '是否删除',
    `request_id`          varchar(128)   NOT NULL COMMENT '接口请求id',
    `extend_data`         json                DEFAULT NULL COMMENT '扩展数据JSON，存储特殊业务数据',
    `rate_details`        json                DEFAULT NULL COMMENT '费用详情json',
    -- 业务字段...
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_resource_id (`resource_id`) USING INVERTED COMMENT '资源ID的Bitmap索引',
    INDEX idx_supplier_id (`supplier_id`) USING INVERTED COMMENT '供应商ID的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`bill_detail_id`, `business_id`)
COMMENT '后付费-成本账单-明细表 ${serviceCode} - ${year}'
DISTRIBUTED BY HASH(`bill_detail_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

CREATE TABLE `customer_daily_usage_bill_${year}`
(
    `id`                   BIGINT(20)  NOT NULL COMMENT '主键 ID',
    `create_time`          BIGINT(20)  NOT NULL COMMENT '创建时间戳',
    `bill_date`            VARCHAR(16) NOT NULL COMMENT '账单日期(yyyyMMddhh)',
    `timezone`             VARCHAR(32) NOT NULL COMMENT '时区',
    `customer_id`          bigint(20)  NOT NULL COMMENT '客户 ID',
    `account_id`           bigint(20)  NOT NULL COMMENT '账户 ID',
    `product_id`           bigint(20)  NOT NULL COMMENT '产品 ID',
    `service_id`           bigint(20)  NOT NULL COMMENT '服务 ID',
    `usage_count`          DECIMAL(18, 6) DEFAULT 0 COMMENT '使用量',
    `usage_unit`           VARCHAR(16)    DEFAULT NULL COMMENT '使用量单位',
    `cash_amount`          DECIMAL(18, 6) DEFAULT 0 COMMENT '现金消费金额',
    `cash_amount_currency` varchar(3)     DEFAULT 0 COMMENT '现金消费金额货币单位',
    `point_amount`         DECIMAL(18, 0) DEFAULT 0 COMMENT '积分消费金额',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_product_id (`product_id`) USING INVERTED COMMENT 'product_id的Bitmap索引',
    INDEX idx_customer_id (`customer_id`) USING INVERTED COMMENT '供应商ID的Bitmap索引',
    INDEX idx_service_id (`service_id`) USING INVERTED COMMENT 'service_id的Bitmap索引'
)
    ENGINE = OLAP UNIQUE KEY(`id`)
COMMENT '客户-每日-账单表'
DISTRIBUTED BY HASH(`id`) BUCKETS 10
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "storage_format" = "V2",
    "enable_unique_key_merge_on_write" = "true"
);

CREATE TABLE `supplier_daily_usage_bill_${year}`
(
    `id`                   BIGINT(20)  NOT NULL COMMENT '主键 ID',
    `create_time`          BIGINT(20)  NOT NULL COMMENT '创建时间戳',
    `bill_date`            VARCHAR(16) NOT NULL COMMENT '账单日期(yyyyMMddhh)',
    `supplier_id`          bigint(20)  NOT NULL COMMENT '供应商 ID',
    `account_id`           bigint(20)  NOT NULL COMMENT '账户 ID',
    `resource_id`          bigint(20)  NOT NULL COMMENT '资源 ID',
    `resource_service_id`  bigint(20)  NOT NULL COMMENT '资源服务 ID',
    `usage_count`          DECIMAL(18, 6) DEFAULT 0 COMMENT '使用量',
    `usage_unit`           VARCHAR(16)    DEFAULT NULL COMMENT '使用量单位',
    `cash_amount`          DECIMAL(18, 6) DEFAULT 0 COMMENT '现金消费金额',
    `cash_amount_currency` varchar(3)     DEFAULT 0 COMMENT '现金消费金额货币单位',
    `point_amount`         DECIMAL(18, 0) DEFAULT 0 COMMENT '积分消费金额',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_resource_id (`resource_id`) USING INVERTED COMMENT 'resource_id的Bitmap索引',
    INDEX idx_supplier_id (`supplier_id`) USING INVERTED COMMENT 'supplier_id的Bitmap索引',
    INDEX idx_resource_service_id (`resource_service_id`) USING INVERTED COMMENT 'resource_service_id的Bitmap索引'
)
    ENGINE = OLAP UNIQUE KEY(`id`)
COMMENT '供应商-每日-账单表 ${year}'
DISTRIBUTED BY HASH(`id`) BUCKETS 10
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "storage_format" = "V2",
    "enable_unique_key_merge_on_write" = "true"
);

CREATE TABLE `postpaid_product_income_bill_${year}`
(
    `product_bill_id`          bigint(20)     NOT NULL COMMENT '产品账单id',
    `business_time`            bigint(20)     NOT NULL COMMENT '业务产生的时间戳',
    `service_id`               bigint(20)     NOT NULL COMMENT '服务id',
    `customer_id`              bigint(20)     NOT NULL COMMENT '客户id',
    `account_id`               bigint(20)     NOT NULL COMMENT '账户id',
    `product_id`               bigint(20)     NOT NULL COMMENT '产品id',
    `entity_id`                bigint(20)     NOT NULL COMMENT '主体id',
    `contract_id`              bigint(20)          DEFAULT NULL COMMENT '合同id',
    `wallet_id`                bigint(20)     NOT NULL COMMENT '钱包id',
    `plan_id`                  bigint(20)          DEFAULT NULL COMMENT '计划id',
    `discount_id`              varchar(128)        DEFAULT NULL COMMENT '优惠id',
    `subscribe_id`             bigint(20)     NULL COMMENT '订阅id',
    `usage_count`              DECIMAL(18, 6) NOT NULL COMMENT '消耗量',
    `usage_unit`               varchar(16)    NOT NULL COMMENT '消耗量单位',

    `payment_method`           tinyint(2)     NOT NULL COMMENT '支付方式 0-现金, 1-积分',
    `in_trial`                 boolean COMMENT '在试用期内',
    `bill_status`              tinyint(2)     NOT NULL COMMENT '账单状态	 0-草稿, 1-待支付, 2-已支付, 3-未结清',

    `tax_rate`                 DECIMAL(5, 4)       DEFAULT 0 COMMENT '税率',
    `unit_price`               DECIMAL(18, 6)      DEFAULT 0 COMMENT '单价',
    `original_price`           DECIMAL(18, 6)      DEFAULT 0 COMMENT '目录价(原价)',
    `discounted_price`         DECIMAL(18, 6)      DEFAULT 0 COMMENT '订阅价(优惠后的价格)',
    `discount_amount`          DECIMAL(18, 6)      DEFAULT 0 COMMENT '优惠的金额',
    `invoiced_amount`          DECIMAL(18, 6)      DEFAULT 0 COMMENT '已开票金额',
    `available_invoice_amount` DECIMAL(18, 6)      DEFAULT 0 COMMENT '可开票金额(=优惠价-已开票金额)',
    `amount_with_tax`          DECIMAL(18, 6)      DEFAULT 0 COMMENT '含税总金额',
    `amount_without_tax`       DECIMAL(18, 6)      DEFAULT 0 COMMENT '不含税金额',
    `currency`                 varchar(3)          DEFAULT NULL COMMENT '货币单位 CNY USD ',

    `payment_time`             bigint(20)          DEFAULT NULL COMMENT '实际支付时间戳',
    `billing_time`             bigint(20)          DEFAULT NULL COMMENT '出账时间戳',
    `billing_start_time`       bigint(20)          DEFAULT NULL COMMENT '出账开始时间戳（毫秒）',
    `billing_end_time`         bigint(20)          DEFAULT NULL COMMENT '出账结束时间戳（毫秒）',
    `billing_cycle_type`       tinyint(2)          DEFAULT NULL COMMENT '出账周期类型，0：每月，1：每周，2：每季度，3：每年',
    `billing_day`              int                 DEFAULT NULL COMMENT '出账第几天',
    `billing_cycle`            varchar(32)         DEFAULT NULL COMMENT '出账周期标识（如202507-202509）',
    `timezone`                 varchar(32)         DEFAULT NULL COMMENT '时区',
    `create_time`              bigint(20)     NOT NULL COMMENT '数据创建时间戳',
    `deleted`                  boolean COMMENT '是否删除',
    `discount_config`          text                DEFAULT NULL COMMENT '优惠配置json: {"discount_name":"优惠金额","type":"优惠类型：0-固定, 1-百分比","amount_percentage":"金额/百分比","in_package":"是否套餐内  true/false"}',
    `discount_details`         json                DEFAULT NULL COMMENT '服务优惠详情json',
    `bill_discount_details`    json                DEFAULT NULL COMMENT '账单优惠详情json',
    `callback_url`             varchar(500)        DEFAULT NULL COMMENT '回调地址，异步通知使用',
    `callback_status`          tinyint(4)          DEFAULT '0' COMMENT '回调状态：0-未回调，1-回调成功，2-回调失败',
    `wallet_deduct_status`     tinyint        NULL DEFAULT 0 COMMENT '钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）',
    bill_code                  varchar(50) comment '账单号码-唯一',
    refund_invoice_amount      decimal(18, 6) comment '开票退款金额',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_customer_id (`customer_id`) USING INVERTED COMMENT '客户ID的Bitmap索引',
    INDEX idx_product_id (`product_id`) USING INVERTED COMMENT '产品的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`product_bill_id`)
COMMENT '后付费-收入-按产品收入-账单表 ${year}'
DISTRIBUTED BY HASH(`product_bill_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

CREATE TABLE `postpaid_product_service_income_bill_${year}`
(
    `product_service_bill_id` bigint(20) NOT NULL COMMENT '产品服务账单id',
    `billing_time`            bigint(20)      DEFAULT NULL COMMENT '出账时间戳',
    `service_id`              bigint(20) NOT NULL COMMENT '服务id',
    `service_code`            varchar(32)     DEFAULT NULL COMMENT '服务编码',
    `bill_id`                 bigint(20) NOT NULL COMMENT '产品账单id',
    `entity_id`               bigint(20) NOT NULL COMMENT '主体id',
    `contract_id`             bigint(20)      DEFAULT NULL COMMENT '合同id',
    `customer_id`             bigint(20) NOT NULL COMMENT '客户id',
    `account_id`              bigint(20) NOT NULL COMMENT '账户id',
    `wallet_id`               bigint(20) NOT NULL COMMENT '钱包id',
    `plan_id`                 bigint(20)      DEFAULT NULL COMMENT '计划id',
    `product_id`              bigint(20) NOT NULL COMMENT '产品id',
    `subscribe_id`            bigint(20)      DEFAULT NULL COMMENT '订阅id',

    `payment_method`          tinyint(2) NOT NULL COMMENT '支付方式 0-现金, 1-积分',

    `unit_price`              DECIMAL(18, 6)  DEFAULT 0 COMMENT '单价',
    `usage_count`             DECIMAL(18, 6)  DEFAULT 0 COMMENT '消耗量',
    `usage_unit`              varchar(16)     DEFAULT NULL COMMENT '消耗量单位',
    `tax_rate`                DECIMAL(5, 4)   DEFAULT 0 COMMENT '税率',

    `original_price`          DECIMAL(18, 6)  DEFAULT 0 COMMENT '目录价(原价)',
    `discounted_price`        DECIMAL(18, 6)  DEFAULT 0 COMMENT '优惠后价格',
    `discount_amount`         DECIMAL(18, 6)  DEFAULT 0 COMMENT '优惠金额',
    `amount_with_tax`         DECIMAL(18, 6)  DEFAULT 0 COMMENT '含税总金额',
    `amount_without_tax`      DECIMAL(18, 6)  DEFAULT 0 COMMENT '不含税金额',
    `currency`                varchar(3)      DEFAULT NULL COMMENT '货币单位 CNY USD ',

    `payment_time`            bigint(20)      DEFAULT NULL COMMENT '实际支付时间戳',
    `billing_start_time`      bigint(20)      DEFAULT NULL COMMENT '出账开始时间戳（毫秒）',
    `billing_end_time`        bigint(20)      DEFAULT NULL COMMENT '出账结束时间戳（毫秒）',
    `billing_cycle_type`      tinyint(2)      DEFAULT NULL COMMENT '出账周期类型，0：每月，1：每周，2：每季度，3：每年',
    `billing_day`             int             DEFAULT NULL COMMENT '出账第几天',
    `billing_cycle`           varchar(32)     DEFAULT NULL COMMENT '出账周期标识（如202507-202509）',
    `timezone`                varchar(32)     DEFAULT NULL COMMENT '时区',
    `create_time`             bigint(20) NOT NULL COMMENT '数据创建时间戳',
    `deleted`                 boolean COMMENT '是否删除',

    `tiered_price`            json            DEFAULT NULL COMMENT '阶梯费用json {min_usage, max_usage, unit_price, usage_unit, tax_rate, original_price }',
    `rate_details`            json            DEFAULT NULL COMMENT '费用详情json',
    `discount_details`        json            DEFAULT NULL COMMENT '服务优惠详情json',
    `wallet_deduct_status`    tinyint    NULL DEFAULT 0 COMMENT '钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）',

    INDEX idx_bill_id (`bill_id`) USING INVERTED COMMENT 'bill_id的Bitmap索引',
    INDEX idx_customer_id (`customer_id`) USING INVERTED COMMENT '客户ID的Bitmap索引',
    INDEX idx_product_id (`product_id`) USING INVERTED COMMENT '产品的Bitmap索引',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT 'account_id的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`product_service_bill_id`)
COMMENT '后付费-收入-按产品收入-服务详细-账单表 ${year}'
DISTRIBUTED BY HASH(`product_service_bill_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

CREATE TABLE `postpaid_resource_cost_bill_${year}`
(
    `resource_bill_id`         bigint(20)     NOT NULL COMMENT '资源成本账单id',
    `business_time`            bigint(20)     NOT NULL COMMENT '业务产生的时间戳',
    `service_id`               bigint(20)     NOT NULL COMMENT '服务id',
    `supplier_id`              bigint(20)     NOT NULL COMMENT '供应商id',
    `account_id`               bigint(20)     NOT NULL COMMENT '账户id',
    `resource_id`              bigint(20)     NOT NULL COMMENT '资源id',
    `entity_id`                bigint(20)     NOT NULL COMMENT '主体id',
    `discount_id`              varchar(128)   DEFAULT NULL COMMENT '优惠id',
    `purchase_id`              bigint(20)     NULL COMMENT '采购id',
    `usage_count`              DECIMAL(18, 6) NOT NULL COMMENT '消耗量',
    `usage_unit`               varchar(16)    NOT NULL COMMENT '消耗量单位',

    `payment_method`           tinyint(2)     NOT NULL COMMENT '支付方式 0-现金, 1-积分',
    `billing_type`             tinyint(2)     NOT NULL COMMENT '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费',
    `in_trial`                 boolean COMMENT '在试用期内',
    `bill_status`              tinyint(2)     NOT NULL COMMENT '账单状态	 0-草稿, 1-待支付, 2-已支付, 3-未结清',

    `tax_rate`                 DECIMAL(5, 4)  DEFAULT 0 COMMENT '税率',
    `unit_price`               DECIMAL(18, 6) DEFAULT 0 COMMENT '单价',
    `original_price`           DECIMAL(18, 6) DEFAULT 0 COMMENT '目录价(原价)',
    `discounted_price`         DECIMAL(18, 6) DEFAULT 0 COMMENT '订阅价(优惠后的价格)',
    `discount_amount`          DECIMAL(18, 6) DEFAULT 0 COMMENT '优惠的金额',
    `invoiced_amount`          DECIMAL(18, 6) DEFAULT 0 COMMENT '已开票金额',
    `available_invoice_amount` DECIMAL(18, 6) DEFAULT 0 COMMENT '可开票金额(=优惠价-已开票金额)',
    `amount_with_tax`          DECIMAL(18, 6) DEFAULT 0 COMMENT '含税总金额',
    `amount_without_tax`       DECIMAL(18, 6) DEFAULT 0 COMMENT '不含税金额',
    `currency`                 varchar(3)     DEFAULT NULL COMMENT '货币单位 CNY USD ',

    `payment_time`             bigint(20)     DEFAULT NULL COMMENT '实际支付时间戳',
    `billing_time`             bigint(20)     DEFAULT NULL COMMENT '出账时间戳',
    `create_time`              bigint(20)     NOT NULL COMMENT '数据创建时间戳',
    `deleted`                  boolean COMMENT '是否删除',

    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_supplier_id (`supplier_id`) USING INVERTED COMMENT '供应商ID的Bitmap索引',
    INDEX idx_resource_id (`resource_id`) USING INVERTED COMMENT '资源ID的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`resource_bill_id`)
COMMENT '后付费-成本-按资源成本-账单表 ${year}'
DISTRIBUTED BY HASH(`resource_bill_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

CREATE TABLE `postpaid_resource_service_cost_bill_${year}`
(
    `resource_service_bill_id` bigint(20) NOT NULL COMMENT '资源服务账单id',
    `billing_time`             bigint(20)     DEFAULT NULL COMMENT '出账时间戳',
    `service_id`               bigint(20) NOT NULL COMMENT '服务id',
    `service_code`             varchar(32)    DEFAULT NULL COMMENT '服务编码',
    `resource_bill_id`         bigint(20) NOT NULL COMMENT '资源账单id',
    `entity_id`                bigint(20) NOT NULL COMMENT '主体id',
    `supplier_id`              bigint(20) NOT NULL COMMENT '供应商id',
    `account_id`               bigint(20) NOT NULL COMMENT '账户id',
    `resource_id`              bigint(20) NOT NULL COMMENT '资源id',

    `payment_method`           tinyint(2) NOT NULL COMMENT '支付方式 0-现金, 1-积分',

    `unit_price`               DECIMAL(18, 6) DEFAULT 0 COMMENT '单价',
    `usage_count`              DECIMAL(18, 6) DEFAULT 0 COMMENT '消耗量',
    `usage_unit`               varchar(16)    DEFAULT NULL COMMENT '消耗量单位',
    `tax_rate`                 DECIMAL(5, 4)  DEFAULT 0 COMMENT '税率',

    `original_price`           DECIMAL(18, 6) DEFAULT 0 COMMENT '目录价(原价)',
    `amount_with_tax`          DECIMAL(18, 6) DEFAULT 0 COMMENT '含税总金额',
    `amount_without_tax`       DECIMAL(18, 6) DEFAULT 0 COMMENT '不含税金额',
    `currency`                 varchar(3)     DEFAULT NULL COMMENT '货币单位 CNY USD ',

    `payment_time`             bigint(20)     DEFAULT NULL COMMENT '实际支付时间戳',
    `create_time`              bigint(20) NOT NULL COMMENT '数据创建时间戳',
    `deleted`                  boolean COMMENT '是否删除',

    `tiered_price`             json           DEFAULT NULL COMMENT '阶梯费用json {min_usage, max_usage, unit_price, usage_unit, tax_rate, original_price }',

    INDEX idx_resource_bill_id (`resource_bill_id`) USING INVERTED COMMENT '资源账单id的Bitmap索引',
    INDEX idx_supplier_id (`supplier_id`) USING INVERTED COMMENT '供应商id的Bitmap索引',
    INDEX idx_resource_id (`resource_id`) USING INVERTED COMMENT '资源id的Bitmap索引',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户id的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`resource_service_bill_id`)
COMMENT '后付费-成本-按资源成本-服务详细-账单表 ${year}'
DISTRIBUTED BY HASH(`resource_service_bill_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);